#!/usr/bin/env python3
"""
Zara Voice Assistant GUI
Simple GUI launcher for Zara Voice Assistant

Creator: Ratnam Sanjay
Version: 2.0.0
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import asyncio
import threading
import json
from datetime import datetime
import sqlite3

# Import Zara components
from zara_assistant import ZaraAssistant, ZaraDatabase

class ZaraGUI:
    """Simple GUI for Zara Voice Assistant"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Zara Voice Assistant - Created by <PERSON><PERSON> Sanjay")
        self.root.geometry("800x600")
        self.root.configure(bg='#2c3e50')
        
        # Initialize assistant
        self.assistant = None
        self.is_running = False
        
        self.setup_ui()
        self.load_conversation_history()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Title
        title_frame = tk.Frame(self.root, bg='#2c3e50')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(
            title_frame,
            text="🤖 Zara Voice Assistant",
            font=('Arial', 18, 'bold'),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        title_label.pack(side='left')
        
        creator_label = tk.Label(
            title_frame,
            text="Created by: Ratnam Sanjay",
            font=('Arial', 10),
            fg='#bdc3c7',
            bg='#2c3e50'
        )
        creator_label.pack(side='right')
        
        # Status frame
        status_frame = tk.Frame(self.root, bg='#34495e')
        status_frame.pack(fill='x', padx=10, pady=5)
        
        self.status_label = tk.Label(
            status_frame,
            text="🔴 Offline",
            font=('Arial', 12),
            fg='#e74c3c',
            bg='#34495e'
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # Control buttons
        button_frame = tk.Frame(status_frame, bg='#34495e')
        button_frame.pack(side='right', padx=10, pady=5)
        
        self.start_button = tk.Button(
            button_frame,
            text="Start Zara",
            command=self.start_assistant,
            bg='#27ae60',
            fg='white',
            font=('Arial', 10, 'bold'),
            padx=20
        )
        self.start_button.pack(side='left', padx=5)
        
        self.stop_button = tk.Button(
            button_frame,
            text="Stop",
            command=self.stop_assistant,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 10, 'bold'),
            padx=20,
            state='disabled'
        )
        self.stop_button.pack(side='left', padx=5)
        
        # Main content area
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Conversation display
        conv_frame = tk.LabelFrame(
            main_frame,
            text="Conversation History",
            font=('Arial', 12, 'bold'),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        conv_frame.pack(fill='both', expand=True, pady=5)
        
        self.conversation_text = scrolledtext.ScrolledText(
            conv_frame,
            wrap=tk.WORD,
            font=('Consolas', 10),
            bg='#34495e',
            fg='#ecf0f1',
            insertbackground='#ecf0f1'
        )
        self.conversation_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Input area
        input_frame = tk.Frame(main_frame, bg='#2c3e50')
        input_frame.pack(fill='x', pady=5)
        
        tk.Label(
            input_frame,
            text="Message:",
            font=('Arial', 10, 'bold'),
            fg='#ecf0f1',
            bg='#2c3e50'
        ).pack(side='left', padx=5)
        
        self.input_entry = tk.Entry(
            input_frame,
            font=('Arial', 12),
            bg='#34495e',
            fg='#ecf0f1',
            insertbackground='#ecf0f1'
        )
        self.input_entry.pack(side='left', fill='x', expand=True, padx=5)
        self.input_entry.bind('<Return>', self.send_message)
        
        self.send_button = tk.Button(
            input_frame,
            text="Send",
            command=self.send_message,
            bg='#3498db',
            fg='white',
            font=('Arial', 10, 'bold'),
            padx=15
        )
        self.send_button.pack(side='right', padx=5)
        
        # Tools frame
        tools_frame = tk.LabelFrame(
            main_frame,
            text="Available Tools",
            font=('Arial', 10, 'bold'),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        tools_frame.pack(fill='x', pady=5)
        
        self.tools_text = tk.Text(
            tools_frame,
            height=4,
            font=('Consolas', 9),
            bg='#34495e',
            fg='#bdc3c7',
            state='disabled'
        )
        self.tools_text.pack(fill='x', padx=10, pady=5)
    
    def start_assistant(self):
        """Start the Zara assistant"""
        try:
            self.assistant = ZaraAssistant()
            self.is_running = True
            
            # Update UI
            self.status_label.config(text="🟢 Online", fg='#27ae60')
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            
            # Display tools
            self.update_tools_display()
            
            # Add startup message
            startup_msg = "नमस्कार सर, मैं ज़ारा आपकी सेवा में प्रस्तुत हूँ। आज का कार्य प्रारंभ करें?"
            self.add_message("Zara", startup_msg)
            
            messagebox.showinfo("Success", "Zara Assistant started successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start Zara: {str(e)}")
    
    def stop_assistant(self):
        """Stop the Zara assistant"""
        self.is_running = False
        self.assistant = None
        
        # Update UI
        self.status_label.config(text="🔴 Offline", fg='#e74c3c')
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        
        self.add_message("System", "Zara Assistant stopped")
    
    def send_message(self, event=None):
        """Send message to Zara"""
        if not self.is_running or not self.assistant:
            messagebox.showwarning("Warning", "Please start Zara first!")
            return
        
        message = self.input_entry.get().strip()
        if not message:
            return
        
        # Clear input
        self.input_entry.delete(0, tk.END)
        
        # Add user message
        self.add_message("You", message)
        
        # Log to database
        self.assistant._log_conversation("User", message)
        
        # Simple response (without full LLM integration)
        response = f"मैंने समझा कि आपने कहा: '{message}'. मैं अभी भी सीख रही हूँ।"
        
        # Add assistant response
        self.add_message("Zara", response)
        
        # Log to database
        self.assistant._log_conversation("Assistant", response)
    
    def add_message(self, sender: str, message: str):
        """Add message to conversation display"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        self.conversation_text.config(state='normal')
        
        # Color coding
        if sender == "You":
            color = '#3498db'
        elif sender == "Zara":
            color = '#27ae60'
        else:
            color = '#f39c12'
        
        self.conversation_text.insert(tk.END, f"[{timestamp}] {sender}: ", ('sender',))
        self.conversation_text.insert(tk.END, f"{message}\n\n")
        
        # Configure tags
        self.conversation_text.tag_config('sender', foreground=color, font=('Arial', 10, 'bold'))
        
        self.conversation_text.config(state='disabled')
        self.conversation_text.see(tk.END)
    
    def update_tools_display(self):
        """Update the tools display"""
        if not self.assistant:
            return
        
        tools_list = list(self.assistant._tools.keys())
        tools_text = f"Tools loaded: {len(tools_list)}\n"
        tools_text += ", ".join(tools_list[:10])  # Show first 10 tools
        if len(tools_list) > 10:
            tools_text += f" ... and {len(tools_list) - 10} more"
        
        self.tools_text.config(state='normal')
        self.tools_text.delete(1.0, tk.END)
        self.tools_text.insert(1.0, tools_text)
        self.tools_text.config(state='disabled')
    
    def load_conversation_history(self):
        """Load recent conversation history"""
        try:
            ZaraDatabase.init_db()
            messages = ZaraDatabase.get_last_n_messages(5)
            
            if messages:
                self.add_message("System", "Loading recent conversation history...")
                for msg in messages:
                    timestamp = datetime.fromtimestamp(msg[2]).strftime('%H:%M:%S')
                    self.conversation_text.config(state='normal')
                    self.conversation_text.insert(tk.END, f"[{timestamp}] {msg[0]}: {msg[1]}\n")
                    self.conversation_text.config(state='disabled')
        except Exception as e:
            print(f"Failed to load conversation history: {e}")
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

def main():
    """Main function"""
    app = ZaraGUI()
    app.run()

if __name__ == "__main__":
    main()
