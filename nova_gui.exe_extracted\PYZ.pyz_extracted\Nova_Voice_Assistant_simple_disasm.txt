Simple disassembly of nova_gui.exe_extracted\PYZ.pyz_extracted\Nova_Voice_Assistant.pyc
============================================================

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 (0)
              4 LOAD_CONST               1 (None)
              6 IMPORT_NAME              0 (asyncio)
              8 STORE_NAME               0 (asyncio)

  2          10 LOAD_CONST               0 (0)
             12 LOAD_CONST               2 (('rtc',))
             14 IMPORT_NAME              1 (livekit)
             16 IMPORT_FROM              2 (rtc)
             18 STORE_NAME               2 (rtc)
             20 POP_TOP

  3          22 LOAD_CONST               0 (0)
             24 LOAD_CONST               3 (('images',))
             26 IMPORT_NAME              3 (livekit.agents.utils)
             28 IMPORT_FROM              4 (images)
             30 STORE_NAME               4 (images)
             32 POP_TOP

  4          34 LOAD_CONST               0 (0)
             36 LOAD_CONST               1 (None)
             38 IMPORT_NAME              0 (asyncio)
             40 STORE_NAME               0 (asyncio)

  5          42 LOAD_CONST               0 (0)
             44 LOAD_CONST               4 (('VideoBufferType',))
             46 IMPORT_NAME              5 (livekit.rtc)
             48 IMPORT_FROM              6 (VideoBufferType)
             50 STORE_NAME               6 (VideoBufferType)
             52 POP_TOP

  6          54 LOAD_CONST               0 (0)
             56 LOAD_CONST               5 (('Any', 'List', 'Optional'))
             58 IMPORT_NAME              7 (typing)
             60 IMPORT_FROM              8 (Any)
             62 STORE_NAME               8 (Any)
             64 IMPORT_FROM              9 (List)
             66 STORE_NAME               9 (List)
             68 IMPORT_FROM             10 (Optional)
             70 STORE_NAME              10 (Optional)
             72 POP_TOP

  7          74 LOAD_CONST               0 (0)
             76 LOAD_CONST               6 (('load_dotenv',))
             78 IMPORT_NAME             11 (dotenv)
             80 IMPORT_FROM             12 (load_dotenv)
             82 STORE_NAME              12 (load_dotenv)
             84 POP_TOP

  8          86 LOAD_CONST               0 (0)
             88 LOAD_CONST               7 (('datetime',))
             90 IMPORT_NAME             13 (datetime)
             92 IMPORT_FROM             13 (datetime)
             94 STORE_NAME              13 (datetime)
             96 POP_TOP

  9          98 LOAD_CONST               0 (0)
            100 LOAD_CONST               8 (('agents',))
            102 IMPORT_NAME              1 (livekit)
            104 IMPORT_FROM             14 (agents)
            106 STORE_NAME              14 (agents)
            108 POP_TOP

 10         110 LOAD_CONST               0 (0)
            112 LOAD_CONST               9 (('AgentSession', 'Agent', 'RoomInputOptions'))
            114 IMPORT_NAME             15 (livekit.agents)
            116 IMPORT_FROM             16 (AgentSession)
            118 STORE_NAME              16 (AgentSession)
            120 IMPORT_FROM             17 (Agent)
            122 STORE_NAME              17 (Agent)
            124 IMPORT_FROM             18 (RoomInputOptions)
            126 STORE_NAME              18 (RoomInputOptions)
            128 POP_TOP

 11         130 LOAD_CONST               0 (0)
            132 LOAD_CONST              10 (('noise_cancellation',))
            134 IMPORT_NAME             19 (livekit.plugins)
            136 IMPORT_FROM             20 (noise_cancellation)
            138 STORE_NAME              20 (noise_cancellation)
            140 POP_TOP

 12         142 LOAD_CONST               0 (0)
            144 LOAD_CONST              11 (('google',))
            146 IMPORT_NAME             19 (livekit.plugins)
            148 IMPORT_FROM             21 (google)
            150 STORE_NAME              21 (google)
            152 POP_TOP

 13         154 LOAD_CONST               0 (0)
            156 LOAD_CONST              12 (('ChatContext',))
            158 IMPORT_NAME             22 (livekit.agents.llm.chat_context)
            160 IMPORT_FROM             23 (ChatContext)
            162 STORE_NAME              23 (ChatContext)
            164 POP_TOP

 14         166 LOAD_CONST               0 (0)
            168 LOAD_CONST               1 (None)
            170 IMPORT_NAME             24 (numpy)
            172 STORE_NAME              25 (np)

 15         174 LOAD_CONST               0 (0)
            176 LOAD_CONST               1 (None)
            178 IMPORT_NAME             26 (cv2)
            180 STORE_NAME              26 (cv2)

 16         182 LOAD_CONST               0 (0)
            184 LOAD_CONST              13 (('deque',))
            186 IMPORT_NAME             27 (collections)
            188 IMPORT_FROM             28 (deque)
            190 STORE_NAME              28 (deque)
            192 POP_TOP

 17         194 LOAD_CONST               0 (0)
            196 LOAD_CONST               1 (None)
            198 IMPORT_NAME             29 (json)
            200 STORE_NAME              29 (json)

 20         202 LOAD_CONST               0 (0)
            204 LOAD_CONST              14 (('AGENT_INSTRUCTION', 'SESSION_INSTRUCTION', 'AGENT_INSTRUCTION_FOR_TOOLS'))
            206 IMPORT_NAME             30 (prompts)
            208 IMPORT_FROM             31 (AGENT_INSTRUCTION)
            210 STORE_NAME              31 (AGENT_INSTRUCTION)
            212 IMPORT_FROM             32 (SESSION_INSTRUCTION)
            214 STORE_NAME              32 (SESSION_INSTRUCTION)
            216 IMPORT_FROM             33 (AGENT_INSTRUCTION_FOR_TOOLS)
            218 STORE_NAME              33 (AGENT_INSTRUCTION_FOR_TOOLS)
            220 POP_TOP

 26         222 LOAD_CONST               0 (0)
            224 LOAD_CONST              15 (('get_weather', 'search_web', 'play_media', 'get_time_info', 'system_power_action', 'manage_window', 'desktop_control', 'list_active_windows', 'manage_window_state', 'get_today_reminder_message_from_db', 'say_reminder', 'send_whatsapp_message', 'write_in_notepad', 'open_app', 'press_key', 'get_system_info', 'type_user_message_auto', 'scan_system_for_viruses', 'get_analysis_report', 'get_analysis_status', 'get_top_insights', 'get_data_summary', 'export_results', 'full_analysis_with_report', 'create_quick_advanced_graph', 'advanced_network_scan'))
            226 IMPORT_NAME             34 (tools)
            228 IMPORT_FROM             35 (get_weather)
            230 STORE_NAME              35 (get_weather)
            232 IMPORT_FROM             36 (search_web)
            234 STORE_NAME              36 (search_web)
            236 IMPORT_FROM             37 (play_media)
            238 STORE_NAME              37 (play_media)
            240 IMPORT_FROM             38 (get_time_info)
            242 STORE_NAME              38 (get_time_info)
            244 IMPORT_FROM             39 (system_power_action)
            246 STORE_NAME              39 (system_power_action)
            248 IMPORT_FROM             40 (manage_window)
            250 STORE_NAME              40 (manage_window)
            252 IMPORT_FROM             41 (desktop_control)
            254 STORE_NAME              41 (desktop_control)
            256 IMPORT_FROM             42 (list_active_windows)
            258 STORE_NAME              42 (list_active_windows)
            260 IMPORT_FROM             43 (manage_window_state)
            262 STORE_NAME              43 (manage_window_state)
            264 IMPORT_FROM             44 (get_today_reminder_message_from_db)
            266 STORE_NAME              44 (get_today_reminder_message_from_db)
            268 IMPORT_FROM             45 (say_reminder)
            270 STORE_NAME              45 (say_reminder)
            272 IMPORT_FROM             46 (send_whatsapp_message)
            274 STORE_NAME              46 (send_whatsapp_message)
            276 IMPORT_FROM             47 (write_in_notepad)
            278 STORE_NAME              47 (write_in_notepad)
            280 IMPORT_FROM             48 (open_app)
            282 STORE_NAME              48 (open_app)
            284 IMPORT_FROM             49 (press_key)
            286 STORE_NAME              49 (press_key)
            288 IMPORT_FROM             50 (get_system_info)
            290 STORE_NAME              50 (get_system_info)
            292 IMPORT_FROM             51 (type_user_message_auto)
            294 STORE_NAME              51 (type_user_message_auto)
            296 IMPORT_FROM             52 (scan_system_for_viruses)
            298 STORE_NAME              52 (scan_system_for_viruses)
            300 IMPORT_FROM             53 (get_analysis_report)
            302 STORE_NAME              53 (get_analysis_report)
            304 IMPORT_FROM             54 (get_analysis_status)
            306 STORE_NAME              54 (get_analysis_status)
            308 IMPORT_FROM             55 (get_top_insights)
            310 STORE_NAME              55 (get_top_insights)
            312 IMPORT_FROM             56 (get_data_summary)
            314 STORE_NAME              56 (get_data_summary)
            316 IMPORT_FROM             57 (export_results)
            318 STORE_NAME              57 (export_results)
            320 IMPORT_FROM             58 (full_analysis_with_report)
            322 STORE_NAME              58 (full_analysis_with_report)
            324 IMPORT_FROM             59 (create_quick_advanced_graph)
            326 STORE_NAME              59 (create_quick_advanced_graph)
            328 IMPORT_FROM             60 (advanced_network_scan)
            330 STORE_NAME              60 (advanced_network_scan)
            332 POP_TOP

 56         334 NOP

 57         336 LOAD_CONST               0 (0)
            338 LOAD_CONST              16 (('EffectPlugin',))
            340 IMPORT_NAME             61 (livekit.plugins.google.beta.realtime.custom_plugins)
            342 IMPORT_FROM             62 (EffectPlugin)
            344 STORE_NAME              62 (EffectPlugin)
            346 POP_TOP
            348 JUMP_FORWARD            11 (to 372)
        >>  350 PUSH_EXC_INFO

 58         352 LOAD_NAME               63 (ImportError)
            354 CHECK_EXC_MATCH
            356 POP_JUMP_IF_FALSE        3 (to 364)
            358 POP_TOP

 59         360 POP_EXCEPT
            362 JUMP_FORWARD             4 (to 372)

 58     >>  364 RERAISE                  0
        >>  366 COPY                     3
            368 POP_EXCEPT
            370 RERAISE                  1

 61     >>  372 LOAD_CONST               0 (0)
            374 LOAD_CONST               1 (None)
            376 IMPORT_NAME             64 (time)
            378 STORE_NAME              64 (time)

 62         380 LOAD_CONST               0 (0)
            382 LOAD_CONST               1 (None)
            384 IMPORT_NAME             65 (logging)
            386 STORE_NAME              65 (logging)

 63         388 LOAD_CONST               0 (0)
            390 LOAD_CONST              17 (('RotatingFileHandler',))
            392 IMPORT_NAME             66 (logging.handlers)
            394 IMPORT_FROM             67 (RotatingFileHandler)
            396 STORE_NAME              67 (RotatingFileHandler)
            398 POP_TOP

 66         400 PUSH_NULL
            402 LOAD_NAME               12 (load_dotenv)
            404 UNPACK_SEQUENCE          0
            408 CALL                     0
            416 CACHE
            418 POP_TOP

 68         420 PUSH_NULL
            422 LOAD_BUILD_CLASS
            424 LOAD_CONST              18 (<code object Assistant at 0x0000024C7AA5FC50, file "Nova_Voice_Assistant.py", line 68>)
            426 MAKE_FUNCTION            0
            428 LOAD_CONST              19 ('Assistant')
            430 LOAD_NAME               17 (Agent)
            432 UNPACK_SEQUENCE          3
            436 CALL                     3
            444 CACHE
            446 STORE_NAME              68 (Assistant)

408         448 LOAD_CONST              20 ('ctx')
            450 LOAD_NAME               14 (agents)
            452 LOAD_ATTR               69 (NULL|self + tools)
            472 LOAD_CONST              22 ('__main__')
            474 COMPARE_OP               2 (<)
            478 CACHE
            480 POP_JUMP_IF_FALSE       43 (to 568)

473         482 LOAD_NAME               14 (agents)
            484 LOAD_ATTR               72 (search_web)
            504 CACHE
            506 CACHE
            508 CACHE
            510 CACHE
            512 CACHE
            514 CACHE
            516 PUSH_NULL
            518 LOAD_NAME               14 (agents)
            520 LOAD_ATTR               74 (play_media)
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 UNPACK_SEQUENCE          1
            552 CALL                     1
            560 CACHE
            562 POP_TOP
            564 LOAD_CONST               1 (None)
            566 RETURN_VALUE

472     >>  568 LOAD_CONST               1 (None)
            570 RETURN_VALUE
ExceptionTable:
  336 to 346 -> 350 [0]
  350 to 358 -> 366 [1] lasti
  364 to 364 -> 366 [1] lasti

Disassembly of <code object Assistant at 0x0000024C7AA5FC50, file "Nova_Voice_Assistant.py", line 68>:
              0 MAKE_CELL                0 (__class__)

 68           2 RESUME                   0
              4 LOAD_NAME                0 (__name__)
              6 STORE_NAME               1 (__module__)
              8 LOAD_CONST               0 ('Assistant')
             10 STORE_NAME               2 (__qualname__)

 69          12 LOAD_CONST              23 (('return', None))
             14 LOAD_CLOSURE             0 (__class__)
             16 BUILD_TUPLE              1
             18 LOAD_CONST               3 (<code object __init__ at 0x0000024C7BBCBC00, file "Nova_Voice_Assistant.py", line 69>)
             20 MAKE_FUNCTION           12 (annotations, closure)
             22 STORE_NAME               3 (__init__)

130          24 LOAD_CONST               4 ('enable')
             26 LOAD_NAME                4 (bool)
             28 BUILD_TUPLE              2
             30 LOAD_CONST               5 (<code object enable_visual_analysis at 0x0000024C7BB85050, file "Nova_Voice_Assistant.py", line 130>)
             32 MAKE_FUNCTION            4 (annotations)
             34 STORE_NAME               5 (enable_visual_analysis)

143          36 LOAD_CONST               6 ('frame')
             38 LOAD_NAME                6 (rtc)
             40 LOAD_ATTR                7 (NULL|self + __init__)
             60 LOAD_NAME                9 (str)
             62 LOAD_CONST               1 ('return')
             64 LOAD_NAME                9 (str)
             66 BUILD_TUPLE              4
             68 LOAD_CONST               9 (<code object analyze_current_scene at 0x0000024C7BACC2A0, file "Nova_Voice_Assistant.py", line 171>)
             70 MAKE_FUNCTION            4 (annotations)
             72 STORE_NAME              10 (analyze_current_scene)

236          74 LOAD_CONST              10 (<code object _trigger_gui_effect at 0x0000024C7ADB5CA0, file "Nova_Voice_Assistant.py", line 236>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME              11 (_trigger_gui_effect)

240          80 LOAD_CONST              11 ('tools')
             82 LOAD_NAME               12 (List)
             84 LOAD_NAME               13 (Any)
             86 BINARY_SUBSCR
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_CONST               1 ('return')
             98 LOAD_NAME               12 (List)
            100 LOAD_NAME               13 (Any)
            102 BINARY_SUBSCR
            106 CACHE
            108 CACHE
            110 CACHE
            112 BUILD_TUPLE              4
            114 LOAD_CONST              12 (<code object _initialize_tools at 0x0000024C7BB09690, file "Nova_Voice_Assistant.py", line 240>)
            116 MAKE_FUNCTION            4 (annotations)
            118 STORE_NAME              14 (_initialize_tools)

266         120 LOAD_CONST               1 ('return')
            122 LOAD_NAME                9 (str)
            124 BUILD_TUPLE              2
            126 LOAD_CONST              13 (<code object _build_instructions at 0x0000024C7AAE2120, file "Nova_Voice_Assistant.py", line 266>)
            128 MAKE_FUNCTION            4 (annotations)
            130 STORE_NAME              15 (_build_instructions)

285         132 LOAD_CLOSURE             0 (__class__)
            134 BUILD_TUPLE              1
            136 LOAD_CONST              14 (<code object on_tool_call_start at 0x0000024C7AB1FB20, file "Nova_Voice_Assistant.py", line 285>)
            138 MAKE_FUNCTION            8 (closure)
            140 STORE_NAME              16 (on_tool_call_start)

291         142 LOAD_CLOSURE             0 (__class__)
            144 BUILD_TUPLE              1
            146 LOAD_CONST              15 (<code object on_tool_call_end at 0x0000024C7AA3D5F0, file "Nova_Voice_Assistant.py", line 291>)
            148 MAKE_FUNCTION            8 (closure)
            150 STORE_NAME              17 (on_tool_call_end)

302         152 LOAD_CLOSURE             0 (__class__)
            154 BUILD_TUPLE              1
            156 LOAD_CONST              16 (<code object on_user_turn_completed at 0x0000024C7BB6F7F0, file "Nova_Voice_Assistant.py", line 302>)
            158 MAKE_FUNCTION            8 (closure)
            160 STORE_NAME              18 (on_user_turn_completed)

323         162 LOAD_CONST              17 ('sender')
            164 LOAD_NAME                9 (str)
            166 LOAD_CONST              18 ('message')
            168 LOAD_NAME                9 (str)
            170 LOAD_CONST               1 ('return')
            172 LOAD_CONST               2 (None)
            174 BUILD_TUPLE              6
            176 LOAD_CONST              19 (<code object _log_conversation at 0x0000024C7B1861D0, file "Nova_Voice_Assistant.py", line 323>)
            178 MAKE_FUNCTION            4 (annotations)
            180 STORE_NAME              19 (_log_conversation)

332         182 LOAD_CONST              23 (('return', None))
            184 LOAD_CONST              20 (<code object _check_reminders at 0x0000024C7AA5F770, file "Nova_Voice_Assistant.py", line 332>)
            186 MAKE_FUNCTION            4 (annotations)
            188 STORE_NAME              20 (_check_reminders)

347         190 LOAD_CONST              21 ('session')
            192 LOAD_NAME               21 (AgentSession)
            194 BUILD_TUPLE              2
            196 LOAD_CONST              22 (<code object generate_startup_message at 0x0000024C7BBE9960, file "Nova_Voice_Assistant.py", line 347>)
            198 MAKE_FUNCTION            4 (annotations)
            200 STORE_NAME              22 (generate_startup_message)
            202 LOAD_CLOSURE             0 (__class__)
            204 COPY                     1
            206 STORE_NAME              23 (__classcell__)
            208 RETURN_VALUE

Disassembly of <code object __init__ at 0x0000024C7BBCBC00, file "Nova_Voice_Assistant.py", line 69>:
              0 COPY_FREE_VARS           1

 69           2 RESUME                   0

 71           4 LOAD_CONST               1 (0)
              6 LOAD_CONST               0 (None)
              8 IMPORT_NAME              0 (tools)
             10 STORE_FAST               1 (tools)

 72          12 LOAD_FAST                0 (self)
             14 LOAD_FAST                1 (tools)
             16 STORE_ATTR               1 (assistant_instance)

 73          26 LOAD_FAST                0 (self)
             28 STORE_SUBSCR
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE

 74          50 LOAD_GLOBAL              6 (get_weather)
             60 CACHE

 75          62 LOAD_GLOBAL              8 (search_web)
             72 CACHE

 76          74 LOAD_GLOBAL             10 (play_media)
             84 CACHE

 77          86 LOAD_GLOBAL             12 (get_time_info)
             96 CACHE

 78          98 LOAD_GLOBAL             14 (system_power_action)
            108 CACHE

 79         110 LOAD_GLOBAL             16 (manage_window)
            120 CACHE

 80         122 LOAD_GLOBAL             18 (desktop_control)
            132 CACHE

 81         134 LOAD_GLOBAL             20 (list_active_windows)
            144 CACHE

 82         146 LOAD_GLOBAL             22 (manage_window_state)
            156 CACHE

 83         158 LOAD_GLOBAL             24 (send_whatsapp_message)
            168 CACHE

 84         170 LOAD_GLOBAL             26 (write_in_notepad)
            180 CACHE

 85         182 LOAD_GLOBAL             28 (open_app)
            192 CACHE

 86         194 LOAD_GLOBAL             30 (press_key)
            204 CACHE

 87         206 LOAD_GLOBAL             32 (get_system_info)
            216 CACHE

 88         218 LOAD_GLOBAL             34 (type_user_message_auto)
            228 CACHE

 89         230 LOAD_GLOBAL             36 (scan_system_for_viruses)
            240 CACHE

 90         242 LOAD_GLOBAL             38 (get_analysis_status)
            252 CACHE

 91         254 LOAD_GLOBAL             40 (get_analysis_report)
            264 CACHE

 92         266 LOAD_GLOBAL             42 (get_data_summary)
            276 CACHE

 93         278 LOAD_GLOBAL             44 (get_top_insights)
            288 CACHE

 94         290 LOAD_GLOBAL             46 (full_analysis_with_report)
            300 CACHE

 95         302 LOAD_GLOBAL             48 (export_results)
            312 CACHE

 96         314 LOAD_GLOBAL             50 (create_quick_advanced_graph)
            324 CACHE

 73         326 BUILD_LIST              23
            328 UNPACK_SEQUENCE          1
            332 CALL                     1
            340 CACHE
            342 LOAD_FAST                0 (self)
            344 STORE_ATTR              26 (_tools)

102         354 LOAD_GLOBAL             55 (NULL + super)
            364 CACHE
            366 UNPACK_SEQUENCE          0
            370 CALL                     0
            378 CACHE
            380 STORE_SUBSCR
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE

103         402 LOAD_FAST                0 (self)
            404 STORE_SUBSCR
            408 CACHE
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 CACHE
            422 CACHE
            424 CACHE
            426 UNPACK_SEQUENCE          0
            430 CALL                     0
            438 CACHE

104         440 LOAD_GLOBAL             60 (google)
            450 CACHE
            452 LOAD_ATTR               31 (NULL|self + press_key)
            472 STORE_SUBSCR
            476 CACHE
            478 CACHE
            480 CACHE
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE

105         494 LOAD_CONST               2 ('Charon')

106         496 LOAD_CONST               3 (0.5)

107         498 LOAD_CONST               4 (0.9)

104         500 KW_NAMES                 5 (('voice', 'temperature', 'top_p'))
            502 UNPACK_SEQUENCE          3
            506 CALL                     3
            514 CACHE

110         516 LOAD_FAST                0 (self)
            518 LOAD_ATTR               26 (write_in_notepad)
            538 CACHE
            540 CACHE
            542 CACHE
            544 POP_TOP

114         546 LOAD_CONST               0 (None)
            548 LOAD_FAST                0 (self)
            550 STORE_ATTR              34 (_last_tool_used)

115         560 LOAD_CONST               7 (False)
            562 LOAD_FAST                0 (self)
            564 STORE_ATTR              35 (_last_tool_success)

116         574 LOAD_CONST               8 ('chat_log.txt')
            576 LOAD_FAST                0 (self)
            578 STORE_ATTR              36 (_chat_log_path)

117         588 LOAD_GLOBAL             75 (NULL + EffectPlugin)
            598 CACHE
            600 LOAD_ATTR               38 (get_analysis_status)
            620 CACHE
            622 UNPACK_SEQUENCE          1
            626 CALL                     1
            634 CACHE
            636 POP_TOP

118         638 LOAD_CONST               3 (0.5)
            640 LOAD_FAST                0 (self)
            642 STORE_ATTR              40 (_min_frame_interval)

119         652 LOAD_CONST               9 (20)
            654 LOAD_FAST                0 (self)
            656 STORE_ATTR              41 (_max_buffer_size)

120         666 LOAD_CONST              10 (3)
            668 LOAD_FAST                0 (self)
            670 STORE_ATTR              42 (_max_frames_to_send)

123         680 LOAD_GLOBAL             87 (NULL + deque)
            690 CACHE
            692 LOAD_FAST                0 (self)
            694 LOAD_ATTR               41 (NULL|self + get_analysis_report)
            714 CACHE
            716 CACHE
            718 CACHE
            720 LOAD_FAST                0 (self)
            722 STORE_ATTR              44 (_frame_buffer)

124         732 LOAD_CONST               1 (0)
            734 LOAD_FAST                0 (self)
            736 STORE_ATTR              45 (_last_frame_time)

125         746 LOAD_CONST               7 (False)
            748 LOAD_FAST                0 (self)
            750 STORE_ATTR              46 (_visual_analysis_enabled)

126         760 LOAD_CONST               7 (False)
            762 LOAD_FAST                0 (self)
            764 STORE_ATTR              47 (_is_analyzing)

127         774 LOAD_GLOBAL             97 (NULL + asyncio)
            784 CACHE
            786 LOAD_ATTR               49 (NULL|self + export_results)
            806 CACHE
            808 CACHE
            810 LOAD_FAST                0 (self)
            812 STORE_ATTR              50 (_analysis_lock)
            822 LOAD_CONST               0 (None)
            824 RETURN_VALUE

Disassembly of <code object enable_visual_analysis at 0x0000024C7BB85050, file "Nova_Voice_Assistant.py", line 130>:
130           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

132           6 LOAD_FAST                1 (enable)
              8 LOAD_FAST                0 (self)
             10 STORE_ATTR               0 (_visual_analysis_enabled)

133          20 LOAD_FAST                1 (enable)
             22 POP_JUMP_IF_FALSE       34 (to 92)

134          24 LOAD_FAST                0 (self)
             26 LOAD_ATTR                1 (NULL|self + _visual_analysis_enabled)
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 UNPACK_SEQUENCE          0
             62 CALL                     0
             70 CACHE
             72 POP_TOP

135          74 LOAD_CONST               1 (0)
             76 LOAD_FAST                0 (self)
             78 STORE_ATTR               3 (_last_frame_time)

136          88 LOAD_CONST               2 ('कैमरा विश्लेषण सक्रिय किया गया')
             90 RETURN_VALUE

138     >>   92 LOAD_FAST                0 (self)
             94 LOAD_ATTR                1 (NULL|self + _visual_analysis_enabled)
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 UNPACK_SEQUENCE          0
            130 CALL                     0
            138 CACHE
            140 POP_TOP

139         142 LOAD_GLOBAL              9 (NULL + hasattr)
            152 CACHE
            154 LOAD_FAST                0 (self)
            156 LOAD_ATTR                5 (NULL|self + clear)
            176 CACHE
            178 CACHE
            180 CACHE
            182 POP_JUMP_IF_FALSE       68 (to 320)
            184 LOAD_FAST                0 (self)
            186 LOAD_ATTR                5 (NULL|self + clear)
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 UNPACK_SEQUENCE          0
            222 CALL                     0
            230 CACHE
            232 POP_JUMP_IF_FALSE       43 (to 320)

140         234 LOAD_FAST                0 (self)
            236 LOAD_ATTR                5 (NULL|self + clear)
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 UNPACK_SEQUENCE          0
            272 CALL                     0
            280 CACHE
            282 STORE_SUBSCR
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 UNPACK_SEQUENCE          0
            308 CALL                     0
            316 CACHE
            318 POP_TOP

141     >>  320 LOAD_CONST               4 ('कैमरा विश्लेषण निष्क्रिय किया गया')
            322 RETURN_VALUE

Disassembly of <code object process_visual_frame at 0x0000024C7BB0A480, file "Nova_Voice_Assistant.py", line 143>:
143           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

148           6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (_visual_analysis_enabled)
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_ATTR                1 (NULL|self + _visual_analysis_enabled)
             56 CACHE
             58 CACHE
             60 STORE_FAST               2 (current_time)

153          62 LOAD_FAST                2 (current_time)
             64 LOAD_FAST                0 (self)
             66 LOAD_ATTR                2 (time)
             86 CACHE
             88 CACHE
             90 CACHE
             92 COMPARE_OP               0 (<)
             96 CACHE
             98 POP_JUMP_IF_FALSE        2 (to 104)

154         100 LOAD_CONST               1 (None)
            102 RETURN_VALUE

156     >>  104 NOP

157         106 LOAD_FAST                1 (frame)
            108 LOAD_ATTR                4 (_last_frame_time)
            128 CACHE
            130 LOAD_ATTR                6 (_min_frame_interval)
            150 STORE_SUBSCR
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 LOAD_GLOBAL             10 (VideoBufferType)
            182 CACHE
            184 LOAD_ATTR                6 (_min_frame_interval)
            204 CACHE
            206 CACHE
            208 STORE_FAST               1 (frame)

161         210 LOAD_FAST                2 (current_time)
            212 LOAD_FAST                1 (frame)
            214 STORE_ATTR               8 (timestamp)

162         224 LOAD_FAST                0 (self)
            226 LOAD_ATTR                9 (NULL|self + type)
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 LOAD_FAST                1 (frame)
            260 UNPACK_SEQUENCE          1
            264 CALL                     1
            272 CACHE
            274 POP_TOP

163         276 LOAD_FAST                2 (current_time)
            278 LOAD_FAST                0 (self)
            280 STORE_ATTR               2 (_last_frame_time)
            290 LOAD_CONST               1 (None)
            292 RETURN_VALUE
        >>  294 PUSH_EXC_INFO

165         296 LOAD_GLOBAL             22 (Exception)
            306 CACHE
            308 CHECK_EXC_MATCH
            310 POP_JUMP_IF_FALSE       95 (to 502)
            312 STORE_FAST               3 (e)

167         314 LOAD_GLOBAL             25 (NULL + getattr)
            324 CACHE
            326 LOAD_FAST                0 (self)
            328 LOAD_CONST               2 ('logger')
            330 LOAD_GLOBAL             26 (logging)
            340 CACHE
            342 UNPACK_SEQUENCE          3
            346 CALL                     3
            354 CACHE
            356 STORE_FAST               4 (logger)

168         358 LOAD_FAST                4 (logger)
            360 STORE_SUBSCR
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 CACHE
            382 LOAD_CONST               3 ('Frame processing error: ')
            384 LOAD_GLOBAL             31 (NULL + str)
            394 CACHE
            396 LOAD_FAST                3 (e)
            398 UNPACK_SEQUENCE          1
            402 CALL                     1
            410 CACHE
            412 FORMAT_VALUE             0
            414 BUILD_STRING             2
            416 UNPACK_SEQUENCE          1
            420 CALL                     1
            428 CACHE
            430 POP_TOP

169         432 LOAD_FAST                0 (self)
            434 LOAD_ATTR                9 (NULL|self + type)
            454 CACHE
            456 CACHE
            458 CACHE
            460 CACHE
            462 CACHE
            464 CACHE
            466 UNPACK_SEQUENCE          0
            470 CALL                     0
            478 CACHE
            480 POP_TOP
            482 POP_EXCEPT
            484 LOAD_CONST               1 (None)
            486 STORE_FAST               3 (e)
            488 DELETE_FAST              3 (e)
            490 LOAD_CONST               1 (None)
            492 RETURN_VALUE
        >>  494 LOAD_CONST               1 (None)
            496 STORE_FAST               3 (e)
            498 DELETE_FAST              3 (e)
            500 RERAISE                  1

165     >>  502 RERAISE                  0
        >>  504 COPY                     3
            506 POP_EXCEPT
            508 RERAISE                  1
ExceptionTable:
  106 to 288 -> 294 [0]
  294 to 312 -> 504 [1] lasti
  314 to 480 -> 494 [1] lasti
  494 to 502 -> 504 [1] lasti

Disassembly of <code object analyze_current_scene at 0x0000024C7BACC2A0, file "Nova_Voice_Assistant.py", line 171>:
              0 MAKE_CELL                0 (self)

171           2 RETURN_GENERATOR
              4 POP_TOP
              6 RESUME                   0

175           8 LOAD_DEREF               0 (self)
             10 LOAD_ATTR                0 (_visual_analysis_enabled)
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 POP_JUMP_IF_TRUE         2 (to 44)

179          40 LOAD_CONST               2 ('कोई ताजा फ्रेम उपलब्ध नहीं')
             42 RETURN_VALUE

181     >>   44 LOAD_DEREF               0 (self)
             46 LOAD_ATTR                2 (_frame_buffer)
             66 RESUME                   3
             68 JUMP_BACKWARD_NO_INTERRUPT     4 (to 62)
             70 POP_TOP

182          72 NOP

183          74 LOAD_CONST               4 (True)
             76 LOAD_DEREF               0 (self)
             78 STORE_ATTR               3 (_is_analyzing)

188          88 BUILD_LIST               0
             90 STORE_FAST               2 (frames_to_send)

189          92 LOAD_GLOBAL              9 (NULL + len)
            102 CACHE
            104 LOAD_DEREF               0 (self)
            106 LOAD_ATTR                1 (NULL|self + _visual_analysis_enabled)
            126 CACHE
            128 CACHE
            130 STORE_FAST               3 (buffer_len)

191         132 LOAD_FAST                3 (buffer_len)
            134 LOAD_CONST               5 (0)
            136 COMPARE_OP               4 (<)
            140 CACHE
            142 POP_JUMP_IF_FALSE       66 (to 276)

194         144 LOAD_CONST               5 (0)
            146 LOAD_FAST                3 (buffer_len)
            148 LOAD_CONST               6 (2)
            150 BINARY_OP                2 (//)
            154 LOAD_FAST                3 (buffer_len)
            156 LOAD_CONST               7 (1)
            158 BINARY_OP               10 (-)
            162 BUILD_SET                3
            164 STORE_FAST               4 (indices_to_pick)

197         166 LOAD_CLOSURE             0 (self)
            168 BUILD_TUPLE              1
            170 LOAD_CONST               8 (<code object <listcomp> at 0x0000024C7ACF7E10, file "Nova_Voice_Assistant.py", line 197>)
            172 MAKE_FUNCTION            8 (closure)
            174 LOAD_GLOBAL             11 (NULL + sorted)
            184 CACHE
            186 LOAD_GLOBAL             13 (NULL + list)
            196 CACHE
            198 LOAD_FAST                4 (indices_to_pick)
            200 UNPACK_SEQUENCE          1
            204 CALL                     1
            212 CACHE
            214 UNPACK_SEQUENCE          1
            218 CALL                     1
            226 CACHE
            228 GET_ITER
            230 UNPACK_SEQUENCE          0
            234 CALL                     0
            242 CACHE
            244 STORE_FAST               5 (selected_frames)

200         246 LOAD_FAST                5 (selected_frames)
            248 LOAD_CONST               3 (None)
            250 LOAD_DEREF               0 (self)
            252 LOAD_ATTR                7 (NULL|self + _is_analyzing)
            272 CACHE
            274 STORE_FAST               2 (frames_to_send)

202     >>  276 LOAD_FAST                2 (frames_to_send)
            278 POP_JUMP_IF_TRUE        27 (to 334)

203         280 NOP

233         282 LOAD_CONST               9 (False)
            284 LOAD_DEREF               0 (self)
            286 STORE_ATTR               3 (_is_analyzing)

181         296 LOAD_CONST               3 (None)
            298 LOAD_CONST               3 (None)
            300 LOAD_CONST               3 (None)
            302 UNPACK_SEQUENCE          2
            306 CALL                     2
            314 CACHE
            316 GET_AWAITABLE            2
            318 LOAD_CONST               3 (None)
        >>  320 SEND                     3 (to 330)
            324 RESUME                   3
            326 JUMP_BACKWARD_NO_INTERRUPT     4 (to 320)
            328 POP_TOP
        >>  330 LOAD_CONST              10 ('विश्लेषण के लिए कोई फ्रेम नहीं चुना गया')
            332 RETURN_VALUE

205     >>  334 LOAD_DEREF               0 (self)
            336 LOAD_ATTR                8 (len)
            356 CACHE
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 UNPACK_SEQUENCE          0
            372 CALL                     0
            380 CACHE
            382 STORE_FAST               6 (session)

206         384 LOAD_FAST                6 (session)
            386 POP_JUMP_IF_TRUE        27 (to 442)

207         388 NOP

233         390 LOAD_CONST               9 (False)
            392 LOAD_DEREF               0 (self)
            394 STORE_ATTR               3 (_is_analyzing)

181         404 LOAD_CONST               3 (None)
            406 LOAD_CONST               3 (None)
            408 LOAD_CONST               3 (None)
            410 UNPACK_SEQUENCE          2
            414 CALL                     2
            422 CACHE
            424 GET_AWAITABLE            2
            426 LOAD_CONST               3 (None)
        >>  428 SEND                     3 (to 438)
            432 RESUME                   3
            434 JUMP_BACKWARD_NO_INTERRUPT     4 (to 428)
            436 POP_TOP
        >>  438 LOAD_CONST              11 ('सत्र उपलब्ध नहीं')
            440 RETURN_VALUE

210     >>  442 LOAD_FAST                6 (session)
            444 STORE_SUBSCR
            448 CACHE
            450 CACHE
            452 CACHE
            454 CACHE
            456 CACHE
            458 CACHE
            460 CACHE
            462 CACHE
            464 CACHE
            466 UNPACK_SEQUENCE          0
            470 CALL                     0
            478 CACHE
            480 POP_TOP

211         482 LOAD_FAST                2 (frames_to_send)
            484 GET_ITER
        >>  486 FOR_ITER                23 (to 536)

212         490 LOAD_FAST                6 (session)
            492 STORE_SUBSCR
            496 CACHE
            498 CACHE
            500 CACHE
            502 CACHE
            504 CACHE
            506 CACHE
            508 CACHE
            510 CACHE
            512 CACHE
            514 LOAD_FAST                7 (frame)
            516 UNPACK_SEQUENCE          1
            520 CALL                     1
            528 CACHE
            530 POP_TOP
            532 JUMP_BACKWARD           24 (to 486)

215         534 LOAD_FAST                1 (prompt)
        >>  536 FORMAT_VALUE             0
            538 LOAD_CONST              12 ('\nसंदर्भ: ')

216         540 LOAD_GLOBAL              9 (NULL + len)
            550 CACHE
            552 LOAD_FAST                2 (frames_to_send)
            554 UNPACK_SEQUENCE          1
            558 CALL                     1
            566 CACHE

215         568 FORMAT_VALUE             0
            570 LOAD_CONST              13 (' प्रतिनिधि फ्रेम्स का विश्लेषण करें।\nकृपया हिंदी में विस्तृत विश्लेषण दें')
            572 BUILD_STRING             4

214         574 STORE_FAST               8 (enhanced_prompt)

220         576 LOAD_FAST                6 (session)
            578 STORE_SUBSCR
            582 CACHE
            584 CACHE
            586 CACHE
            588 CACHE
            590 CACHE
            592 CACHE
            594 CACHE
            596 CACHE
            598 CACHE

221         600 LOAD_FAST                8 (enhanced_prompt)

222         602 LOAD_CONST              14 (20.0)

220         604 KW_NAMES                15 (('instructions', 'timeout'))
            606 UNPACK_SEQUENCE          2
            610 CALL                     2
            618 CACHE
            620 GET_AWAITABLE            0
            622 LOAD_CONST               3 (None)
        >>  624 SEND                     3 (to 634)
            628 RESUME                   3
            630 JUMP_BACKWARD_NO_INTERRUPT     4 (to 624)
            632 STORE_FAST               9 (response)

224     >>  634 LOAD_FAST                9 (response)
            636 LOAD_ATTR               13 (NULL|self + list)
            656 CACHE
            658 CACHE

181         660 SWAP                     2
            662 LOAD_CONST               3 (None)
            664 LOAD_CONST               3 (None)
            666 LOAD_CONST               3 (None)
            668 UNPACK_SEQUENCE          2
            672 CALL                     2
            680 CACHE
            682 GET_AWAITABLE            2
            684 LOAD_CONST               3 (None)
        >>  686 SEND                     3 (to 696)
            690 RESUME                   3
            692 JUMP_BACKWARD_NO_INTERRUPT     4 (to 686)
            694 POP_TOP
        >>  696 RETURN_VALUE
        >>  698 PUSH_EXC_INFO

226         700 LOAD_GLOBAL             28 (asyncio)
            710 CACHE
            712 LOAD_ATTR               15 (NULL|self + _max_frames_to_send)
            732 LOAD_DEREF               0 (self)
            734 STORE_ATTR               3 (_is_analyzing)

181         744 LOAD_CONST               3 (None)
            746 LOAD_CONST               3 (None)
            748 LOAD_CONST               3 (None)
            750 UNPACK_SEQUENCE          2
            754 CALL                     2
            762 CACHE
            764 GET_AWAITABLE            2
            766 LOAD_CONST               3 (None)
        >>  768 SEND                     3 (to 778)
            772 RESUME                   3
            774 JUMP_BACKWARD_NO_INTERRUPT     4 (to 768)
            776 POP_TOP
        >>  778 LOAD_CONST              16 ('विश्लेषण समय समाप्त - कृपया पुनः प्रयास करें')
            780 RETURN_VALUE

228         782 LOAD_GLOBAL             32 (Exception)
            792 CACHE
            794 CHECK_EXC_MATCH
            796 POP_JUMP_IF_FALSE       94 (to 986)
            798 STORE_FAST              10 (e)

229         800 LOAD_GLOBAL             35 (NULL + getattr)
            810 CACHE
            812 LOAD_DEREF               0 (self)
            814 LOAD_CONST              17 ('logger')
            816 LOAD_GLOBAL             36 (logging)
            826 CACHE
            828 UNPACK_SEQUENCE          3
            832 CALL                     3
            840 CACHE
            842 STORE_FAST              11 (logger)

230         844 LOAD_FAST               11 (logger)
            846 STORE_SUBSCR
            850 CACHE
            852 CACHE
            854 CACHE
            856 CACHE
            858 CACHE
            860 CACHE
            862 CACHE
            864 CACHE
            866 CACHE
            868 LOAD_CONST              18 ('Analysis error: ')
            870 LOAD_GLOBAL             41 (NULL + str)
            880 CACHE
            882 LOAD_FAST               10 (e)
            884 UNPACK_SEQUENCE          1
            888 CALL                     1
            896 CACHE
            898 FORMAT_VALUE             0
            900 BUILD_STRING             2
            902 UNPACK_SEQUENCE          1
            906 CALL                     1
            914 CACHE
            916 POP_TOP

231         918 POP_EXCEPT
            920 LOAD_CONST               3 (None)
            922 STORE_FAST              10 (e)
            924 DELETE_FAST             10 (e)

233         926 LOAD_CONST               9 (False)
            928 LOAD_DEREF               0 (self)
            930 STORE_ATTR               3 (_is_analyzing)

181         940 LOAD_CONST               3 (None)
            942 LOAD_CONST               3 (None)
            944 LOAD_CONST               3 (None)
            946 UNPACK_SEQUENCE          2
            950 CALL                     2
            958 CACHE
            960 GET_AWAITABLE            2
            962 LOAD_CONST               3 (None)
        >>  964 SEND                     3 (to 974)
            968 RESUME                   3
            970 JUMP_BACKWARD_NO_INTERRUPT     4 (to 964)
            972 POP_TOP
        >>  974 LOAD_CONST              19 ('विश्लेषण में त्रुटि हुई')
            976 RETURN_VALUE
        >>  978 LOAD_CONST               3 (None)
            980 STORE_FAST              10 (e)
            982 DELETE_FAST             10 (e)
            984 RERAISE                  1

228     >>  986 RERAISE                  0
        >>  988 COPY                     3
            990 POP_EXCEPT
            992 RERAISE                  1
        >>  994 PUSH_EXC_INFO

233         996 LOAD_CONST               9 (False)
            998 LOAD_DEREF               0 (self)
           1000 STORE_ATTR               3 (_is_analyzing)
           1010 RERAISE                  0
        >> 1012 COPY                     3
           1014 POP_EXCEPT
           1016 RERAISE                  1

181     >> 1018 PUSH_EXC_INFO
           1020 WITH_EXCEPT_START
           1022 GET_AWAITABLE            2
           1024 LOAD_CONST               3 (None)
        >> 1026 SEND                     3 (to 1036)
           1030 RESUME                   3
           1032 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1026)
           1034 POP_JUMP_IF_TRUE         4 (to 1044)
        >> 1036 RERAISE                  2
        >> 1038 COPY                     3
           1040 POP_EXCEPT
           1042 RERAISE                  1
        >> 1044 POP_TOP
           1046 POP_EXCEPT
           1048 POP_TOP
           1050 POP_TOP
           1052 LOAD_CONST               3 (None)
           1054 RETURN_VALUE
ExceptionTable:
  70 to 70 -> 1018 [1] lasti
  74 to 278 -> 698 [1]
  282 to 294 -> 1018 [1] lasti
  334 to 386 -> 698 [1]
  390 to 402 -> 1018 [1] lasti
  442 to 644 -> 698 [1]
  646 to 658 -> 1018 [1] lasti
  698 to 726 -> 988 [2] lasti
  728 to 728 -> 994 [1]
  730 to 742 -> 1018 [1] lasti
  782 to 798 -> 988 [2] lasti
  800 to 916 -> 978 [2] lasti
  918 to 924 -> 994 [1]
  926 to 938 -> 1018 [1] lasti
  978 to 986 -> 988 [2] lasti
  988 to 992 -> 994 [1]
  994 to 1010 -> 1012 [2] lasti
  1012 to 1016 -> 1018 [1] lasti
  1018 to 1036 -> 1038 [3] lasti
  1044 to 1044 -> 1038 [3] lasti

Disassembly of <code object <listcomp> at 0x0000024C7ACF7E10, file "Nova_Voice_Assistant.py", line 197>:
              0 COPY_FREE_VARS           1

197           2 RESUME                   0
              4 BUILD_LIST               0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                15 (to 42)
             12 LOAD_DEREF               2 (self)
             14 LOAD_ATTR                0 (_frame_buffer)
             34 CACHE
             36 LIST_APPEND              2
             38 JUMP_BACKWARD           16 (to 8)
             40 RETURN_VALUE

Disassembly of <code object _trigger_gui_effect at 0x0000024C7ADB5CA0, file "Nova_Voice_Assistant.py", line 236>:
236           0 RESUME                   0

238           2 LOAD_CONST               0 (None)
              4 RETURN_VALUE

Disassembly of <code object _initialize_tools at 0x0000024C7BB09690, file "Nova_Voice_Assistant.py", line 240>:
240           0 RESUME                   0

242           2 BUILD_LIST               0
              4 STORE_FAST               2 (validated_tools)

243           6 LOAD_FAST                1 (tools)
              8 GET_ITER
        >>   10 EXTENDED_ARG             1
             12 FOR_ITER               277 (to 570)

244          16 NOP

245          18 LOAD_GLOBAL              1 (NULL + callable)
             28 CACHE
             30 LOAD_FAST                3 (tool)
             32 UNPACK_SEQUENCE          1
             36 CALL                     1
             44 CACHE
             46 POP_JUMP_IF_TRUE        47 (to 142)

246          48 LOAD_GLOBAL              3 (NULL + ValueError)
             58 CACHE
             60 LOAD_CONST               1 ('Tool ')
             62 LOAD_GLOBAL              5 (NULL + getattr)
             72 CACHE
             74 LOAD_FAST                3 (tool)
             76 LOAD_CONST               2 ('__name__')
             78 LOAD_GLOBAL              7 (NULL + str)
             88 CACHE
             90 LOAD_FAST                3 (tool)
             92 UNPACK_SEQUENCE          1
             96 CALL                     1
            104 CACHE
            106 UNPACK_SEQUENCE          3
            110 CALL                     3
            118 CACHE
            120 FORMAT_VALUE             0
            122 LOAD_CONST               3 (' is not callable')
            124 BUILD_STRING             3
            126 UNPACK_SEQUENCE          1
            130 CALL                     1
            138 CACHE
            140 RAISE_VARARGS            1

247     >>  142 LOAD_FAST                3 (tool)
            144 LOAD_ATTR                4 (getattr)
            164 CACHE
            166 CACHE
            168 LOAD_CONST               4 ('⚠️ Warning: Tool ')
            170 LOAD_FAST                3 (tool)
            172 LOAD_ATTR                6 (str)
            192 CALL                     1
            200 CACHE
            202 POP_TOP

252         204 LOAD_FAST                3 (tool)
            206 LOAD_ATTR                4 (getattr)
            226 CACHE
            228 CACHE
            230 STORE_SUBSCR
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 UNPACK_SEQUENCE          0
            256 CALL                     0
            264 CACHE
            266 JUMP_FORWARD             9 (to 286)
            268 LOAD_CONST               6 ('Tool: ')
            270 LOAD_FAST                3 (tool)
            272 LOAD_ATTR                6 (str)
            292 BUILD_CONST_KEY_MAP      3
            294 LOAD_FAST                3 (tool)
            296 STORE_ATTR               8 (metadata)

256         306 LOAD_FAST                2 (validated_tools)
            308 STORE_SUBSCR
            312 CACHE
            314 CACHE
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 LOAD_FAST                3 (tool)
            332 UNPACK_SEQUENCE          1
            336 CALL                     1
            344 CACHE
            346 POP_TOP

257         348 LOAD_GLOBAL             11 (NULL + print)
            358 CACHE
            360 LOAD_CONST              10 ('✅ Initialized tool: ')
            362 LOAD_FAST                3 (tool)
            364 LOAD_ATTR                6 (str)
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 POP_TOP
            394 JUMP_BACKWARD          193 (to 10)
        >>  396 PUSH_EXC_INFO

258         398 LOAD_GLOBAL             20 (Exception)
            408 CACHE
            410 CHECK_EXC_MATCH
            412 POP_JUMP_IF_FALSE       73 (to 560)
            414 STORE_FAST               4 (e)

259         416 LOAD_GLOBAL             11 (NULL + print)
            426 CACHE
            428 LOAD_CONST              11 ('⚠️ Failed to initialize tool ')
            430 LOAD_GLOBAL              5 (NULL + getattr)
            440 CACHE
            442 LOAD_FAST                3 (tool)
            444 LOAD_CONST               2 ('__name__')
            446 LOAD_GLOBAL              7 (NULL + str)
            456 CACHE
            458 LOAD_FAST                3 (tool)
            460 UNPACK_SEQUENCE          1
            464 CALL                     1
            472 CACHE
            474 UNPACK_SEQUENCE          3
            478 CALL                     3
            486 CACHE
            488 FORMAT_VALUE             0
            490 LOAD_CONST              12 (': ')
            492 LOAD_GLOBAL              7 (NULL + str)
            502 CACHE
            504 LOAD_FAST                4 (e)
            506 UNPACK_SEQUENCE          1
            510 CALL                     1
            518 CACHE
            520 FORMAT_VALUE             0
            522 BUILD_STRING             4
            524 UNPACK_SEQUENCE          1
            528 CALL                     1
            536 CACHE
            538 POP_TOP
            540 POP_EXCEPT
            542 LOAD_CONST               7 (None)
            544 STORE_FAST               4 (e)
            546 DELETE_FAST              4 (e)
            548 EXTENDED_ARG             1
            550 JUMP_BACKWARD          271 (to 10)
        >>  552 LOAD_CONST               7 (None)
            554 STORE_FAST               4 (e)
            556 DELETE_FAST              4 (e)
            558 RERAISE                  1

258     >>  560 RERAISE                  0
        >>  562 COPY                     3
            564 POP_EXCEPT
            566 RERAISE                  1

261         568 LOAD_GLOBAL             11 (NULL + print)
            578 CACHE
            580 LOAD_CONST              13 ('📋 Total tools initialized: ')
            582 LOAD_GLOBAL             23 (NULL + len)
            592 CACHE
            594 LOAD_FAST                2 (validated_tools)
            596 UNPACK_SEQUENCE          1
            600 CALL                     1
            608 CACHE
            610 FORMAT_VALUE             0
            612 BUILD_STRING             2
            614 UNPACK_SEQUENCE          1
            618 CALL                     1
            626 CACHE
            628 POP_TOP

262         630 LOAD_FAST                2 (validated_tools)
            632 RETURN_VALUE
ExceptionTable:
  18 to 392 -> 396 [1]
  396 to 414 -> 562 [2] lasti
  416 to 538 -> 552 [2] lasti
  552 to 560 -> 562 [2] lasti

Disassembly of <code object _build_instructions at 0x0000024C7AAE2120, file "Nova_Voice_Assistant.py", line 266>:
266           0 RESUME                   0

268           2 BUILD_LIST               0
              4 STORE_FAST               1 (tool_descriptions)

269           6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (_tools)
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 STORE_FAST               3 (tool_name)

271          38 LOAD_FAST                2 (tool)
             40 LOAD_ATTR                2 (__name__)
             60 CACHE
             62 CACHE
             64 JUMP_FORWARD             1 (to 68)
             66 LOAD_CONST               1 ('No description available')
        >>   68 STORE_FAST               4 (tool_doc)

272          70 LOAD_FAST                1 (tool_descriptions)
             72 STORE_SUBSCR
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 LOAD_CONST               2 ('- ')
             96 LOAD_FAST                3 (tool_name)
             98 FORMAT_VALUE             0
            100 LOAD_CONST               3 (': ')
            102 LOAD_FAST                4 (tool_doc)
            104 STORE_SUBSCR
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 UNPACK_SEQUENCE          0
            130 CALL                     0
            138 CACHE
            140 FORMAT_VALUE             0
            142 BUILD_STRING             4
            144 UNPACK_SEQUENCE          1
            148 CALL                     1
            156 CACHE
            158 POP_TOP
            160 JUMP_BACKWARD           71 (to 20)

274         162 LOAD_CONST               4 ('\n')
            164 STORE_SUBSCR
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 LOAD_FAST                1 (tool_descriptions)
            188 UNPACK_SEQUENCE          1
            192 CALL                     1
            200 CACHE
            202 STORE_FAST               5 (available_tools)

276         204 LOAD_CONST               4 ('\n')
            206 STORE_SUBSCR
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE

277         228 LOAD_GLOBAL             12 (AGENT_INSTRUCTION)
            238 CACHE

278         240 LOAD_GLOBAL             14 (SESSION_INSTRUCTION)
            250 CACHE

279         252 LOAD_GLOBAL             16 (AGENT_INSTRUCTION_FOR_TOOLS)
            262 CACHE

280         264 LOAD_CONST               5 ('You are a multilingual assistant capable of understanding and responding in multiple languages.')

281         266 LOAD_CONST               6 ('\nAvailable tools:\n')
            268 LOAD_FAST                5 (available_tools)
            270 FORMAT_VALUE             0
            272 BUILD_STRING             2

282         274 LOAD_CONST               7 ('\nWhen a user request requires tool usage, actively use the appropriate tool and provide feedback about the action taken.')

276         276 BUILD_LIST               6
            278 UNPACK_SEQUENCE          1
            282 CALL                     1
            290 CACHE
            292 RETURN_VALUE

Disassembly of <code object on_tool_call_start at 0x0000024C7AB1FB20, file "Nova_Voice_Assistant.py", line 285>:
              0 COPY_FREE_VARS           1

285           2 RETURN_GENERATOR
              4 POP_TOP
              6 RESUME                   0

287           8 LOAD_GLOBAL              1 (NULL + print)
             18 CACHE
             20 LOAD_CONST               1 ('🔧 Starting tool call: ')
             22 LOAD_FAST                1 (tool_call)
             24 LOAD_ATTR                1 (NULL|self + print)
             44 FORMAT_VALUE             0
             46 BUILD_STRING             2
             48 UNPACK_SEQUENCE          1
             52 CALL                     1
             60 CACHE
             62 POP_TOP

288          64 LOAD_FAST                1 (tool_call)
             66 LOAD_ATTR                1 (NULL|self + print)
             86 LOAD_FAST                0 (self)
             88 STORE_ATTR               3 (_last_tool_used)

289          98 LOAD_GLOBAL              9 (NULL + super)
            108 CACHE
            110 UNPACK_SEQUENCE          0
            114 CALL                     0
            122 CACHE
            124 STORE_SUBSCR
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 LOAD_FAST                1 (tool_call)
            148 UNPACK_SEQUENCE          1
            152 CALL                     1
            160 CACHE
            162 GET_AWAITABLE            0
            164 LOAD_CONST               2 (None)
        >>  166 SEND                     3 (to 176)
            170 RESUME                   3
            172 JUMP_BACKWARD_NO_INTERRUPT     4 (to 166)
            174 RETURN_VALUE

Disassembly of <code object on_tool_call_end at 0x0000024C7AA3D5F0, file "Nova_Voice_Assistant.py", line 291>:
              0 COPY_FREE_VARS           1

291           2 RETURN_GENERATOR
              4 POP_TOP
              6 RESUME                   0

293           8 LOAD_FAST                2 (result)
