#!/usr/bin/env python3.12
"""
Python .pyc decompiler script using Python 3.12
Supports multiple decompilation methods
"""

import os
import sys
import subprocess
from pathlib import Path

def decompile_with_uncompyle6(pyc_file, output_file=None):
    """Decompile using uncompyle6"""
    try:
        import uncompyle6
        from uncompyle6.main import decompile_file
        import io
        
        if output_file is None:
            output_file = pyc_file.replace('.pyc', '_decompiled.py')
        
        print(f"Decompiling {pyc_file} with uncompyle6...")
        
        # Capture output
        output = io.StringIO()
        
        # Decompile
        decompile_file(pyc_file, output)
        
        # Write to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(output.getvalue())
        
        print(f"✅ Successfully decompiled to: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ uncompyle6 failed: {e}")
        return False

def decompile_with_decompyle3(pyc_file, output_file=None):
    """Decompile using decompyle3"""
    try:
        import decompyle3.main
        import io
        import sys

        if output_file is None:
            output_file = pyc_file.replace('.pyc', '_decompiled.py')

        print(f"Decompiling {pyc_file} with decompyle3...")

        # Capture stdout
        old_stdout = sys.stdout
        sys.stdout = captured_output = io.StringIO()

        try:
            # Call decompyle3 main function
            decompyle3.main.main(
                in_base=os.path.dirname(pyc_file) or '.',
                out_base=os.path.dirname(output_file) or '.',
                compiled_files=[pyc_file],
                source_files=[output_file]
            )

            # Restore stdout
            sys.stdout = old_stdout

            # Check if file was created
            if os.path.exists(output_file):
                print(f"✅ Successfully decompiled to: {output_file}")
                return True
            else:
                # Try alternative approach
                output_content = captured_output.getvalue()
                if output_content.strip():
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(output_content)
                    print(f"✅ Successfully decompiled to: {output_file}")
                    return True
                else:
                    print(f"❌ decompyle3 produced no output")
                    return False

        finally:
            sys.stdout = old_stdout

    except Exception as e:
        print(f"❌ decompyle3 failed: {e}")
        return False

def analyze_pyc_file(pyc_file):
    """Analyze .pyc file structure"""
    try:
        with open(pyc_file, 'rb') as f:
            magic = f.read(4)
            timestamp = f.read(4)
            size = f.read(4) if len(f.read(4)) == 4 else None

        print(f"📊 Analysis of {pyc_file}:")
        print(f"   Magic bytes: {magic.hex()}")
        print(f"   File size: {os.path.getsize(pyc_file)} bytes")

        # Try to determine Python version from magic bytes
        magic_versions = {
            b'\xa7\x0d\x0d\x0a': 'Python 3.11',
            b'\x6f\x0d\x0d\x0a': 'Python 3.11 (older)',
            b'\x61\x0d\x0d\x0a': 'Python 3.10',
            b'\x55\x0d\x0d\x0a': 'Python 3.9',
            b'\x42\x0d\x0d\x0a': 'Python 3.8',
            b'\x33\x0d\x0d\x0a': 'Python 3.7',
        }

        version = magic_versions.get(magic, 'Unknown Python version')
        print(f"   Detected version: {version}")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")

def disassemble_bytecode(pyc_file, output_file=None):
    """Use dis module to disassemble bytecode"""
    try:
        import dis
        import marshal

        if output_file is None:
            output_file = pyc_file.replace('.pyc', '_disassembled.txt')

        print(f"Disassembling bytecode from {pyc_file}...")

        with open(pyc_file, 'rb') as f:
            # Skip header (magic, timestamp, size)
            f.read(16)  # Skip header
            code_obj = marshal.load(f)

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"Bytecode disassembly of {pyc_file}\n")
            f.write("=" * 50 + "\n\n")

            # Redirect dis output to file
            import sys
            old_stdout = sys.stdout
            sys.stdout = f

            try:
                dis.dis(code_obj)
            finally:
                sys.stdout = old_stdout

        print(f"✅ Bytecode disassembled to: {output_file}")
        return True

    except Exception as e:
        print(f"❌ Bytecode disassembly failed: {e}")
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python decompile_pyc.py <pyc_file> [output_file]")
        return
    
    pyc_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(pyc_file):
        print(f"❌ File not found: {pyc_file}")
        return
    
    print(f"🔍 Processing: {pyc_file}")
    
    # Analyze the file first
    analyze_pyc_file(pyc_file)
    print()
    
    # Try different decompilation methods
    success = False
    
    # Method 1: uncompyle6
    if decompile_with_uncompyle6(pyc_file, output_file):
        success = True
    
    # Method 2: decompyle3 (if uncompyle6 failed)
    if not success:
        if decompile_with_decompyle3(pyc_file, output_file):
            success = True

    # Method 3: Bytecode disassembly (always try this for analysis)
    print("\n🔍 Attempting bytecode disassembly...")
    disassemble_bytecode(pyc_file)

    if not success:
        print("\n❌ All decompilation methods failed")
        print("💡 Try these alternatives:")
        print("   1. Use online decompilers like https://python-decompiler.com/")
        print("   2. Try pycdc (C++ based): https://github.com/zrax/pycdc")
        print("   3. Use newer versions of uncompyle6 or decompyle3")
        print("   4. Check the bytecode disassembly file for structure analysis")
    else:
        print(f"\n✅ Decompilation successful! Check the output file.")

if __name__ == "__main__":
    main()
