#!/usr/bin/env python3
"""
Python .pyc decompiler script
Supports multiple decompilation methods
"""

import os
import sys
import subprocess
from pathlib import Path

def decompile_with_uncompyle6(pyc_file, output_file=None):
    """Decompile using uncompyle6"""
    try:
        import uncompyle6
        from uncompyle6.main import decompile_file
        import io
        
        if output_file is None:
            output_file = pyc_file.replace('.pyc', '_decompiled.py')
        
        print(f"Decompiling {pyc_file} with uncompyle6...")
        
        # Capture output
        output = io.StringIO()
        
        # Decompile
        decompile_file(pyc_file, output)
        
        # Write to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(output.getvalue())
        
        print(f"✅ Successfully decompiled to: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ uncompyle6 failed: {e}")
        return False

def decompile_with_decompyle3(pyc_file, output_file=None):
    """Decompile using decompyle3"""
    try:
        if output_file is None:
            output_file = pyc_file.replace('.pyc', '_decompiled.py')
        
        print(f"Decompiling {pyc_file} with decompyle3...")
        
        # Try subprocess approach
        result = subprocess.run([
            sys.executable, '-m', 'decompyle3', pyc_file
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result.stdout)
            print(f"✅ Successfully decompiled to: {output_file}")
            return True
        else:
            print(f"❌ decompyle3 failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ decompyle3 failed: {e}")
        return False

def analyze_pyc_file(pyc_file):
    """Analyze .pyc file structure"""
    try:
        with open(pyc_file, 'rb') as f:
            magic = f.read(4)
            timestamp = f.read(4)
            size = f.read(4) if len(f.read(4)) == 4 else None
            
        print(f"📊 Analysis of {pyc_file}:")
        print(f"   Magic bytes: {magic.hex()}")
        print(f"   File size: {os.path.getsize(pyc_file)} bytes")
        
        # Try to determine Python version from magic bytes
        magic_versions = {
            b'\x6f\x0d\x0d\x0a': 'Python 3.11',
            b'\x61\x0d\x0d\x0a': 'Python 3.10',
            b'\x55\x0d\x0d\x0a': 'Python 3.9',
            b'\x42\x0d\x0d\x0a': 'Python 3.8',
            b'\x33\x0d\x0d\x0a': 'Python 3.7',
        }
        
        version = magic_versions.get(magic, 'Unknown Python version')
        print(f"   Detected version: {version}")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

def main():
    if len(sys.argv) < 2:
        print("Usage: python decompile_pyc.py <pyc_file> [output_file]")
        return
    
    pyc_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(pyc_file):
        print(f"❌ File not found: {pyc_file}")
        return
    
    print(f"🔍 Processing: {pyc_file}")
    
    # Analyze the file first
    analyze_pyc_file(pyc_file)
    print()
    
    # Try different decompilation methods
    success = False
    
    # Method 1: uncompyle6
    if decompile_with_uncompyle6(pyc_file, output_file):
        success = True
    
    # Method 2: decompyle3 (if uncompyle6 failed)
    if not success:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'decompyle3'], 
                         check=True, capture_output=True)
            if decompile_with_decompyle3(pyc_file, output_file):
                success = True
        except:
            pass
    
    if not success:
        print("❌ All decompilation methods failed")
        print("💡 Try these alternatives:")
        print("   1. Use online decompilers")
        print("   2. Try pycdc (C++ based)")
        print("   3. Use dis module for bytecode analysis")

if __name__ == "__main__":
    main()
