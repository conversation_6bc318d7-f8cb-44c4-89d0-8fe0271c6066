Readable Information from nova_gui.exe_extracted\PYZ.pyz_extracted\Nova_Voice_Assistant.pyc
============================================================

HEADER ANALYSIS:
Magic bytes: a70d0d0a
File size: 23302 bytes

CODE STRUCTURE:
Code Object Analysis:
  Name: <module>
  Filename: Nova_Voice_Assistant.py
  First line: 1
  Arg count: 0
  Local count: 0
  Stack size: 5
  Flags: 0
  Names: ('asyncio', 'livekit', 'rtc', 'livekit.agents.utils', 'images', 'livekit.rtc', 'VideoBufferType', 'typing', 'Any', 'List', 'Optional', 'dotenv', 'load_dotenv', 'datetime', 'agents', 'livekit.agents', 'AgentSession', 'Agent', 'RoomInputOptions', 'livekit.plugins', 'noise_cancellation', 'google', 'livekit.agents.llm.chat_context', 'ChatContext', 'numpy', 'np', 'cv2', 'collections', 'deque', 'json', 'prompts', 'AGENT_INSTRUCTION', 'SESSION_INSTRUCTION', 'AGENT_INSTRUCTION_FOR_TOOLS', 'tools', 'get_weather', 'search_web', 'play_media', 'get_time_info', 'system_power_action', 'manage_window', 'desktop_control', 'list_active_windows', 'manage_window_state', 'get_today_reminder_message_from_db', 'say_reminder', 'send_whatsapp_message', 'write_in_notepad', 'open_app', 'press_key', 'get_system_info', 'type_user_message_auto', 'scan_system_for_viruses', 'get_analysis_report', 'get_analysis_status', 'get_top_insights', 'get_data_summary', 'export_results', 'full_analysis_with_report', 'create_quick_advanced_graph', 'advanced_network_scan', 'livekit.plugins.google.beta.realtime.custom_plugins', 'EffectPlugin', 'ImportError', 'time', 'logging', 'logging.handlers', 'RotatingFileHandler', 'Assistant', 'JobContext', 'entrypoint', '__name__', 'cli', 'run_app', 'WorkerOptions')
  Var names: ()
  Constants: [0, None, ('rtc',), ('images',), ('VideoBufferType',), ('Any', 'List', 'Optional'), ('load_dotenv',), ('datetime',), ('agents',), ('AgentSession', 'Agent', 'RoomInputOptions'), ('noise_cancellation',), ('google',), ('ChatContext',), ('deque',), "('AGENT_INSTRUCTION', 'SESSION_INSTRUCTION', 'AGEN...", "('get_weather', 'search_web', 'play_media', 'get_t...", ('EffectPlugin',), ('RotatingFileHandler',), '<code object Assistant at 0x0000024C7AA5F770, file...', 'Assistant', 'ctx', '<code object entrypoint at 0x0000024C7BB678A0, fil...', '__main__', ('entrypoint_fnc',)]
  Nested code object 18:
    Code Object Analysis:
      Name: Assistant
      Filename: Nova_Voice_Assistant.py
      First line: 68
      Arg count: 0
      Local count: 0
      Stack size: 6
      Flags: 0
      Names: ('__name__', '__module__', '__qualname__', '__init__', 'bool', 'enable_visual_analysis', 'rtc', 'VideoFrame', 'process_visual_frame', 'str', 'analyze_current_scene', '_trigger_gui_effect', 'List', 'Any', '_initialize_tools', '_build_instructions', 'on_tool_call_start', 'on_tool_call_end', 'on_user_turn_completed', '_log_conversation', '_check_reminders', 'AgentSession', 'generate_startup_message', '__classcell__')
      Var names: ()
      Constants: ['Assistant', 'return', None, '<code object __init__ at 0x0000024C7BBCB400, file ...', 'enable', '<code object enable_visual_analysis at 0x0000024C7...', 'frame', '<code object process_visual_frame at 0x0000024C7BB...', 'prompt', '<code object analyze_current_scene at 0x0000024C7B...', '<code object _trigger_gui_effect at 0x0000024C7ADB...', 'tools', '<code object _initialize_tools at 0x0000024C7BB099...', '<code object _build_instructions at 0x0000024C7AAE...', '<code object on_tool_call_start at 0x0000024C7AB1F...', '<code object on_tool_call_end at 0x0000024C7AA3D5F...', '<code object on_user_turn_completed at 0x0000024C7...', 'sender', 'message', '<code object _log_conversation at 0x0000024C7B1868...', '<code object _check_reminders at 0x0000024C7AA5FC5...', 'session', '<code object generate_startup_message at 0x0000024...', ('return', None)]
      Nested code object 3:
        Code Object Analysis:
          Name: __init__
          Filename: Nova_Voice_Assistant.py
          First line: 69
          Arg count: 1
          Local count: 2
          Stack size: 25
          Flags: 3
          Names: ('tools', 'assistant_instance', '_initialize_tools', 'get_weather', 'search_web', 'play_media', 'get_time_info', 'system_power_action', 'manage_window', 'desktop_control', 'list_active_windows', 'manage_window_state', 'send_whatsapp_message', 'write_in_notepad', 'open_app', 'press_key', 'get_system_info', 'type_user_message_auto', 'scan_system_for_viruses', 'get_analysis_status', 'get_analysis_report', 'get_data_summary', 'get_top_insights', 'full_analysis_with_report', 'export_results', 'create_quick_advanced_graph', '_tools', 'super', '__init__', '_build_instructions', 'google', 'beta', 'realtime', 'RealtimeModel', '_last_tool_used', '_last_tool_success', '_chat_log_path', 'EffectPlugin', 'register_effect_callback', '_trigger_gui_effect', '_min_frame_interval', '_max_buffer_size', '_max_frames_to_send', 'deque', '_frame_buffer', '_last_frame_time', '_visual_analysis_enabled', '_is_analyzing', 'asyncio', 'Lock', '_analysis_lock')
          Var names: ('self', 'tools')
          Constants: [None, 0, 'Charon', 0.5, 0.9, ('voice', 'temperature', 'top_p'), ('instructions', 'llm', 'tools'), False, 'chat_log.txt', 20, 3, ('maxlen',)]
      Nested code object 5:
        Code Object Analysis:
          Name: enable_visual_analysis
          Filename: Nova_Voice_Assistant.py
          First line: 130
          Arg count: 2
          Local count: 2
          Stack size: 4
          Flags: 131
          Names: ('_visual_analysis_enabled', '_frame_buffer', 'clear', '_last_frame_time', 'hasattr', 'llm', 'session', 'clear_video')
          Var names: ('self', 'enable')
          Constants: ['Toggle visual analysis with proper frame refresh', 0, 'कैमरा विश्लेषण सक्रिय किया गया', 'session', 'कैमरा विश्लेषण निष्क्रिय किया गया']
      Nested code object 7:
        Code Object Analysis:
          Name: process_visual_frame
          Filename: Nova_Voice_Assistant.py
          First line: 143
          Arg count: 2
          Local count: 5
          Stack size: 7
          Flags: 131
          Names: ('_visual_analysis_enabled', 'time', '_last_frame_time', '_min_frame_interval', 'type', 'VideoBufferType', 'RGBA', 'convert', 'timestamp', '_frame_buffer', 'append', 'Exception', 'getattr', 'logging', 'error', 'str', 'clear')
          Var names: ('self', 'frame', 'current_time', 'e', 'logger')
          Constants: ['\n        Process incoming frames with throttling a...', None, 'logger', 'Frame processing error: ']
      Nested code object 9:
        Code Object Analysis:
          Name: analyze_current_scene
          Filename: Nova_Voice_Assistant.py
          First line: 171
          Arg count: 2
          Local count: 12
          Stack size: 8
          Flags: 131
          Names: ('_visual_analysis_enabled', '_frame_buffer', '_analysis_lock', '_is_analyzing', 'len', 'sorted', 'list', '_max_frames_to_send', 'llm', 'session', 'clear_video', 'push_video', 'generate_reply', 'text_content', 'asyncio', 'TimeoutError', 'Exception', 'getattr', 'logging', 'error', 'str')
          Var names: ('self', 'prompt', 'frames_to_send', 'buffer_len', 'indices_to_pick', 'selected_frames', 'session', 'frame', 'enhanced_prompt', 'response', 'e', 'logger')
          Constants: ['\n        Analyzes a limited, representative sample...', 'कृपया पहले कैमरा सक्रिय करें', 'कोई ताजा फ्रेम उपलब्ध नहीं', None, True, 0, 2, 1, '<code object <listcomp> at 0x0000024C7ACF7E10, fil...', False, 'विश्लेषण के लिए कोई फ्रेम नहीं चुना गया', 'सत्र उपलब्ध नहीं', '\nसंदर्भ: ', ' प्रतिनिधि फ्रेम्स का विश्लेषण करें।\nकृपया हिंदी म...', 20.0, ('instructions', 'timeout'), 'विश्लेषण समय समाप्त - कृपया पुनः प्रयास करें', 'logger', 'Analysis error: ', 'विश्लेषण में त्रुटि हुई']
          Nested code object 8:
            Code Object Analysis:
              Name: <listcomp>
              Filename: Nova_Voice_Assistant.py
              First line: 197
              Arg count: 1
              Local count: 2
              Stack size: 4
              Flags: 19
              Names: ('_frame_buffer',)
              Var names: ('.0', 'i')
              Constants: []
      Nested code object 10:
        Code Object Analysis:
          Name: _trigger_gui_effect
          Filename: Nova_Voice_Assistant.py
          First line: 236
          Arg count: 1
          Local count: 1
          Stack size: 1
          Flags: 3
          Names: ()
          Var names: ('self',)
          Constants: [None]
      Nested code object 12:
        Code Object Analysis:
          Name: _initialize_tools
          Filename: Nova_Voice_Assistant.py
          First line: 240
          Arg count: 2
          Local count: 5
          Stack size: 12
          Flags: 3
          Names: ('callable', 'ValueError', 'getattr', 'str', '__doc__', 'print', '__name__', 'strip', 'metadata', 'append', 'Exception', 'len')
          Var names: ('self', 'tools', 'validated_tools', 'tool', 'e')
          Constants: ['Validate and initialize tools with proper error ha...', 'Tool ', '__name__', ' is not callable', '⚠️ Warning: Tool ', ' missing docstring', 'Tool: ', None, 0, ('description', 'last_used', 'usage_count'), '✅ Initialized tool: ', '⚠️ Failed to initialize tool ', ': ', '📋 Total tools initialized: ']
      Nested code object 13:
        Code Object Analysis:
          Name: _build_instructions
          Filename: Nova_Voice_Assistant.py
          First line: 266
          Arg count: 1
          Local count: 6
          Stack size: 8
          Flags: 3
          Names: ('_tools', '__name__', '__doc__', 'append', 'strip', 'join', 'AGENT_INSTRUCTION', 'SESSION_INSTRUCTION', 'AGENT_INSTRUCTION_FOR_TOOLS')
          Var names: ('self', 'tool_descriptions', 'tool', 'tool_name', 'tool_doc', 'available_tools')
          Constants: ['Construct the complete instruction set for the age...', 'No description available', '- ', ': ', '\n', 'You are a multilingual assistant capable of unders...', '\nAvailable tools:\n', '\nWhen a user request requires tool usage, actively...']
      Nested code object 14:
        Code Object Analysis:
          Name: on_tool_call_start
          Filename: Nova_Voice_Assistant.py
          First line: 285
          Arg count: 2
          Local count: 2
          Stack size: 4
          Flags: 131
          Names: ('print', 'function_info', 'name', '_last_tool_used', 'super', 'on_tool_call_start')
          Var names: ('self', 'tool_call')
          Constants: ['Handle tool call start event.', '🔧 Starting tool call: ', None]
      Nested code object 15:
        Code Object Analysis:
          Name: on_tool_call_end
          Filename: Nova_Voice_Assistant.py
          First line: 291
          Arg count: 3
          Local count: 4
          Stack size: 6
          Flags: 131
          Names: ('isinstance', 'Exception', '_last_tool_success', 'print', 'function_info', 'name', 'super', 'on_tool_call_end')
          Var names: ('self', 'tool_call', 'result', 'success')
          Constants: ['Handle tool call completion.', '🔧 Tool call completed: ', ' - ', '✅ Success', '❌ Failed', '   Error: ', None]
      Nested code object 16:
        Code Object Analysis:
          Name: on_user_turn_completed
          Filename: Nova_Voice_Assistant.py
          First line: 302
          Arg count: 3
          Local count: 6
          Stack size: 5
          Flags: 131
          Names: ('user_message', 'text_content', 'print', '_log_conversation', '_last_tool_used', '_tools', '__name__', 'datetime', 'now', 'metadata', 'super', 'on_user_turn_completed')
          Var names: ('self', 'turn_ctx', 'new_message', 'user_message', 'assistant_message', 'tool')
          Constants: ['Handle post-processing after user turn completion.', '[no user input]', '[no assistant reply]', '\n🗣️ USER: ', '🤖 ASSISTANT: ', 'User', None, 'Assistant', 'last_used', 'usage_count', 1]
      Nested code object 19:
        Code Object Analysis:
          Name: _log_conversation
          Filename: Nova_Voice_Assistant.py
          First line: 323
          Arg count: 3
          Local count: 6
          Stack size: 10
          Flags: 131
          Names: ('datetime', 'now', 'strftime', 'open', '_chat_log_path', 'write', 'IOError', 'print', 'str')
          Var names: ('self', 'sender', 'message', 'timestamp', 'f', 'e')
          Constants: ['Log conversation to file with proper encoding and ...', '%Y-%m-%d %H:%M:%S', 'a', 'utf-8', ('encoding',), '[', '] ', ': ', '\n', None, '⚠️ Failed to log conversation: ']
      Nested code object 20:
        Code Object Analysis:
          Name: _check_reminders
          Filename: Nova_Voice_Assistant.py
          First line: 332
          Arg count: 1
          Local count: 3
          Stack size: 7
          Flags: 131
          Names: ('get_today_reminder_message_from_db', 'say_reminder', 'Exception', 'print', 'str')
          Var names: ('self', 'reminder_text', 'e')
          Constants: ['Check and announce any reminders.', None, '⚠️ Failed to check reminders: ']
      Nested code object 22:
        Code Object Analysis:
          Name: generate_startup_message
          Filename: Nova_Voice_Assistant.py
          First line: 347
          Arg count: 2
          Local count: 11
          Stack size: 7
          Flags: 131
          Names: ('nova_db', 'get_last_n_messages', 'json', 'SESSION_INSTRUCTION', 'print', 'loads', 'append', 'JSONDecodeError', 'IndexError', 'join', 'generate_reply', 'Exception')
          Var names: ('self', 'session', 'get_last_n_messages', 'json', 'final_instructions', 'last_messages', 'summary_lines', 'msg', 'content', 'last_convo_summary', 'e')
          Constants: ['\n        Dynamically constructs startup instructio...', 0, ('get_last_n_messages',), None, 4, ('n',), '✅ Found recent messages. Adding conversational con...', 1, '- ', ': ', '\n', '\n\n🔰 विशेष संदर्भ:\nयह हमारी पिछली बातचीत का अंतिम अ...', '\n--------------------------', '✅ No message history found. Using standard startup...', ('instructions',), '⚠️ Failed to generate startup message: ', 'नमस्कार सर, मैं आपकी सेवा में प्रस्तुत हूँ।']
  Nested code object 21:
    Code Object Analysis:
      Name: entrypoint
      Filename: Nova_Voice_Assistant.py
      First line: 408
      Arg count: 1
      Local count: 7
      Stack size: 12
      Flags: 131
      Names: ('range', 'Assistant', 'AgentSession', 'asyncio', 'wait_for', 'start', 'room', 'RoomInputOptions', 'noise_cancellation', 'BVC', 'TimeoutError', 'Exception', 'connect', 'generate_startup_message', '_check_reminders', 'print', 'sleep')
      Var names: ('ctx', 'max_retries', 'retry_delay', 'attempt', 'agent', 'session', 'e')
      Constants: ['Main entry point for the agent with retry logic.', 3, 5, 1, True, ('video_enabled', 'noise_cancellation'), ('room', 'agent', 'room_input_options'), 30.0, ('timeout',), None, 'Connection timeout', '✅ Agent session started successfully and is now li...', '❌ Entrypoint failed on attempt ', ': ']


EXTRACTED STRINGS:
------------------------------
  1: 'chat_log.txt'
  2: 'usage_count'
  3: '⚠️ Failed to initialize tool '
  4: '\nWhen a user request requires tool usage, actively use the appropriate tool and provide feedback about the action taken.'
  5: '❌ Failed'
  6: '✅ Success'
  7: '\n        Dynamically constructs startup instructions by combining the base SESSION_INSTRUCTION\n        with a summary of the last conversation, then generates a context-aware greeting.\n        '
  8: 'Log conversation to file with proper encoding and error handling.'
  9: 'Assistant'
 11: 'User'
 12: 'sender'
 13: 'session'
 14: '\n🗣️ USER: '
 15: '['
 16: '\n--------------------------'
 17: 'Main entry point for the agent with retry logic.'
 18: 'utf-8'
 19: 'enable'
 20: 'विश्लेषण के लिए कोई फ्रेम नहीं चुना गया'
 21: 'Frame processing error: '
 22: '✅ No message history found. Using standard startup instructions.'
 23: '🔧 Tool call completed: '
 24: 'logger'
 25: '✅ Initialized tool: '
 26: '🔧 Starting tool call: '
 27: '❌ Entrypoint failed on attempt '
 28: '- '
 29: 'Connection timeout'
 30: '⚠️ Failed to check reminders: '
 31: '\n        Process incoming frames with throttling and efficient buffering.\n        This function is called frequently by the video stream.\n        '
 32: 'Tool: '
 33: 'a'
 34: '__main__'
 35: 'सत्र उपलब्ध नहीं'
 36: 'Toggle visual analysis with proper frame refresh'
 37: 'You are a multilingual assistant capable of understanding and responding in multiple languages.'
 38: 'कृपया पहले कैमरा सक्रिय करें'
 39: 'विश्लेषण में त्रुटि हुई'
 40: 'Analysis error: '
 41: '⚠️ Failed to log conversation: '
 42: ' is not callable'
 43: 'Charon'
 44: 'कैमरा विश्लेषण सक्रिय किया गया'
 45: 'Handle tool call completion.'
 46: ' missing docstring'
 47: 'Construct the complete instruction set for the agent.'
 48: 'नमस्कार सर, मैं आपकी सेवा में प्रस्तुत हूँ।'
 49: 'Handle tool call start event.'
 50: '%Y-%m-%d %H:%M:%S'
 51: '   Error: '
 52: 'tools'
 53: '\nAvailable tools:\n'
 54: 'Handle post-processing after user turn completion.'
 55: 'Validate and initialize tools with proper error handling.'
 56: 'last_used'
 57: ' प्रतिनिधि फ्रेम्स का विश्लेषण करें।\nकृपया हिंदी में विस्तृत विश्लेषण दें'
 58: '\nसंदर्भ: '
 59: 'Tool '
 60: '📋 Total tools initialized: '
 61: '[no user input]'
 62: '⚠️ Failed to generate startup message: '
 63: '✅ Found recent messages. Adding conversational context to startup instructions.'
 64: 'frame'
 65: 'कोई ताजा फ्रेम उपलब्ध नहीं'
 66: 'Check and announce any reminders.'
 67: '✅ Agent session started successfully and is now live.'
 68: 'विश्लेषण समय समाप्त - कृपया पुनः प्रयास करें'
 69: '🤖 ASSISTANT: '
 70: '__name__'
 71: 'return'
 72: ': '
 73: ' - '
 74: '[no assistant reply]'
 75: 'ctx'
 76: '] '
 77: '\n        Analyzes a limited, representative sample of frames to avoid API quota issues.\n        '
 78: '⚠️ Warning: Tool '
 79: 'No description available'
 80: 'prompt'
 81: 'message'
 82: "\n\n🔰 विशेष संदर्भ:\nयह हमारी पिछली बातचीत का अंतिम अंश है। कृपया इसका उपयोग करके अपना अभिवादन व्यक्तिगत और प्रासंगिक बनाएं।\nउदाहरण: यदि अंतिम संदेश कॉलेज जाने के बारे में था, तो आप कह सकते हैं, 'नमस्कार अंकित सर, कॉलेज से वापस स्वागत है! आपका दिन कैसा रहा?'\n\n--- पिछली बातचीत का अंत ---\n"
 83: 'कैमरा विश्लेषण निष्क्रिय किया गया'


EXTRACTED NAMES/IDENTIFIERS:
------------------------------
  1: now
  2: images
  3: _min_frame_interval
  4: get_system_info
  5: logging.handlers
  6: append
  7: entrypoint
  8: __qualname__
  9: dotenv
 10: self
 11: register_effect_callback
 12: buffer_len
 13: get_time_info
 14: turn_ctx
 15: livekit.rtc
 16: Exception
 17: livekit.plugins
 18: agent
 19: get_data_summary
 20: IOError
 21: reminder_text
 22: frames_to_send
 23: assistant_instance
 24: Assistant
 25: Optional
 26: content
 27: advanced_network_scan
 28: msg
 29: numpy
 30: process_visual_frame
 31: ChatContext
 32: export_results
 33: _frame_buffer
 34: clear
 35: connect
 36: datetime
 37: final_instructions
 38: google
 39: enable_visual_analysis
 40: session
 41: sender
 42: open
 43: str
 44: error
 45: summary_lines
 46: enable
 47: livekit
 48: type
 49: get_analysis_status
 50: _max_buffer_size
 51: last_messages
 52: JobContext
 53: selected_frames
 54: loads
 55: logger
 56: nova_db
 57: JSONDecodeError
 58: typing
 59: rtc
 60: llm
 61: system_power_action
 62: name
 63: _chat_log_path
 64: AgentSession
 65: deque
 66: f
 67: i
 68: scan_system_for_viruses
 69: callable
 70: range
 71: desktop_control
 72: get_weather
 73: last_convo_summary
 74: get_today_reminder_message_from_db
 75: current_time
 76: json
 77: search_web
 78: tool_doc
 79: list
 80: _tools
 81: tool
 82: play_media
 83: room
 84: _initialize_tools
 85: RGBA
 86: run_app
 87: on_tool_call_start
 88: Agent
 89: prompts
 90: livekit.plugins.google.beta.realtime.custom_plugins
 91: _last_frame_time
 92: manage_window
 93: beta
 94: clear_video
 95: cli
 96: tool_call
 97: AGENT_INSTRUCTION
 98: ImportError
 99: indices_to_pick
100: press_key
101: open_app
102: manage_window_state
103: retry_delay
104: generate_startup_message
105: __classcell__
106: _is_analyzing
107: asyncio
108: _trigger_gui_effect
109: tool_name
110: WorkerOptions
111: _analysis_lock
112: max_retries
113: e
114: tools
115: .0
116: available_tools
117: strftime
118: on_tool_call_end
119: ValueError
120: print
121: create_quick_advanced_graph
122: response
123: start
124: list_active_windows
125: join
126: _max_frames_to_send
127: analyze_current_scene
128: assistant_message
129: EffectPlugin
130: _last_tool_used
131: Lock
132: validated_tools
133: generate_reply
134: np
135: send_whatsapp_message
136: _build_instructions
137: _log_conversation
138: success
139: collections
140: noise_cancellation
141: bool
142: new_message
143: say_reminder
144: type_user_message_auto
145: logging
146: _visual_analysis_enabled
147: AGENT_INSTRUCTION_FOR_TOOLS
148: push_video
149: cv2
150: isinstance
151: super
152: RotatingFileHandler
153: timestamp
154: write_in_notepad
155: Any
156: wait_for
157: frame
158: RealtimeModel
159: time
160: strip
161: RoomInputOptions
162: sleep
163: livekit.agents.llm.chat_context
164: on_user_turn_completed
165: IndexError
166: sorted
167: load_dotenv
168: __name__
169: __init__
170: livekit.agents.utils
171: function_info
172: tool_descriptions
173: List
174: SESSION_INSTRUCTION
175: convert
176: user_message
177: get_last_n_messages
178: get_analysis_report
179: metadata
180: ctx
181: get_top_insights
182: result
183: realtime
184: len
185: TimeoutError
186: __doc__
187: livekit.agents
188: full_analysis_with_report
189: agents
190: hasattr
191: VideoBufferType
192: getattr
193: prompt
194: message
195: BVC
196: __module__
197: enhanced_prompt
198: text_content
199: VideoFrame
200: _check_reminders
201: write
202: attempt
203: _last_tool_success
