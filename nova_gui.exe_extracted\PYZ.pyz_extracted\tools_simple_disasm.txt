Simple disassembly of nova_gui.exe_extracted\PYZ.pyz_extracted\tools.pyc
============================================================

   0           0 RESUME                   0

   1           2 LOAD_CONST               0 (0)
               4 LOAD_CONST               1 (None)
               6 IMPORT_NAME              0 (logging)
               8 STORE_NAME               0 (logging)

   2          10 LOAD_CONST               0 (0)
              12 LOAD_CONST               2 (('function_tool',))
              14 IMPORT_NAME              1 (livekit.agents)
              16 IMPORT_FROM              2 (function_tool)
              18 STORE_NAME               2 (function_tool)
              20 POP_TOP

   3          22 LOAD_CONST               0 (0)
              24 LOAD_CONST               1 (None)
              26 IMPORT_NAME              3 (requests)
              28 STORE_NAME               3 (requests)

   4          30 LOAD_CONST               0 (0)
              32 LOAD_CONST               3 (('DuckDuckGoSearchRun',))
              34 IMPORT_NAME              4 (langchain_community.tools)
              36 IMPORT_FROM              5 (DuckDuckGoSearchRun)
              38 STORE_NAME               5 (DuckDuckGoSearchRun)
              40 POP_TOP

   5          42 LOAD_CONST               0 (0)
              44 LOAD_CONST               1 (None)
              46 IMPORT_NAME              6 (subprocess)
              48 STORE_NAME               6 (subprocess)

   6          50 LOAD_CONST               0 (0)
              52 LOAD_CONST               1 (None)
              54 IMPORT_NAME              7 (ctypes)
              56 STORE_NAME               7 (ctypes)

   7          58 LOAD_CONST               0 (0)
              60 LOAD_CONST               1 (None)
              62 IMPORT_NAME              8 (pygetwindow)
              64 STORE_NAME               9 (gw)

   8          66 LOAD_CONST               0 (0)
              68 LOAD_CONST               1 (None)
              70 IMPORT_NAME             10 (platform)
              72 STORE_NAME              10 (platform)

   9          74 LOAD_CONST               0 (0)
              76 LOAD_CONST               1 (None)
              78 IMPORT_NAME             11 (time)
              80 STORE_NAME              11 (time)

  10          82 LOAD_CONST               0 (0)
              84 LOAD_CONST               1 (None)
              86 IMPORT_NAME             12 (os)
              88 STORE_NAME              12 (os)

  11          90 LOAD_CONST               0 (0)
              92 LOAD_CONST               1 (None)
              94 IMPORT_NAME             13 (webbrowser)
              96 STORE_NAME              13 (webbrowser)

  12          98 LOAD_CONST               0 (0)
             100 LOAD_CONST               4 (('Optional', 'Literal'))
             102 IMPORT_NAME             14 (typing)
             104 IMPORT_FROM             15 (Optional)
             106 STORE_NAME              15 (Optional)
             108 IMPORT_FROM             16 (Literal)
             110 STORE_NAME              16 (Literal)
             112 POP_TOP

  13         114 LOAD_CONST               0 (0)
             116 LOAD_CONST               5 (('datetime',))
             118 IMPORT_NAME             17 (datetime)
             120 IMPORT_FROM             17 (datetime)
             122 STORE_NAME              17 (datetime)
             124 POP_TOP

  14         126 LOAD_CONST               0 (0)
             128 LOAD_CONST               1 (None)
             130 IMPORT_NAME             18 (psutil)
             132 STORE_NAME              18 (psutil)

  15         134 LOAD_CONST               0 (0)
             136 LOAD_CONST               1 (None)
             138 IMPORT_NAME             19 (smtplib)
             140 STORE_NAME              19 (smtplib)

  16         142 LOAD_CONST               0 (0)
             144 LOAD_CONST               6 (('MIMEMultipart',))
             146 IMPORT_NAME             20 (email.mime.multipart)
             148 IMPORT_FROM             21 (MIMEMultipart)
             150 STORE_NAME              21 (MIMEMultipart)
             152 POP_TOP

  17         154 LOAD_CONST               0 (0)
             156 LOAD_CONST               7 (('MIMEText',))
             158 IMPORT_NAME             22 (email.mime.text)
             160 IMPORT_FROM             23 (MIMEText)
             162 STORE_NAME              23 (MIMEText)
             164 POP_TOP

  18         166 LOAD_CONST               0 (0)
             168 LOAD_CONST               1 (None)
             170 IMPORT_NAME             24 (pyautogui)
             172 STORE_NAME              24 (pyautogui)

  19         174 LOAD_CONST               0 (0)
             176 LOAD_CONST               1 (None)
             178 IMPORT_NAME             25 (re)
             180 STORE_NAME              25 (re)

  20         182 LOAD_CONST               0 (0)
             184 LOAD_CONST               8 (('load_dotenv',))
             186 IMPORT_NAME             26 (dotenv)
             188 IMPORT_FROM             27 (load_dotenv)
             190 STORE_NAME              27 (load_dotenv)
             192 POP_TOP

  21         194 LOAD_CONST               0 (0)
             196 LOAD_CONST               1 (None)
             198 IMPORT_NAME             28 (json)
             200 STORE_NAME              28 (json)

  22         202 LOAD_CONST               0 (0)
             204 LOAD_CONST               1 (None)
             206 IMPORT_NAME             29 (wikipedia)
             208 STORE_NAME              29 (wikipedia)

  23         210 LOAD_CONST               0 (0)
             212 LOAD_CONST               9 (('List', 'Dict'))
             214 IMPORT_NAME             14 (typing)
             216 IMPORT_FROM             30 (List)
             218 STORE_NAME              30 (List)
             220 IMPORT_FROM             31 (Dict)
             222 STORE_NAME              31 (Dict)
             224 POP_TOP

  24         226 LOAD_CONST               0 (0)
             228 LOAD_CONST               1 (None)
             230 IMPORT_NAME             32 (asyncio)
             232 STORE_NAME              32 (asyncio)

  25         234 LOAD_CONST               0 (0)
             236 LOAD_CONST               1 (None)
             238 IMPORT_NAME             33 (aiohttp)
             240 STORE_NAME              33 (aiohttp)

  26         242 LOAD_CONST               1 (None)
             244 STORE_NAME              34 (assistant_instance)

  29         246 PUSH_NULL
             248 LOAD_NAME               27 (load_dotenv)
             250 UNPACK_SEQUENCE          0
             254 CALL                     0
             262 CACHE
             264 POP_TOP

  32         266 PUSH_NULL
             268 LOAD_NAME                0 (logging)
             270 LOAD_ATTR               35 (NULL|self + datetime)
             290 CACHE

  34         292 LOAD_CONST              10 ('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

  32         294 KW_NAMES                11 (('level', 'format'))
             296 UNPACK_SEQUENCE          2
             300 CALL                     2
             308 CACHE
             310 POP_TOP

  36         312 PUSH_NULL
             314 LOAD_NAME                0 (logging)
             316 LOAD_ATTR               37 (NULL|self + psutil)
             336 CACHE
             338 CACHE
             340 CACHE
             342 STORE_NAME              39 (logger)

  39         344 LOAD_CONST              12 ('AIzaSyBW5YkF21ntit_zIMGE5TpTKlBpBJl3mfM')
             346 STORE_NAME              40 (YOUTUBE_API_KEY)

  40         348 LOAD_CONST              13 ('<EMAIL>')
             350 STORE_NAME              41 (GMAIL_USER)

  41         352 LOAD_CONST              14 ('zisn kydb qysb yifr')
             354 STORE_NAME              42 (GMAIL_PASSWORD)

  44         356 LOAD_CONST              15 (0.1)
             358 LOAD_NAME               24 (pyautogui)
             360 STORE_ATTR              43 (PAUSE)

  45         370 LOAD_CONST              16 (True)
             372 LOAD_NAME               24 (pyautogui)
             374 STORE_ATTR              44 (FAILSAFE)

  47         384 LOAD_CONST              17 ('email')
             386 LOAD_NAME               45 (str)
             388 LOAD_CONST              18 ('return')
             390 LOAD_NAME               46 (bool)
             392 BUILD_TUPLE              4
             394 LOAD_CONST              19 (<code object validate_email at 0x000001EFF690E630, file "tools.py", line 47>)
             396 MAKE_FUNCTION            4 (annotations)
             398 STORE_NAME              47 (validate_email)

  52         400 PUSH_NULL
             402 LOAD_NAME                2 (function_tool)
             404 UNPACK_SEQUENCE          0
             408 CALL                     0
             416 CACHE

  53         418 LOAD_CONST              20 ('city')
             420 LOAD_NAME               45 (str)
             422 LOAD_CONST              18 ('return')
             424 LOAD_NAME               45 (str)
             426 BUILD_TUPLE              4
             428 LOAD_CONST              21 (<code object get_weather at 0x000001EFF4BD45C0, file "tools.py", line 52>)
             430 MAKE_FUNCTION            4 (annotations)

  52         432 UNPACK_SEQUENCE          0
             436 CALL                     0
             444 CACHE

  53         446 STORE_NAME              48 (get_weather)

 120         448 PUSH_NULL
             450 LOAD_NAME                2 (function_tool)
             452 UNPACK_SEQUENCE          0
             456 CALL                     0
             464 CACHE

 121         466 LOAD_CONST              22 ('action')
             468 LOAD_NAME               16 (Literal)
             470 LOAD_CONST              23 (('shutdown', 'restart', 'lock'))
             472 BINARY_SUBSCR
             476 CACHE
             478 CACHE
             480 CACHE
             482 LOAD_CONST              18 ('return')
             484 LOAD_NAME               45 (str)
             486 BUILD_TUPLE              4
             488 LOAD_CONST              24 (<code object system_power_action at 0x000001EFF4B5A060, file "tools.py", line 120>)
             490 MAKE_FUNCTION            4 (annotations)

 120         492 UNPACK_SEQUENCE          0
             496 CALL                     0
             504 CACHE

 121         506 STORE_NAME              49 (system_power_action)

 178         508 PUSH_NULL
             510 LOAD_NAME                2 (function_tool)
             512 UNPACK_SEQUENCE          0
             516 CALL                     0
             524 CACHE

 179         526 LOAD_CONST              22 ('action')
             528 LOAD_NAME               16 (Literal)
             530 LOAD_CONST              25 (('close', 'minimize', 'maximize'))
             532 BINARY_SUBSCR
             536 CACHE
             538 CACHE
             540 CACHE
             542 LOAD_CONST              18 ('return')
             544 LOAD_NAME               45 (str)
             546 BUILD_TUPLE              4
             548 LOAD_CONST              26 (<code object manage_window at 0x000001EFF4AF8A40, file "tools.py", line 178>)
             550 MAKE_FUNCTION            4 (annotations)

 178         552 UNPACK_SEQUENCE          0
             556 CALL                     0
             564 CACHE

 179         566 STORE_NAME              50 (manage_window)

 222         568 PUSH_NULL
             570 LOAD_NAME                2 (function_tool)
             572 UNPACK_SEQUENCE          0
             576 CALL                     0
             584 CACHE

 223         586 LOAD_CONST              18 ('return')
             588 LOAD_NAME               45 (str)
             590 BUILD_TUPLE              2
             592 LOAD_CONST              27 (<code object get_time_info at 0x000001EFF3F22D30, file "tools.py", line 222>)
             594 MAKE_FUNCTION            4 (annotations)

 222         596 UNPACK_SEQUENCE          0
             600 CALL                     0
             608 CACHE

 223         610 STORE_NAME              51 (get_time_info)

 246         612 PUSH_NULL
             614 LOAD_NAME                2 (function_tool)
             616 UNPACK_SEQUENCE          0
             620 CALL                     0
             628 CACHE

 247         630 LOAD_CONST              28 ('query')
             632 LOAD_NAME               45 (str)
             634 LOAD_CONST              18 ('return')
             636 LOAD_NAME               45 (str)
             638 BUILD_TUPLE              4
             640 LOAD_CONST              29 (<code object search_web at 0x000001EFF4B5A6C0, file "tools.py", line 246>)
             642 MAKE_FUNCTION            4 (annotations)

 246         644 UNPACK_SEQUENCE          0
             648 CALL                     0
             656 CACHE

 247         658 STORE_NAME              52 (search_web)

 308         660 PUSH_NULL
             662 LOAD_NAME                2 (function_tool)
             664 UNPACK_SEQUENCE          0
             668 CALL                     0
             676 CACHE

 309         678 LOAD_CONST             171 (('song',))
             680 LOAD_CONST              31 ('media_name')
             682 LOAD_NAME               45 (str)
             684 LOAD_CONST              32 ('media_type')
             686 LOAD_NAME               16 (Literal)
             688 LOAD_CONST              33 (('song', 'video'))
             690 BINARY_SUBSCR
             694 CACHE
             696 CACHE
             698 CACHE
             700 LOAD_CONST              18 ('return')
             702 LOAD_NAME               45 (str)
             704 BUILD_TUPLE              6
             706 LOAD_CONST              34 (<code object play_media at 0x000001EFF4B5B2C0, file "tools.py", line 308>)
             708 MAKE_FUNCTION            5 (defaults, annotations)

 308         710 UNPACK_SEQUENCE          0
             714 CALL                     0
             722 CACHE

 309         724 STORE_NAME              53 (play_media)

 350         726 PUSH_NULL
             728 LOAD_NAME                2 (function_tool)
             730 UNPACK_SEQUENCE          0
             734 CALL                     0
             742 CACHE

 351         744 LOAD_CONST             172 ((None, 3))
             746 LOAD_CONST              22 ('action')
             748 LOAD_NAME               16 (Literal)
             750 LOAD_CONST              36 (('show', 'scroll'))
             752 BINARY_SUBSCR
             756 CACHE
             758 CACHE
             760 CACHE
             762 LOAD_CONST              37 ('direction')
             764 LOAD_NAME               15 (Optional)
             766 LOAD_NAME               16 (Literal)
             768 LOAD_CONST              38 (('up', 'down'))
             770 BINARY_SUBSCR
             774 CACHE
             776 CACHE
             778 CACHE
             780 BINARY_SUBSCR
             784 CACHE
             786 CACHE
             788 CACHE
             790 LOAD_CONST              39 ('amount')
             792 LOAD_NAME               15 (Optional)
             794 LOAD_NAME               54 (int)
             796 BINARY_SUBSCR
             800 CACHE
             802 CACHE
             804 CACHE
             806 LOAD_CONST              18 ('return')
             808 LOAD_NAME               45 (str)
             810 BUILD_TUPLE              8
             812 LOAD_CONST              40 (<code object desktop_control at 0x000001EFF4B75850, file "tools.py", line 350>)
             814 MAKE_FUNCTION            5 (defaults, annotations)

 350         816 UNPACK_SEQUENCE          0
             820 CALL                     0
             828 CACHE

 351         830 STORE_NAME              55 (desktop_control)

 408         832 PUSH_NULL
             834 LOAD_NAME                2 (function_tool)
             836 UNPACK_SEQUENCE          0
             840 CALL                     0
             848 CACHE

 409         850 LOAD_CONST             173 ((None,))
             852 LOAD_CONST              41 ('to_email')
             854 LOAD_NAME               45 (str)
             856 LOAD_CONST              42 ('subject')
             858 LOAD_NAME               45 (str)
             860 LOAD_CONST              43 ('message')
             862 LOAD_NAME               45 (str)
             864 LOAD_CONST              44 ('cc_email')
             866 LOAD_NAME               15 (Optional)
             868 LOAD_NAME               45 (str)
             870 BINARY_SUBSCR
             874 CACHE
             876 CACHE
             878 CACHE
             880 LOAD_CONST              18 ('return')
             882 LOAD_NAME               45 (str)
             884 BUILD_TUPLE             10
             886 LOAD_CONST              45 (<code object send_email at 0x000001EFF41E12E0, file "tools.py", line 408>)
             888 MAKE_FUNCTION            5 (defaults, annotations)

 408         890 UNPACK_SEQUENCE          0
             894 CALL                     0
             902 CACHE

 409         904 STORE_NAME              56 (send_email)

 462         906 PUSH_NULL
             908 LOAD_NAME                2 (function_tool)
             910 UNPACK_SEQUENCE          0
             914 CALL                     0
             922 CACHE

 463         924 LOAD_CONST              18 ('return')
             926 LOAD_NAME               45 (str)
             928 BUILD_TUPLE              2
             930 LOAD_CONST              46 (<code object list_active_windows at 0x000001EFF4B69330, file "tools.py", line 462>)
             932 MAKE_FUNCTION            4 (annotations)

 462         934 UNPACK_SEQUENCE          0
             938 CALL                     0
             946 CACHE

 463         948 STORE_NAME              57 (list_active_windows)

 500         950 PUSH_NULL
             952 LOAD_NAME                2 (function_tool)
             954 UNPACK_SEQUENCE          0
             958 CALL                     0
             966 CACHE

 501         968 LOAD_CONST             173 ((None,))
             970 LOAD_CONST              22 ('action')
             972 LOAD_NAME               16 (Literal)
             974 LOAD_CONST              47 (('maximize', 'minimize', 'restore'))
             976 BINARY_SUBSCR
             980 CACHE
             982 CACHE
             984 CACHE
             986 LOAD_CONST              48 ('window_title')
             988 LOAD_NAME               15 (Optional)
             990 LOAD_NAME               45 (str)
             992 BINARY_SUBSCR
             996 CACHE
             998 CACHE
            1000 CACHE
            1002 LOAD_CONST              18 ('return')
            1004 LOAD_NAME               45 (str)
            1006 BUILD_TUPLE              6
            1008 LOAD_CONST              49 (<code object manage_window_state at 0x000001EFF4BB19D0, file "tools.py", line 500>)
            1010 MAKE_FUNCTION            5 (defaults, annotations)

 500        1012 UNPACK_SEQUENCE          0
            1016 CALL                     0
            1024 CACHE

 501        1026 STORE_NAME              58 (manage_window_state)

 539        1028 PUSH_NULL
            1030 LOAD_NAME                2 (function_tool)
            1032 UNPACK_SEQUENCE          0
            1036 CALL                     0
            1044 CACHE

 540        1046 LOAD_CONST              50 ('msg')
            1048 LOAD_NAME               45 (str)
            1050 LOAD_CONST              18 ('return')
            1052 LOAD_NAME               45 (str)
            1054 BUILD_TUPLE              4
            1056 LOAD_CONST              51 (<code object say_reminder at 0x000001EFF690E730, file "tools.py", line 539>)
            1058 MAKE_FUNCTION            4 (annotations)

 539        1060 UNPACK_SEQUENCE          0
            1064 CALL                     0
            1072 CACHE

 540        1074 STORE_NAME              59 (say_reminder)

 557        1076 LOAD_CONST               0 (0)
            1078 LOAD_CONST              52 (('datetime', 'date'))
            1080 IMPORT_NAME             17 (datetime)
            1082 IMPORT_FROM             17 (datetime)
            1084 STORE_NAME              17 (datetime)
            1086 IMPORT_FROM             60 (date)
            1088 STORE_NAME              60 (date)
            1090 POP_TOP

 558        1092 LOAD_CONST               0 (0)
            1094 LOAD_CONST               1 (None)
            1096 IMPORT_NAME             61 (sqlite3)
            1098 STORE_NAME              61 (sqlite3)

 560        1100 LOAD_CONST              53 ('nova_memory/chat_history.db')
            1102 STORE_NAME              62 (DB_PATH)

 561        1104 LOAD_CONST              54 ('chat_messages')
            1106 STORE_NAME              63 (TABLE_NAME)

 563        1108 LOAD_CONST              18 ('return')
            1110 LOAD_NAME               45 (str)
            1112 LOAD_CONST               1 (None)
            1114 BINARY_OP                7 (|)
            1118 BUILD_TUPLE              2
            1120 LOAD_CONST              55 (<code object get_today_reminder_message_from_db at 0x000001EFF4BD4CD0, file "tools.py", line 563>)
            1122 MAKE_FUNCTION            4 (annotations)
            1124 STORE_NAME              64 (get_today_reminder_message_from_db)

 608        1126 LOAD_CONST              56 ('text')
            1128 LOAD_NAME               45 (str)
            1130 LOAD_CONST              18 ('return')
            1132 LOAD_NAME               15 (Optional)
            1134 LOAD_NAME               60 (date)
            1136 BINARY_SUBSCR
            1140 CACHE
            1142 CACHE
            1144 CACHE
            1146 BUILD_TUPLE              4
            1148 LOAD_CONST              57 (<code object extract_date_from_text at 0x000001EFF6991430, file "tools.py", line 608>)
            1150 MAKE_FUNCTION            4 (annotations)
            1152 STORE_NAME              65 (extract_date_from_text)

 627        1154 LOAD_CONST               0 (0)
            1156 LOAD_CONST               1 (None)
            1158 IMPORT_NAME             32 (asyncio)
            1160 STORE_NAME              32 (asyncio)

 628        1162 LOAD_CONST               0 (0)
            1164 LOAD_CONST               1 (None)
            1166 IMPORT_NAME             12 (os)
            1168 STORE_NAME              12 (os)

 629        1170 LOAD_CONST               0 (0)
            1172 LOAD_CONST               1 (None)
            1174 IMPORT_NAME              6 (subprocess)
            1176 STORE_NAME               6 (subprocess)

 630        1178 LOAD_CONST               0 (0)
            1180 LOAD_CONST               1 (None)
            1182 IMPORT_NAME             24 (pyautogui)
            1184 STORE_NAME              24 (pyautogui)

 631        1186 LOAD_CONST               0 (0)
            1188 LOAD_CONST               1 (None)
            1190 IMPORT_NAME             11 (time)
            1192 STORE_NAME              11 (time)

 632        1194 LOAD_CONST               0 (0)
            1196 LOAD_CONST              58 (('Tuple',))
            1198 IMPORT_NAME             14 (typing)
            1200 IMPORT_FROM             66 (Tuple)
            1202 STORE_NAME              66 (Tuple)
            1204 POP_TOP

 636        1206 PUSH_NULL
            1208 LOAD_NAME                2 (function_tool)
            1210 UNPACK_SEQUENCE          0
            1214 CALL                     0
            1222 CACHE

 637        1224 LOAD_CONST              59 ('contact')
            1226 LOAD_NAME               45 (str)
            1228 LOAD_CONST              43 ('message')
            1230 LOAD_NAME               45 (str)
            1232 LOAD_CONST              18 ('return')
            1234 LOAD_NAME               45 (str)
            1236 BUILD_TUPLE              6
            1238 LOAD_CONST              60 (<code object send_whatsapp_message at 0x000001EFF4B5C1D0, file "tools.py", line 636>)
            1240 MAKE_FUNCTION            4 (annotations)

 636        1242 UNPACK_SEQUENCE          0
            1246 CALL                     0
            1254 CACHE

 637        1256 STORE_NAME              67 (send_whatsapp_message)

 706        1258 LOAD_CONST              16 (True)
            1260 LOAD_NAME               24 (pyautogui)
            1262 STORE_ATTR              44 (FAILSAFE)

 707        1272 LOAD_CONST              15 (0.1)
            1274 LOAD_NAME               24 (pyautogui)
            1276 STORE_ATTR              43 (PAUSE)

 710        1286 PUSH_NULL
            1288 LOAD_NAME                2 (function_tool)
            1290 UNPACK_SEQUENCE          0
            1294 CALL                     0
            1302 CACHE

 711        1304 LOAD_CONST             174 (('letter',))
            1306 LOAD_CONST              62 ('title')
            1308 LOAD_NAME               45 (str)
            1310 LOAD_CONST              63 ('content')
            1312 LOAD_NAME               45 (str)
            1314 LOAD_CONST              64 ('document_type')
            1316 LOAD_NAME               45 (str)
            1318 LOAD_CONST              18 ('return')
            1320 LOAD_NAME               45 (str)
            1322 BUILD_TUPLE              8
            1324 LOAD_CONST              65 (<code object write_in_notepad at 0x000001EFF4B614E0, file "tools.py", line 710>)
            1326 MAKE_FUNCTION            5 (defaults, annotations)

 710        1328 UNPACK_SEQUENCE          0
            1332 CALL                     0
            1340 CACHE

 711        1342 STORE_NAME              68 (write_in_notepad)

 833        1344 LOAD_CONST               0 (0)
            1346 LOAD_CONST               1 (None)
            1348 IMPORT_NAME             24 (pyautogui)
            1350 STORE_NAME              24 (pyautogui)

 834        1352 LOAD_CONST              16 (True)
            1354 LOAD_NAME               24 (pyautogui)
            1356 STORE_ATTR              44 (FAILSAFE)

 835        1366 LOAD_CONST              15 (0.1)
            1368 LOAD_NAME               24 (pyautogui)
            1370 STORE_ATTR              43 (PAUSE)

 839        1380 PUSH_NULL
            1382 LOAD_NAME                2 (function_tool)
            1384 UNPACK_SEQUENCE          0
            1388 CALL                     0
            1396 CACHE

 840        1398 LOAD_CONST              66 ('app_name')
            1400 LOAD_NAME               45 (str)
            1402 LOAD_CONST              18 ('return')
            1404 LOAD_NAME               45 (str)
            1406 BUILD_TUPLE              4
            1408 LOAD_CONST              67 (<code object open_app at 0x000001EFF4BA7480, file "tools.py", line 839>)
            1410 MAKE_FUNCTION            4 (annotations)

 839        1412 UNPACK_SEQUENCE          0
            1416 CALL                     0
            1424 CACHE

 840        1426 STORE_NAME              69 (open_app)

 883        1428 PUSH_NULL
            1430 LOAD_NAME                2 (function_tool)
            1432 UNPACK_SEQUENCE          0
            1436 CALL                     0
            1444 CACHE

 884        1446 LOAD_CONST              68 ('key')
            1448 LOAD_NAME               45 (str)
            1450 LOAD_CONST              18 ('return')
            1452 LOAD_NAME               45 (str)
            1454 BUILD_TUPLE              4
            1456 LOAD_CONST              69 (<code object press_key at 0x000001EFF4C456D0, file "tools.py", line 883>)
            1458 MAKE_FUNCTION            4 (annotations)

 883        1460 UNPACK_SEQUENCE          0
            1464 CALL                     0
            1472 CACHE

 884        1474 STORE_NAME              70 (press_key)

 918        1476 PUSH_NULL
            1478 LOAD_NAME                2 (function_tool)
            1480 UNPACK_SEQUENCE          0
            1484 CALL                     0
            1492 CACHE

 919        1494 LOAD_CONST              18 ('return')
            1496 LOAD_NAME               45 (str)
            1498 BUILD_TUPLE              2
            1500 LOAD_CONST              70 (<code object get_system_info at 0x000001EFF4B5DE50, file "tools.py", line 918>)
            1502 MAKE_FUNCTION            4 (annotations)

 918        1504 UNPACK_SEQUENCE          0
            1508 CALL                     0
            1516 CACHE

 919        1518 STORE_NAME              71 (get_system_info)

 985        1520 PUSH_NULL
            1522 LOAD_NAME                2 (function_tool)
            1524 UNPACK_SEQUENCE          0
            1528 CALL                     0
            1536 CACHE

 986        1538 LOAD_CONST              43 ('message')
            1540 LOAD_NAME               45 (str)
            1542 LOAD_CONST              18 ('return')
            1544 LOAD_NAME               45 (str)
            1546 BUILD_TUPLE              4
            1548 LOAD_CONST              71 (<code object type_user_message_auto at 0x000001EFF69A8C00, file "tools.py", line 985>)
            1550 MAKE_FUNCTION            4 (annotations)

 985        1552 UNPACK_SEQUENCE          0
            1556 CALL                     0
            1564 CACHE

 986        1566 STORE_NAME              72 (type_user_message_auto)

1011        1568 LOAD_CONST               0 (0)
            1570 LOAD_CONST               1 (None)
            1572 IMPORT_NAME             73 (numpy)
            1574 STORE_NAME              74 (np)

1012        1576 LOAD_CONST               0 (0)
            1578 LOAD_CONST               1 (None)
            1580 IMPORT_NAME             75 (cv2)
            1582 STORE_NAME              75 (cv2)

1013        1584 LOAD_CONST               0 (0)
            1586 LOAD_CONST              72 (('mss',))
            1588 IMPORT_NAME             76 (mss)
            1590 IMPORT_FROM             76 (mss)
            1592 STORE_NAME              76 (mss)
            1594 POP_TOP

1014        1596 LOAD_CONST               0 (0)
            1598 LOAD_CONST               1 (None)
            1600 IMPORT_NAME             77 (pytesseract)
            1602 STORE_NAME              77 (pytesseract)

1015        1604 LOAD_CONST               0 (0)
            1606 LOAD_CONST               1 (None)
            1608 IMPORT_NAME             78 (win32gui)
            1610 STORE_NAME              78 (win32gui)

1016        1612 LOAD_CONST               0 (0)
            1614 LOAD_CONST               1 (None)
            1616 IMPORT_NAME             11 (time)
            1618 STORE_NAME              11 (time)

1017        1620 LOAD_CONST               0 (0)
            1622 LOAD_CONST              73 (('Image',))
            1624 IMPORT_NAME             79 (PIL)
            1626 IMPORT_FROM             80 (Image)
            1628 STORE_NAME              80 (Image)
            1630 POP_TOP

1018        1632 LOAD_CONST               0 (0)
            1634 LOAD_CONST               1 (None)
            1636 IMPORT_NAME             12 (os)
            1638 STORE_NAME              12 (os)

1019        1640 LOAD_CONST               0 (0)
            1642 LOAD_CONST               5 (('datetime',))
            1644 IMPORT_NAME             17 (datetime)
            1646 IMPORT_FROM             17 (datetime)
            1648 STORE_NAME              17 (datetime)
            1650 POP_TOP

1020        1652 LOAD_CONST               0 (0)
            1654 LOAD_CONST               1 (None)
            1656 IMPORT_NAME             25 (re)
            1658 STORE_NAME              25 (re)

1021        1660 LOAD_CONST               0 (0)
            1662 LOAD_CONST              74 (('wraps',))
            1664 IMPORT_NAME             81 (functools)
            1666 IMPORT_FROM             82 (wraps)
            1668 STORE_NAME              82 (wraps)
            1670 POP_TOP

1022        1672 LOAD_CONST               0 (0)
            1674 LOAD_CONST               1 (None)
            1676 IMPORT_NAME             28 (json)
            1678 STORE_NAME              28 (json)

1024        1680 LOAD_CONST              75 ('C:\\Program Files\\Tesseract-OCR\\tesseract.exe')
            1682 LOAD_NAME               77 (pytesseract)
            1684 LOAD_ATTR               77 (NULL|self + __name__)

1025        1704 LOAD_CONST              76 ('2')
            1706 LOAD_NAME               12 (os)
            1708 LOAD_ATTR               84 (GMAIL_PASSWORD)
            1728 UNPACK_SEQUENCE          0
            1732 CALL                     0
            1740 CACHE
            1742 STORE_NAME              85 (sct)

1029        1744 LOAD_NAME               85 (sct)
            1746 LOAD_ATTR               86 (PAUSE)
            1766 CACHE
            1768 STORE_NAME              87 (monitor)

1033        1770 PUSH_NULL
            1772 LOAD_NAME                2 (function_tool)
            1774 UNPACK_SEQUENCE          0
            1778 CALL                     0
            1786 CACHE

1034        1788 LOAD_CONST              79 ('target_text')
            1790 LOAD_NAME               45 (str)
            1792 LOAD_CONST              18 ('return')
            1794 LOAD_NAME               45 (str)
            1796 BUILD_TUPLE              4
            1798 LOAD_CONST              80 (<code object click_on_text at 0x000001EFF4A476D0, file "tools.py", line 1033>)
            1800 MAKE_FUNCTION            4 (annotations)

1033        1802 UNPACK_SEQUENCE          0
            1806 CALL                     0
            1814 CACHE

1034        1816 STORE_NAME              88 (click_on_text)

1111        1818 PUSH_NULL
            1820 LOAD_NAME                2 (function_tool)
            1822 UNPACK_SEQUENCE          0
            1826 CALL                     0
            1834 CACHE

1112        1836 LOAD_CONST              18 ('return')
            1838 LOAD_NAME               45 (str)
            1840 BUILD_TUPLE              2
            1842 LOAD_CONST              81 (<code object scan_system_for_viruses at 0x000001EFF4B63DF0, file "tools.py", line 1111>)
            1844 MAKE_FUNCTION            4 (annotations)

1111        1846 UNPACK_SEQUENCE          0
            1850 CALL                     0
            1858 CACHE

1112        1860 STORE_NAME              89 (scan_system_for_viruses)

1167        1862 PUSH_NULL
            1864 LOAD_NAME                2 (function_tool)
            1866 UNPACK_SEQUENCE          0
            1870 CALL                     0
            1878 CACHE

1168        1880 LOAD_CONST              82 ('enable')
            1882 LOAD_NAME               46 (bool)
            1884 LOAD_CONST              18 ('return')
            1886 LOAD_NAME               45 (str)
            1888 BUILD_TUPLE              4
            1890 LOAD_CONST              83 (<code object enable_camera_analysis at 0x000001EFF3F23AB0, file "tools.py", line 1167>)
            1892 MAKE_FUNCTION            4 (annotations)

1167        1894 UNPACK_SEQUENCE          0
            1898 CALL                     0
            1906 CACHE

1168        1908 STORE_NAME              90 (enable_camera_analysis)

1188        1910 PUSH_NULL
            1912 LOAD_NAME                2 (function_tool)
            1914 UNPACK_SEQUENCE          0
            1918 CALL                     0
            1926 CACHE

1189        1928 LOAD_CONST              84 ('prompt')
            1930 LOAD_NAME               45 (str)
            1932 LOAD_CONST              18 ('return')
            1934 LOAD_NAME               45 (str)
            1936 BUILD_TUPLE              4
            1938 LOAD_CONST              85 (<code object analyze_visual_scene at 0x000001EFF4BAEBF0, file "tools.py", line 1188>)
            1940 MAKE_FUNCTION            4 (annotations)

1188        1942 UNPACK_SEQUENCE          0
            1946 CALL                     0
            1954 CACHE

1189        1956 STORE_NAME              91 (analyze_visual_scene)

1216        1958 LOAD_CONST               0 (0)
            1960 LOAD_CONST               1 (None)
            1962 IMPORT_NAME             92 (pandas)
            1964 STORE_NAME              93 (pd)

1217        1966 LOAD_CONST               0 (0)
            1968 LOAD_CONST               1 (None)
            1970 IMPORT_NAME             73 (numpy)
            1972 STORE_NAME              74 (np)

1218        1974 LOAD_CONST               0 (0)
            1976 LOAD_CONST               1 (None)
            1978 IMPORT_NAME             94 (matplotlib.pyplot)
            1980 IMPORT_FROM             95 (pyplot)
            1982 STORE_NAME              96 (plt)
            1984 POP_TOP

1219        1986 LOAD_CONST               0 (0)
            1988 LOAD_CONST               1 (None)
            1990 IMPORT_NAME             97 (seaborn)
            1992 STORE_NAME              98 (sns)

1220        1994 LOAD_CONST               0 (0)
            1996 LOAD_CONST               5 (('datetime',))
            1998 IMPORT_NAME             17 (datetime)
            2000 IMPORT_FROM             17 (datetime)
            2002 STORE_NAME              17 (datetime)
            2004 POP_TOP

1221        2006 LOAD_CONST               0 (0)
            2008 LOAD_CONST               1 (None)
            2010 IMPORT_NAME             99 (warnings)
            2012 STORE_NAME              99 (warnings)

1222        2014 LOAD_CONST               0 (0)
            2016 LOAD_CONST              86 (('Dict', 'List', 'Any', 'Optional'))
            2018 IMPORT_NAME             14 (typing)
            2020 IMPORT_FROM             31 (Dict)
            2022 STORE_NAME              31 (Dict)
            2024 IMPORT_FROM             30 (List)
            2026 STORE_NAME              30 (List)
            2028 IMPORT_FROM            100 (Any)
            2030 STORE_NAME             100 (Any)
            2032 IMPORT_FROM             15 (Optional)
            2034 STORE_NAME              15 (Optional)
            2036 POP_TOP

1223        2038 LOAD_CONST               0 (0)
            2040 LOAD_CONST               1 (None)
            2042 IMPORT_NAME             28 (json)
            2044 STORE_NAME              28 (json)

1224        2046 LOAD_CONST               0 (0)
            2048 LOAD_CONST              87 (('Path',))
            2050 IMPORT_NAME            101 (pathlib)
            2052 IMPORT_FROM            102 (Path)
            2054 STORE_NAME             102 (Path)
            2056 POP_TOP

1225        2058 LOAD_CONST               0 (0)
            2060 LOAD_CONST              88 (('stats',))
            2062 IMPORT_NAME            103 (scipy)
            2064 IMPORT_FROM            104 (stats)
            2066 STORE_NAME             104 (stats)
            2068 POP_TOP

1226        2070 LOAD_CONST               0 (0)
            2072 LOAD_CONST              89 (('StandardScaler',))
            2074 IMPORT_NAME            105 (sklearn.preprocessing)
            2076 IMPORT_FROM            106 (StandardScaler)
            2078 STORE_NAME             106 (StandardScaler)
            2080 POP_TOP

1227        2082 LOAD_CONST               0 (0)
            2084 LOAD_CONST              90 (('KMeans',))
            2086 IMPORT_NAME            107 (sklearn.cluster)
            2088 IMPORT_FROM            108 (KMeans)
            2090 STORE_NAME             108 (KMeans)
            2092 POP_TOP

1228        2094 LOAD_CONST               0 (0)
            2096 LOAD_CONST              91 (('PCA',))
            2098 IMPORT_NAME            109 (sklearn.decomposition)
            2100 IMPORT_FROM            110 (PCA)
            2102 STORE_NAME             110 (PCA)
            2104 POP_TOP

1229        2106 LOAD_CONST               0 (0)
            2108 LOAD_CONST               1 (None)
            2110 IMPORT_NAME            111 (tkinter)
            2112 STORE_NAME             112 (tk)

1230        2114 LOAD_CONST               0 (0)
            2116 LOAD_CONST              92 (('filedialog',))
            2118 IMPORT_NAME            111 (tkinter)
            2120 IMPORT_FROM            113 (filedialog)
            2122 STORE_NAME             113 (filedialog)
            2124 POP_TOP

1231        2126 LOAD_CONST               0 (0)
            2128 LOAD_CONST               1 (None)
            2130 IMPORT_NAME             12 (os)
            2132 STORE_NAME              12 (os)

1232        2134 LOAD_CONST               0 (0)
            2136 LOAD_CONST               1 (None)
            2138 IMPORT_NAME             13 (webbrowser)
            2140 STORE_NAME              13 (webbrowser)

1233        2142 LOAD_CONST               0 (0)
            2144 LOAD_CONST               1 (None)
            2146 IMPORT_NAME             32 (asyncio)
            2148 STORE_NAME              32 (asyncio)

1234        2150 LOAD_CONST               0 (0)
            2152 LOAD_CONST              93 (('ThreadPoolExecutor', 'ProcessPoolExecutor'))
            2154 IMPORT_NAME            114 (concurrent.futures)
            2156 IMPORT_FROM            115 (ThreadPoolExecutor)
            2158 STORE_NAME             115 (ThreadPoolExecutor)
            2160 IMPORT_FROM            116 (ProcessPoolExecutor)
            2162 STORE_NAME             116 (ProcessPoolExecutor)
            2164 POP_TOP

1235        2166 LOAD_CONST               0 (0)
            2168 LOAD_CONST               1 (None)
            2170 IMPORT_NAME            117 (io)
            2172 STORE_NAME             117 (io)

1236        2174 LOAD_CONST               0 (0)
            2176 LOAD_CONST               1 (None)
            2178 IMPORT_NAME            118 (base64)
            2180 STORE_NAME             118 (base64)

1237        2182 LOAD_CONST               0 (0)
            2184 LOAD_CONST               1 (None)
            2186 IMPORT_NAME            119 (multiprocessing)
            2188 STORE_NAME             120 (mp)

1238        2190 LOAD_CONST               0 (0)
            2192 LOAD_CONST              94 (('partial',))
            2194 IMPORT_NAME             81 (functools)
            2196 IMPORT_FROM            121 (partial)
            2198 STORE_NAME             121 (partial)
            2200 POP_TOP

1240        2202 PUSH_NULL
            2204 LOAD_NAME               99 (warnings)
            2206 LOAD_ATTR              122 (sqlite3)
            2226 CACHE
            2228 CACHE
            2230 CACHE
            2232 POP_TOP

1243        2234 PUSH_NULL
            2236 LOAD_NAME               93 (pd)
            2238 LOAD_ATTR              123 (NULL|self + sqlite3)
            2258 CACHE
            2260 CACHE
            2262 CACHE
            2264 CACHE
            2266 POP_TOP

1244        2268 PUSH_NULL
            2270 LOAD_NAME               93 (pd)
            2272 LOAD_ATTR              123 (NULL|self + sqlite3)
            2292 CACHE
            2294 CACHE
            2296 CACHE
            2298 CACHE
            2300 POP_TOP

1247        2302 LOAD_CONST               1 (None)
            2304 STORE_GLOBAL           124 (GLOBAL_DF)

1249        2306 LOAD_CONST               1 (None)

1250        2308 BUILD_MAP                0

1251        2310 BUILD_MAP                0

1252        2312 BUILD_MAP                0

1253        2314 BUILD_MAP                0

1254        2316 BUILD_MAP                0

1248        2318 LOAD_CONST              98 (('data', 'insights', 'metadata', 'analysis_results', 'visualizations', 'business_context'))
            2320 BUILD_CONST_KEY_MAP      6
            2322 STORE_GLOBAL           125 (ANALYSIS_CACHE)

1258        2324 PUSH_NULL
            2326 LOAD_NAME                2 (function_tool)
            2328 UNPACK_SEQUENCE          0
            2332 CALL                     0
            2340 CACHE

1259        2342 LOAD_CONST              18 ('return')
            2344 LOAD_NAME               45 (str)
            2346 BUILD_TUPLE              2
            2348 LOAD_CONST              99 (<code object _open_file_dialog at 0x000001EFF4C44050, file "tools.py", line 1258>)
            2350 MAKE_FUNCTION            4 (annotations)

1258        2352 UNPACK_SEQUENCE          0
            2356 CALL                     0
            2364 CACHE

1259        2366 STORE_NAME             126 (_open_file_dialog)

1284        2368 LOAD_CONST             100 ('file_path')
            2370 LOAD_NAME               45 (str)
            2372 LOAD_CONST              18 ('return')
            2374 LOAD_NAME               93 (pd)
            2376 LOAD_ATTR              127 (NULL|self + TABLE_NAME)
            2396 LOAD_NAME               93 (pd)
            2398 LOAD_ATTR              127 (NULL|self + TABLE_NAME)
            2418 BINARY_SUBSCR
            2422 CACHE
            2424 CACHE
            2426 CACHE
            2428 BUILD_TUPLE              4
            2430 LOAD_CONST             103 (<code object _detect_column_types_fast at 0x000001EFF4BB3650, file "tools.py", line 1326>)
            2432 MAKE_FUNCTION            4 (annotations)
            2434 STORE_NAME             129 (_detect_column_types_fast)

1364        2436 LOAD_CONST             102 ('df')
            2438 LOAD_NAME               93 (pd)
            2440 LOAD_ATTR              127 (NULL|self + TABLE_NAME)
            2460 BINARY_SUBSCR
            2464 CACHE
            2466 CACHE
            2468 CACHE
            2470 BUILD_TUPLE              4
            2472 LOAD_CONST             104 (<code object _detect_business_context_fast at 0x000001EFF4B62CA0, file "tools.py", line 1364>)
            2474 MAKE_FUNCTION            4 (annotations)
            2476 STORE_NAME             130 (_detect_business_context_fast)

1420        2478 LOAD_CONST             102 ('df')
            2480 LOAD_NAME               93 (pd)
            2482 LOAD_ATTR              127 (NULL|self + TABLE_NAME)
            2502 BINARY_SUBSCR
            2506 CACHE
            2508 CACHE
            2510 CACHE
            2512 BUILD_TUPLE              4
            2514 LOAD_CONST             105 (<code object _analyze_data_quality_fast at 0x000001EFF4B63190, file "tools.py", line 1420>)
            2516 MAKE_FUNCTION            4 (annotations)
            2518 STORE_NAME             131 (_analyze_data_quality_fast)

1439        2520 LOAD_CONST             102 ('df')
            2522 LOAD_NAME               93 (pd)
            2524 LOAD_ATTR              127 (NULL|self + TABLE_NAME)
            2544 LOAD_NAME              100 (Any)
            2546 BUILD_TUPLE              2
            2548 BINARY_SUBSCR
            2552 CACHE
            2554 CACHE
            2556 CACHE
            2558 BUILD_TUPLE              6
            2560 LOAD_CONST             107 (<code object _find_key_insights_fast at 0x000001EFF4A481D0, file "tools.py", line 1439>)
            2562 MAKE_FUNCTION            4 (annotations)
            2564 STORE_NAME             132 (_find_key_insights_fast)

1488        2566 LOAD_CONST             102 ('df')
            2568 LOAD_NAME               93 (pd)
            2570 LOAD_ATTR              127 (NULL|self + TABLE_NAME)
            2590 LOAD_NAME               30 (List)
            2592 LOAD_NAME               45 (str)
            2594 BINARY_SUBSCR
            2598 CACHE
            2600 CACHE
            2602 CACHE
            2604 BUILD_TUPLE              8
            2606 LOAD_CONST             109 (<code object _generate_business_insights_fast at 0x000001EFF4B66640, file "tools.py", line 1488>)
            2608 MAKE_FUNCTION            4 (annotations)
            2610 STORE_NAME             133 (_generate_business_insights_fast)

1529        2612 LOAD_CONST             102 ('df')
            2614 LOAD_NAME               93 (pd)
            2616 LOAD_ATTR              127 (NULL|self + TABLE_NAME)
            2636 LOAD_NAME               45 (str)
            2638 BUILD_TUPLE              2
            2640 BINARY_SUBSCR
            2644 CACHE
            2646 CACHE
            2648 CACHE
            2650 BUILD_TUPLE              6
            2652 LOAD_CONST             110 (<code object _create_visualizations_fast at 0x000001EFF4A47150, file "tools.py", line 1529>)
            2654 MAKE_FUNCTION            4 (annotations)
            2656 STORE_NAME             134 (_create_visualizations_fast)

1576        2658 LOAD_CONST             102 ('df')
            2660 LOAD_NAME               93 (pd)
            2662 LOAD_ATTR              127 (NULL|self + TABLE_NAME)
            2682 LOAD_NAME               31 (Dict)
            2684 LOAD_CONST              18 ('return')
            2686 LOAD_NAME               45 (str)
            2688 BUILD_TUPLE             10
            2690 LOAD_CONST             112 (<code object _generate_html_report_fast at 0x000001EFF4B64B30, file "tools.py", line 1576>)
            2692 MAKE_FUNCTION            4 (annotations)
            2694 STORE_NAME             135 (_generate_html_report_fast)

2110        2696 LOAD_CONST              18 ('return')
            2698 LOAD_NAME               45 (str)
            2700 BUILD_TUPLE              2
            2702 LOAD_CONST             113 (<code object reset_analysis at 0x000001EFF69C2BF0, file "tools.py", line 2110>)
            2704 MAKE_FUNCTION            4 (annotations)
            2706 STORE_NAME             136 (reset_analysis)

2127        2708 PUSH_NULL
            2710 LOAD_NAME                2 (function_tool)
            2712 UNPACK_SEQUENCE          0
            2716 CALL                     0
            2724 CACHE

2128        2726 LOAD_CONST              18 ('return')
            2728 LOAD_NAME               45 (str)
            2730 BUILD_TUPLE              2
            2732 LOAD_CONST             114 (<code object load_and_analyze_excel at 0x000001EFF4A47C50, file "tools.py", line 2127>)
            2734 MAKE_FUNCTION            4 (annotations)

2127        2736 UNPACK_SEQUENCE          0
            2740 CALL                     0
            2748 CACHE

2128        2750 STORE_NAME             137 (load_and_analyze_excel)

2189        2752 PUSH_NULL
            2754 LOAD_NAME                2 (function_tool)
            2756 UNPACK_SEQUENCE          0
            2760 CALL                     0
            2768 CACHE

2190        2770 LOAD_CONST              18 ('return')
            2772 LOAD_NAME               45 (str)
            2774 BUILD_TUPLE              2
            2776 LOAD_CONST             115 (<code object get_analysis_report at 0x000001EFF4BAFBD0, file "tools.py", line 2189>)
            2778 MAKE_FUNCTION            4 (annotations)

2189        2780 UNPACK_SEQUENCE          0
            2784 CALL                     0
            2792 CACHE

2190        2794 STORE_NAME             138 (get_analysis_report)

2219        2796 PUSH_NULL
            2798 LOAD_NAME                2 (function_tool)
            2800 UNPACK_SEQUENCE          0
            2804 CALL                     0
            2812 CACHE

2220        2814 LOAD_CONST              18 ('return')
            2816 LOAD_NAME               45 (str)
            2818 BUILD_TUPLE              2
            2820 LOAD_CONST             116 (<code object get_analysis_status at 0x000001EFF4B44450, file "tools.py", line 2219>)
            2822 MAKE_FUNCTION            4 (annotations)

2219        2824 UNPACK_SEQUENCE          0
            2828 CALL                     0
            2836 CACHE

2220        2838 STORE_NAME             139 (get_analysis_status)

2247        2840 PUSH_NULL
            2842 LOAD_NAME                2 (function_tool)
            2844 UNPACK_SEQUENCE          0
            2848 CALL                     0
            2856 CACHE

2248        2858 LOAD_CONST              18 ('return')
            2860 LOAD_NAME               45 (str)
            2862 BUILD_TUPLE              2
            2864 LOAD_CONST             117 (<code object create_visualizations_chart at 0x000001EFF4BEBF90, file "tools.py", line 2247>)
            2866 MAKE_FUNCTION            4 (annotations)

2247        2868 UNPACK_SEQUENCE          0
            2872 CALL                     0
            2880 CACHE

2248        2882 STORE_NAME             140 (create_visualizations_chart)

2282        2884 PUSH_NULL
            2886 LOAD_NAME                2 (function_tool)
            2888 UNPACK_SEQUENCE          0
            2892 CALL                     0
            2900 CACHE

2283        2902 LOAD_CONST              18 ('return')
            2904 LOAD_NAME               45 (str)
            2906 BUILD_TUPLE              2
            2908 LOAD_CONST             118 (<code object get_top_insights at 0x000001EFF69FC9F0, file "tools.py", line 2282>)
            2910 MAKE_FUNCTION            4 (annotations)

2282        2912 UNPACK_SEQUENCE          0
            2916 CALL                     0
            2924 CACHE

2283        2926 STORE_NAME             141 (get_top_insights)

2301        2928 PUSH_NULL
            2930 LOAD_NAME                2 (function_tool)
            2932 UNPACK_SEQUENCE          0
            2936 CALL                     0
            2944 CACHE

2302        2946 LOAD_CONST              18 ('return')
            2948 LOAD_NAME               45 (str)
            2950 BUILD_TUPLE              2
            2952 LOAD_CONST             119 (<code object get_data_summary at 0x000001EFF4B654C0, file "tools.py", line 2301>)
            2954 MAKE_FUNCTION            4 (annotations)

2301        2956 UNPACK_SEQUENCE          0
            2960 CALL                     0
            2968 CACHE

2302        2970 STORE_NAME             142 (get_data_summary)

2329        2972 PUSH_NULL
            2974 LOAD_NAME                2 (function_tool)
            2976 UNPACK_SEQUENCE          0
            2980 CALL                     0
            2988 CACHE

2330        2990 LOAD_CONST             175 (('json',))
            2992 LOAD_CONST             121 ('format_type')
            2994 LOAD_NAME               45 (str)
            2996 LOAD_CONST              18 ('return')
            2998 LOAD_NAME               45 (str)
            3000 BUILD_TUPLE              4
            3002 LOAD_CONST             122 (<code object export_results at 0x000001EFF4B65780, file "tools.py", line 2329>)
            3004 MAKE_FUNCTION            5 (defaults, annotations)

2329        3006 UNPACK_SEQUENCE          0
            3010 CALL                     0
            3018 CACHE

2330        3020 STORE_NAME             143 (export_results)

2366        3022 PUSH_NULL
            3024 LOAD_NAME                2 (function_tool)
            3026 UNPACK_SEQUENCE          0
            3030 CALL                     0
            3038 CACHE

2367        3040 LOAD_CONST              18 ('return')
            3042 LOAD_NAME               45 (str)
            3044 BUILD_TUPLE              2
            3046 LOAD_CONST             123 (<code object full_analysis_with_report at 0x000001EFF698AD80, file "tools.py", line 2366>)
            3048 MAKE_FUNCTION            4 (annotations)

2366        3050 UNPACK_SEQUENCE          0
            3054 CALL                     0
            3062 CACHE

2367        3064 STORE_NAME             144 (full_analysis_with_report)

2387        3066 LOAD_CONST               0 (0)
            3068 LOAD_CONST               1 (None)
            3070 IMPORT_NAME             92 (pandas)
            3072 STORE_NAME              93 (pd)

2388        3074 LOAD_CONST               0 (0)
            3076 LOAD_CONST               1 (None)
            3078 IMPORT_NAME             94 (matplotlib.pyplot)
            3080 IMPORT_FROM             95 (pyplot)
            3082 STORE_NAME              96 (plt)
            3084 POP_TOP

2389        3086 LOAD_CONST               0 (0)
            3088 LOAD_CONST               1 (None)
            3090 IMPORT_NAME             97 (seaborn)
            3092 STORE_NAME              98 (sns)

2390        3094 LOAD_CONST               0 (0)
            3096 LOAD_CONST               1 (None)
            3098 IMPORT_NAME             73 (numpy)
            3100 STORE_NAME              74 (np)

2391        3102 LOAD_CONST               0 (0)
            3104 LOAD_CONST               1 (None)
            3106 IMPORT_NAME            111 (tkinter)
            3108 STORE_NAME             112 (tk)

2392        3110 LOAD_CONST               0 (0)
            3112 LOAD_CONST             124 (('filedialog', 'messagebox', 'simpledialog'))
            3114 IMPORT_NAME            111 (tkinter)
            3116 IMPORT_FROM            113 (filedialog)
            3118 STORE_NAME             113 (filedialog)
            3120 IMPORT_FROM            145 (messagebox)
            3122 STORE_NAME             145 (messagebox)
            3124 IMPORT_FROM            146 (simpledialog)
            3126 STORE_NAME             146 (simpledialog)
            3128 POP_TOP

2393        3130 LOAD_CONST               0 (0)
            3132 LOAD_CONST               1 (None)
            3134 IMPORT_NAME             12 (os)
            3136 STORE_NAME              12 (os)

2394        3138 LOAD_CONST               0 (0)
            3140 LOAD_CONST               1 (None)
            3142 IMPORT_NAME            147 (sys)
            3144 STORE_NAME             147 (sys)

2395        3146 LOAD_CONST               0 (0)
            3148 LOAD_CONST               1 (None)
            3150 IMPORT_NAME             99 (warnings)
            3152 STORE_NAME              99 (warnings)

2396        3154 LOAD_CONST               0 (0)
            3156 LOAD_CONST               5 (('datetime',))
            3158 IMPORT_NAME             17 (datetime)
            3160 IMPORT_FROM             17 (datetime)
            3162 STORE_NAME              17 (datetime)
            3164 POP_TOP

2397        3166 LOAD_CONST               0 (0)
            3168 LOAD_CONST              88 (('stats',))
            3170 IMPORT_NAME            103 (scipy)
            3172 IMPORT_FROM            104 (stats)
            3174 STORE_NAME             104 (stats)
            3176 POP_TOP

2398        3178 LOAD_CONST               0 (0)
            3180 LOAD_CONST               1 (None)
            3182 IMPORT_NAME            148 (matplotlib.patches)
            3184 IMPORT_FROM            149 (patches)
            3186 STORE_NAME             150 (mpatches)
            3188 POP_TOP

2399        3190 LOAD_CONST               0 (0)
            3192 LOAD_CONST               1 (None)
            3194 IMPORT_NAME             32 (asyncio)
            3196 STORE_NAME              32 (asyncio)

2401        3198 LOAD_CONST               0 (0)
            3200 LOAD_CONST               1 (None)
            3202 IMPORT_NAME            151 (matplotlib)
            3204 STORE_NAME             151 (matplotlib)

2402        3206 PUSH_NULL
            3208 LOAD_NAME              151 (matplotlib)
            3210 LOAD_ATTR              152 (mss)
            3230 CACHE
            3232 CACHE
            3234 CACHE
            3236 POP_TOP

2404        3238 PUSH_NULL
            3240 LOAD_NAME               99 (warnings)
            3242 LOAD_ATTR              122 (sqlite3)
            3262 CACHE
            3264 CACHE
            3266 CACHE
            3268 POP_TOP

2406        3270 LOAD_CONST             126 (<code object _create_file_dialog at 0x000001EFF4B93350, file "tools.py", line 2406>)
            3272 MAKE_FUNCTION            0
            3274 STORE_NAME             153 (_create_file_dialog)

2433        3276 LOAD_CONST             176 ((None, 'string'))
            3278 LOAD_CONST             128 (<code object _create_input_dialog at 0x000001EFF41E29F0, file "tools.py", line 2433>)
            3280 MAKE_FUNCTION            1 (defaults)
            3282 STORE_NAME             154 (_create_input_dialog)

2457        3284 LOAD_CONST             129 (<code object _load_data_smart at 0x000001EFF4B65D50, file "tools.py", line 2457>)
            3286 MAKE_FUNCTION            0
            3288 STORE_NAME             155 (_load_data_smart)

2491        3290 LOAD_CONST             130 (<code object _analyze_data at 0x000001EFF4BFE690, file "tools.py", line 2491>)
            3292 MAKE_FUNCTION            0
            3294 STORE_NAME             156 (_analyze_data)

2524        3296 LOAD_CONST             131 (<code object _create_advanced_styling at 0x000001EFF4BF13F0, file "tools.py", line 2524>)
            3298 MAKE_FUNCTION            0
            3300 STORE_NAME             157 (_create_advanced_styling)

2556        3302 LOAD_CONST             132 (<code object _add_statistics at 0x000001EFF4BF1780, file "tools.py", line 2556>)
            3304 MAKE_FUNCTION            0
            3306 STORE_NAME             158 (_add_statistics)

2577        3308 LOAD_CONST             133 (<code object _create_graph_advanced at 0x000001EFF4BF7860, file "tools.py", line 2577>)
            3310 MAKE_FUNCTION            0
            3312 STORE_NAME             159 (_create_graph_advanced)

2710        3314 NOP

2711        3316 NOP

2712        3318 NOP

2713        3320 NOP

2714        3322 NOP

2715        3324 NOP

2716        3326 NOP

2717        3328 NOP

2718        3330 NOP

2719        3332 NOP

2720        3334 NOP

2721        3336 NOP

2722        3338 NOP

2723        3340 NOP

2724        3342 NOP

2709        3344 LOAD_CONST             177 ((None, 'line', None, None, 'full', None, None, None, 'default', 'auto', True, True, 'png', 300, (12, 8)))
            3346 LOAD_CONST             100 ('file_path')

2710        3348 LOAD_NAME               45 (str)

2709        3350 LOAD_CONST             141 ('graph_type')

2711        3352 LOAD_NAME               45 (str)

2709        3354 LOAD_CONST             142 ('x_column')

2712        3356 LOAD_NAME               45 (str)

2709        3358 LOAD_CONST             143 ('y_column')

2713        3360 LOAD_NAME               45 (str)

2709        3362 LOAD_CONST             144 ('data_range')

2714        3364 LOAD_NAME               45 (str)

2709        3366 LOAD_CONST             145 ('data_limit')

2715        3368 LOAD_NAME               54 (int)

2709        3370 LOAD_CONST              62 ('title')

2716        3372 LOAD_NAME               45 (str)

2709        3374 LOAD_CONST             146 ('save_name')

2717        3376 LOAD_NAME               45 (str)

2709        3378 LOAD_CONST             147 ('style')

2718        3380 LOAD_NAME               45 (str)

2709        3382 LOAD_CONST             148 ('color_scheme')

2719        3384 LOAD_NAME               45 (str)

2709        3386 LOAD_CONST             149 ('interactive_mode')

2720        3388 LOAD_NAME               46 (bool)

2709        3390 LOAD_CONST             150 ('show_stats')

2721        3392 LOAD_NAME               46 (bool)

2709        3394 LOAD_CONST             151 ('export_format')

2722        3396 LOAD_NAME               45 (str)

2709        3398 LOAD_CONST             152 ('dpi')

2723        3400 LOAD_NAME               54 (int)

2709        3402 LOAD_CONST             153 ('figsize')

2724        3404 LOAD_NAME              160 (tuple)

2709        3406 LOAD_CONST              18 ('return')

2725        3408 LOAD_NAME               45 (str)

2709        3410 BUILD_TUPLE             32
            3412 LOAD_CONST             154 (<code object create_advanced_graph at 0x000001EFF4C1DE50, file "tools.py", line 2709>)
            3414 MAKE_FUNCTION            5 (defaults, annotations)
            3416 STORE_NAME             161 (create_advanced_graph)

2985        3418 PUSH_NULL
            3420 LOAD_NAME                2 (function_tool)
            3422 UNPACK_SEQUENCE          0
            3426 CALL                     0
            3434 CACHE

2987        3436 NOP

2988        3438 NOP

2989        3440 NOP

2990        3442 NOP

2991        3444 NOP

2992        3446 NOP

2993        3448 NOP

2994        3450 NOP

2995        3452 NOP

2996        3454 NOP

2997        3456 NOP

2998        3458 NOP

2999        3460 NOP

2986        3462 LOAD_CONST             178 (('line', None, None, 'full', None, None, None, 'default', 'auto', False, True, 'png', 300))
            3464 LOAD_CONST             141 ('graph_type')

2987        3466 LOAD_NAME               45 (str)

2986        3468 LOAD_CONST             142 ('x_column')

2988        3470 LOAD_NAME               45 (str)

2986        3472 LOAD_CONST             143 ('y_column')

2989        3474 LOAD_NAME               45 (str)

2986        3476 LOAD_CONST             144 ('data_range')

2990        3478 LOAD_NAME               45 (str)

2986        3480 LOAD_CONST             145 ('data_limit')

2991        3482 LOAD_NAME               54 (int)

2986        3484 LOAD_CONST              62 ('title')

2992        3486 LOAD_NAME               45 (str)

2986        3488 LOAD_CONST             146 ('save_name')

2993        3490 LOAD_NAME               45 (str)

2986        3492 LOAD_CONST             147 ('style')

2994        3494 LOAD_NAME               45 (str)

2986        3496 LOAD_CONST             148 ('color_scheme')

2995        3498 LOAD_NAME               45 (str)

2986        3500 LOAD_CONST             149 ('interactive_mode')

2996        3502 LOAD_NAME               46 (bool)

2986        3504 LOAD_CONST             150 ('show_stats')

2997        3506 LOAD_NAME               46 (bool)

2986        3508 LOAD_CONST             151 ('export_format')

2998        3510 LOAD_NAME               45 (str)

2986        3512 LOAD_CONST             152 ('dpi')

2999        3514 LOAD_NAME               54 (int)

2986        3516 LOAD_CONST              18 ('return')

3000        3518 LOAD_NAME               45 (str)

2986        3520 BUILD_TUPLE             28
            3522 LOAD_CONST             156 (<code object create_quick_advanced_graph at 0x000001EFF69FCD30, file "tools.py", line 2985>)
            3524 MAKE_FUNCTION            5 (defaults, annotations)

2985        3526 UNPACK_SEQUENCE          0
            3530 CALL                     0
            3538 CACHE

2986        3540 STORE_NAME             162 (create_quick_advanced_graph)

3075        3542 LOAD_CONST               0 (0)
            3544 LOAD_CONST               1 (None)
            3546 IMPORT_NAME            163 (nmap)
            3548 STORE_NAME             163 (nmap)

3076        3550 LOAD_CONST               0 (0)
            3552 LOAD_CONST               1 (None)
            3554 IMPORT_NAME             18 (psutil)
            3556 STORE_NAME              18 (psutil)

3077        3558 LOAD_CONST               0 (0)
            3560 LOAD_CONST               1 (None)
            3562 IMPORT_NAME            164 (socket)
            3564 STORE_NAME             164 (socket)

3078        3566 LOAD_CONST               0 (0)
            3568 LOAD_CONST             157 (('MacLookup',))
            3570 IMPORT_NAME            165 (mac_vendor_lookup)
            3572 IMPORT_FROM            166 (MacLookup)
            3574 STORE_NAME             166 (MacLookup)
            3576 POP_TOP

3079        3578 LOAD_CONST               0 (0)
            3580 LOAD_CONST               1 (None)
            3582 IMPORT_NAME             32 (asyncio)
            3584 STORE_NAME              32 (asyncio)

3080        3586 LOAD_CONST               0 (0)
            3588 LOAD_CONST               1 (None)
            3590 IMPORT_NAME            111 (tkinter)
            3592 STORE_NAME             112 (tk)

3081        3594 LOAD_CONST               0 (0)
            3596 LOAD_CONST             158 (('Listbox', 'Scrollbar', 'Frame', 'Label', 'Button', 'SINGLE', 'END', 'ttk'))
            3598 IMPORT_NAME            111 (tkinter)
            3600 IMPORT_FROM            167 (Listbox)
            3602 STORE_NAME             167 (Listbox)
            3604 IMPORT_FROM            168 (Scrollbar)
            3606 STORE_NAME             168 (Scrollbar)
            3608 IMPORT_FROM            169 (Frame)
            3610 STORE_NAME             169 (Frame)
            3612 IMPORT_FROM            170 (Label)
            3614 STORE_NAME             170 (Label)
            3616 IMPORT_FROM            171 (Button)
            3618 STORE_NAME             171 (Button)
            3620 IMPORT_FROM            172 (SINGLE)
            3622 STORE_NAME             172 (SINGLE)
            3624 IMPORT_FROM            173 (END)
            3626 STORE_NAME             173 (END)
            3628 IMPORT_FROM            174 (ttk)
            3630 STORE_NAME             174 (ttk)
            3632 POP_TOP

3082        3634 LOAD_CONST               0 (0)
            3636 LOAD_CONST             159 (('List', 'Literal'))
            3638 IMPORT_NAME             14 (typing)
            3640 IMPORT_FROM             30 (List)
            3642 STORE_NAME              30 (List)
            3644 IMPORT_FROM             16 (Literal)
            3646 STORE_NAME              16 (Literal)
            3648 POP_TOP

3083        3650 LOAD_CONST               0 (0)
            3652 LOAD_CONST               1 (None)
            3654 IMPORT_NAME            175 (threading)
            3656 STORE_NAME             175 (threading)

3084        3658 LOAD_CONST               0 (0)
            3660 LOAD_CONST               1 (None)
            3662 IMPORT_NAME            114 (concurrent.futures)
            3664 STORE_NAME             176 (concurrent)

3085        3666 LOAD_CONST               0 (0)
            3668 LOAD_CONST               1 (None)
            3670 IMPORT_NAME             11 (time)
            3672 STORE_NAME              11 (time)

3086        3674 LOAD_CONST               0 (0)
            3676 LOAD_CONST               1 (None)
            3678 IMPORT_NAME            177 (ipaddress)
            3680 STORE_NAME             177 (ipaddress)

3087        3682 LOAD_CONST               0 (0)
            3684 LOAD_CONST               1 (None)
            3686 IMPORT_NAME             28 (json)
            3688 STORE_NAME              28 (json)

3088        3690 LOAD_CONST               0 (0)
            3692 LOAD_CONST               1 (None)
            3694 IMPORT_NAME             12 (os)
            3696 STORE_NAME              12 (os)

3089        3698 LOAD_CONST               0 (0)
            3700 LOAD_CONST               1 (None)
            3702 IMPORT_NAME            178 (queue)
            3704 STORE_NAME             178 (queue)

3097        3706 BUILD_MAP                0
            3708 STORE_GLOBAL           179 (_mac_cache)

3098        3710 LOAD_CONST             160 ('mac_vendor_cache.json')
            3712 STORE_NAME             180 (_cache_file)

3101        3714 PUSH_NULL
            3716 LOAD_NAME              178 (queue)
            3718 LOAD_ATTR              181 (NULL|self + enable_camera_analysis)
            3738 CACHE
            3740 CACHE
            3742 STORE_NAME             182 (_ui_queue)

3103        3744 LOAD_CONST             161 (<code object _load_mac_cache at 0x000001EFF6A483B0, file "tools.py", line 3103>)
            3746 MAKE_FUNCTION            0
            3748 STORE_NAME             183 (_load_mac_cache)

3113        3750 LOAD_CONST             162 (<code object _save_mac_cache at 0x000001EFF6A4C030, file "tools.py", line 3113>)
            3752 MAKE_FUNCTION            0
            3754 STORE_NAME             184 (_save_mac_cache)

3121        3756 LOAD_CONST             163 ('mac_address')
            3758 LOAD_NAME               45 (str)
            3760 LOAD_CONST              18 ('return')
            3762 LOAD_NAME               45 (str)
            3764 BUILD_TUPLE              4
            3766 LOAD_CONST             164 (<code object _get_mac_vendor_fast at 0x000001EFF69FC510, file "tools.py", line 3121>)
            3768 MAKE_FUNCTION            4 (annotations)
            3770 STORE_NAME             185 (_get_mac_vendor_fast)

3138        3772 LOAD_CONST              18 ('return')
            3774 LOAD_NAME               45 (str)
            3776 BUILD_TUPLE              2
            3778 LOAD_CONST             165 (<code object _discover_network_ultra_fast at 0x000001EFF4BFA9D0, file "tools.py", line 3138>)
            3780 MAKE_FUNCTION            4 (annotations)
            3782 STORE_NAME             186 (_discover_network_ultra_fast)

3169        3784 LOAD_CONST             173 ((None,))
            3786 LOAD_CONST             166 ('target_network')
            3788 LOAD_NAME               45 (str)
            3790 BUILD_TUPLE              2
            3792 LOAD_CONST             167 (<code object _scan_devices_parallel at 0x000001EFF4BAB450, file "tools.py", line 3169>)
            3794 MAKE_FUNCTION            5 (defaults, annotations)
            3796 STORE_NAME             187 (_scan_devices_parallel)

3223        3798 LOAD_CONST              18 ('return')
            3800 LOAD_NAME               45 (str)
            3802 BUILD_TUPLE              2
            3804 LOAD_CONST             168 (<code object _interactive_scan_workflow at 0x000001EFF4BE0F80, file "tools.py", line 3223>)
            3806 MAKE_FUNCTION            4 (annotations)
            3808 STORE_NAME             188 (_interactive_scan_workflow)

3560        3810 PUSH_NULL
            3812 LOAD_NAME                2 (function_tool)
            3814 UNPACK_SEQUENCE          0
            3818 CALL                     0
            3826 CACHE

3561        3828 LOAD_CONST              18 ('return')
            3830 LOAD_NAME               45 (str)
            3832 BUILD_TUPLE              2
            3834 LOAD_CONST             169 (<code object advanced_network_scan at 0x000001EFF69F4310, file "tools.py", line 3560>)
            3836 MAKE_FUNCTION            4 (annotations)

3560        3838 UNPACK_SEQUENCE          0
            3842 CALL                     0
            3850 CACHE

3561        3852 STORE_NAME             189 (advanced_network_scan)

3579        3854 LOAD_NAME               38 (__name__)
            3856 LOAD_CONST             170 ('__main__')
            3858 COMPARE_OP               2 (<)
            3862 CACHE
            3864 POP_JUMP_IF_FALSE       37 (to 3940)

3581        3866 PUSH_NULL
            3868 LOAD_NAME               32 (asyncio)
            3870 LOAD_ATTR              190 (pyplot)
            3890 CACHE
            3892 CACHE
            3894 CACHE
            3896 CACHE
            3898 UNPACK_SEQUENCE          1
            3902 CALL                     1
            3910 CACHE
            3912 STORE_NAME             191 (result)

3582        3914 PUSH_NULL
            3916 LOAD_NAME              192 (print)
            3918 LOAD_NAME              191 (result)
            3920 UNPACK_SEQUENCE          1
            3924 CALL                     1
            3932 CACHE
            3934 POP_TOP
            3936 LOAD_CONST               1 (None)
            3938 RETURN_VALUE

3579     >> 3940 LOAD_CONST               1 (None)
            3942 RETURN_VALUE

Disassembly of <code object validate_email at 0x000001EFF690E630, file "tools.py", line 47>:
 47           0 RESUME                   0

 49           2 LOAD_CONST               1 ('^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$')
              4 STORE_FAST               1 (pattern)

 50           6 LOAD_GLOBAL              1 (NULL + re)
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + re)
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 LOAD_CONST               2 (None)
             48 IS_OP                    1
             50 RETURN_VALUE

Disassembly of <code object get_weather at 0x000001EFF4BD45C0, file "tools.py", line 52>:
 52           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

 73           6 NOP

 74           8 LOAD_GLOBAL              1 (NULL + print)
             18 CACHE
             20 LOAD_CONST               1 ('🌤️ Getting weather for: ')
             22 LOAD_FAST                0 (city)
             24 FORMAT_VALUE             0
             26 BUILD_STRING             2
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

 76          44 LOAD_GLOBAL              3 (NULL + aiohttp)
             54 CACHE
             56 LOAD_ATTR                2 (aiohttp)
             76 CACHE
             78 CACHE
             80 BEFORE_ASYNC_WITH
             82 GET_AWAITABLE            1
             84 LOAD_CONST               2 (None)
        >>   86 SEND                     3 (to 96)
             90 RESUME                   3
             92 JUMP_BACKWARD_NO_INTERRUPT     4 (to 86)
             94 STORE_FAST               1 (session)

 78     >>   96 LOAD_FAST                1 (session)
             98 STORE_SUBSCR
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE

 79         120 LOAD_CONST               3 ('https://geocoding-api.open-meteo.com/v1/search?name=')
            122 LOAD_FAST                0 (city)
            124 FORMAT_VALUE             0
            126 BUILD_STRING             2

 80         128 LOAD_GLOBAL              3 (NULL + aiohttp)
            138 CACHE
            140 LOAD_ATTR                4 (ClientSession)
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE

 78         168 KW_NAMES                 6 (('timeout',))
            170 UNPACK_SEQUENCE          2
            174 CALL                     2
            182 CACHE
            184 BEFORE_ASYNC_WITH
            186 GET_AWAITABLE            1
            188 LOAD_CONST               2 (None)
        >>  190 SEND                     3 (to 200)
            194 RESUME                   3
            196 JUMP_BACKWARD_NO_INTERRUPT     4 (to 190)
            198 NOP

 81     >>  200 STORE_FAST               2 (response)

 82         202 LOAD_FAST                2 (response)
            204 STORE_SUBSCR
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 UNPACK_SEQUENCE          0
            230 CALL                     0
            238 CACHE
            240 GET_AWAITABLE            0
            242 LOAD_CONST               2 (None)
        >>  244 SEND                     3 (to 254)
            248 RESUME                   3
            250 JUMP_BACKWARD_NO_INTERRUPT     4 (to 244)
            252 STORE_FAST               3 (geo_data)

 78     >>  254 LOAD_CONST               2 (None)
            256 LOAD_CONST               2 (None)
            258 LOAD_CONST               2 (None)
            260 UNPACK_SEQUENCE          2
            264 CALL                     2
            272 CACHE
            274 GET_AWAITABLE            2
            276 LOAD_CONST               2 (None)
        >>  278 SEND                     3 (to 288)
            282 RESUME                   3
            284 JUMP_BACKWARD_NO_INTERRUPT     4 (to 278)
            286 POP_TOP
        >>  288 JUMP_FORWARD            17 (to 324)
        >>  290 PUSH_EXC_INFO
            292 WITH_EXCEPT_START
            294 GET_AWAITABLE            2
            296 LOAD_CONST               2 (None)
        >>  298 SEND                     3 (to 308)
            302 RESUME                   3
            304 JUMP_BACKWARD_NO_INTERRUPT     4 (to 298)
            306 POP_JUMP_IF_TRUE         4 (to 316)
        >>  308 RERAISE                  2
        >>  310 COPY                     3
            312 POP_EXCEPT
            314 RERAISE                  1
        >>  316 POP_TOP
            318 POP_EXCEPT
            320 POP_TOP
            322 POP_TOP

 84     >>  324 LOAD_FAST                3 (geo_data)
            326 STORE_SUBSCR
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 LOAD_CONST               7 ('results')
            350 UNPACK_SEQUENCE          1
            354 CALL                     1
            362 CACHE
            364 POP_JUMP_IF_TRUE       160 (to 686)

 85         366 LOAD_FAST                1 (session)
            368 STORE_SUBSCR
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 CACHE
            382 CACHE
            384 CACHE
            386 CACHE
            388 CACHE

 86         390 LOAD_CONST               8 ('https://nominatim.openstreetmap.org/search?q=')
            392 LOAD_FAST                0 (city)
            394 FORMAT_VALUE             0
            396 LOAD_CONST               9 ('&format=json')
            398 BUILD_STRING             3

 87         400 LOAD_GLOBAL              3 (NULL + aiohttp)
            410 CACHE
            412 LOAD_ATTR                4 (ClientSession)
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE

 85         440 KW_NAMES                 6 (('timeout',))
            442 UNPACK_SEQUENCE          2
            446 CALL                     2
            454 CACHE
            456 BEFORE_ASYNC_WITH
            458 GET_AWAITABLE            1
            460 LOAD_CONST               2 (None)
        >>  462 SEND                     3 (to 472)
            466 RESUME                   3
            468 JUMP_BACKWARD_NO_INTERRUPT     4 (to 462)
            470 NOP

 88     >>  472 STORE_FAST               2 (response)

 89         474 LOAD_FAST                2 (response)
            476 STORE_SUBSCR
            480 CACHE
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 CACHE
            496 CACHE
            498 UNPACK_SEQUENCE          0
            502 CALL                     0
            510 CACHE
            512 GET_AWAITABLE            0
            514 LOAD_CONST               2 (None)
        >>  516 SEND                     3 (to 526)
            520 RESUME                   3
            522 JUMP_BACKWARD_NO_INTERRUPT     4 (to 516)
            524 STORE_FAST               3 (geo_data)

 90     >>  526 LOAD_FAST                3 (geo_data)
            528 POP_JUMP_IF_TRUE        42 (to 614)

 91         530 LOAD_CONST              10 ('क्षमा करें, मैं स्थान नहीं ढूंढ पाया: ')
            532 LOAD_FAST                0 (city)
            534 FORMAT_VALUE             0
            536 LOAD_CONST              11 ('.')
            538 BUILD_STRING             3

 85         540 SWAP                     2
            542 LOAD_CONST               2 (None)
            544 LOAD_CONST               2 (None)
            546 LOAD_CONST               2 (None)
            548 UNPACK_SEQUENCE          2
            552 CALL                     2
            560 CACHE
            562 GET_AWAITABLE            2
            564 LOAD_CONST               2 (None)
        >>  566 SEND                     3 (to 576)
            570 RESUME                   3
            572 JUMP_BACKWARD_NO_INTERRUPT     4 (to 566)
            574 POP_TOP

 76     >>  576 SWAP                     2
            578 LOAD_CONST               2 (None)
            580 LOAD_CONST               2 (None)
            582 LOAD_CONST               2 (None)
            584 UNPACK_SEQUENCE          2
            588 CALL                     2
            596 CACHE
            598 GET_AWAITABLE            2
            600 LOAD_CONST               2 (None)
        >>  602 SEND                     3 (to 612)
            606 RESUME                   3
            608 JUMP_BACKWARD_NO_INTERRUPT     4 (to 602)
            610 POP_TOP
        >>  612 RETURN_VALUE

 90     >>  614 NOP

 85         616 LOAD_CONST               2 (None)
            618 LOAD_CONST               2 (None)
            620 LOAD_CONST               2 (None)
            622 UNPACK_SEQUENCE          2
            626 CALL                     2
            634 CACHE
            636 GET_AWAITABLE            2
            638 LOAD_CONST               2 (None)
        >>  640 SEND                     3 (to 650)
            644 RESUME                   3
            646 JUMP_BACKWARD_NO_INTERRUPT     4 (to 640)
            648 POP_TOP
        >>  650 JUMP_FORWARD            17 (to 686)
        >>  652 PUSH_EXC_INFO
            654 WITH_EXCEPT_START
            656 GET_AWAITABLE            2
            658 LOAD_CONST               2 (None)
        >>  660 SEND                     3 (to 670)
            664 RESUME                   3
            666 JUMP_BACKWARD_NO_INTERRUPT     4 (to 660)
            668 POP_JUMP_IF_TRUE         4 (to 678)
        >>  670 RERAISE                  2
        >>  672 COPY                     3
            674 POP_EXCEPT
            676 RERAISE                  1
        >>  678 POP_TOP
            680 POP_EXCEPT
            682 POP_TOP
            684 POP_TOP

 93     >>  686 LOAD_GLOBAL             13 (NULL + isinstance)
            696 CACHE
            698 LOAD_FAST                3 (geo_data)
            700 LOAD_GLOBAL             14 (list)
            710 CACHE
            712 UNPACK_SEQUENCE          2
            716 CALL                     2
            724 CACHE
            726 POP_JUMP_IF_FALSE        8 (to 744)
            728 LOAD_FAST                3 (geo_data)
            730 LOAD_CONST              12 (0)
            732 BINARY_SUBSCR
            736 CACHE
            738 CACHE
            740 CACHE
            742 JUMP_FORWARD            13 (to 770)
        >>  744 LOAD_FAST                3 (geo_data)
            746 LOAD_CONST               7 ('results')
            748 BINARY_SUBSCR
            752 CACHE
            754 CACHE
            756 CACHE
            758 LOAD_CONST              12 (0)
            760 BINARY_SUBSCR
            764 CACHE
            766 CACHE
            768 CACHE
        >>  770 STORE_FAST               4 (location)

 96         772 LOAD_CONST              13 ('https://api.open-meteo.com/v1/forecast?latitude=')

 97         774 LOAD_FAST                4 (location)
            776 STORE_SUBSCR
            780 CACHE
            782 CACHE
            784 CACHE
            786 CACHE
            788 CACHE
            790 CACHE
            792 CACHE
            794 CACHE
            796 CACHE
            798 LOAD_CONST              14 ('lat')
            800 LOAD_FAST                4 (location)
            802 STORE_SUBSCR
            806 CACHE
            808 CACHE
            810 CACHE
            812 CACHE
            814 CACHE
            816 CACHE
            818 CACHE
            820 CACHE
            822 CACHE
            824 LOAD_CONST              15 ('latitude')
            826 UNPACK_SEQUENCE          1
            830 CALL                     1
            838 CACHE
            840 UNPACK_SEQUENCE          2
            844 CALL                     2
            852 CACHE

 96         854 FORMAT_VALUE             0
            856 LOAD_CONST              16 ('&longitude=')

 98         858 LOAD_FAST                4 (location)
            860 STORE_SUBSCR
            864 CACHE
            866 CACHE
            868 CACHE
            870 CACHE
            872 CACHE
            874 CACHE
            876 CACHE
            878 CACHE
            880 CACHE
            882 LOAD_CONST              17 ('lon')
            884 LOAD_FAST                4 (location)
            886 STORE_SUBSCR
            890 CACHE
            892 CACHE
            894 CACHE
            896 CACHE
            898 CACHE
            900 CACHE
            902 CACHE
            904 CACHE
            906 CACHE
            908 LOAD_CONST              18 ('longitude')
            910 UNPACK_SEQUENCE          1
            914 CALL                     1
            922 CACHE
            924 UNPACK_SEQUENCE          2
            928 CALL                     2
            936 CACHE

 96         938 FORMAT_VALUE             0
            940 LOAD_CONST              19 ('&current_weather=true')
            942 BUILD_STRING             5

 95         944 STORE_FAST               5 (weather_url)

102         946 LOAD_FAST                1 (session)
            948 STORE_SUBSCR
            952 CACHE
            954 CACHE
            956 CACHE
            958 CACHE
            960 CACHE
            962 CACHE
            964 CACHE
            966 CACHE
            968 CACHE
            970 LOAD_FAST                5 (weather_url)
            972 LOAD_GLOBAL              3 (NULL + aiohttp)
            982 CACHE
            984 LOAD_ATTR                4 (ClientSession)
           1004 CACHE
           1006 CACHE
           1008 CACHE
           1010 CACHE
           1012 KW_NAMES                 6 (('timeout',))
           1014 UNPACK_SEQUENCE          2
           1018 CALL                     2
           1026 CACHE
           1028 BEFORE_ASYNC_WITH
           1030 GET_AWAITABLE            1
           1032 LOAD_CONST               2 (None)
        >> 1034 SEND                     3 (to 1044)
           1038 RESUME                   3
           1040 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1034)
           1042 STORE_FAST               2 (response)

103     >> 1044 LOAD_FAST                2 (response)
           1046 STORE_SUBSCR
           1050 CACHE
           1052 CACHE
           1054 CACHE
           1056 CACHE
           1058 CACHE
           1060 CACHE
           1062 CACHE
           1064 CACHE
           1066 CACHE
           1068 UNPACK_SEQUENCE          0
           1072 CALL                     0
           1080 CACHE
           1082 GET_AWAITABLE            0
           1084 LOAD_CONST               2 (None)
        >> 1086 SEND                     3 (to 1096)
           1090 RESUME                   3
           1092 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1086)
           1094 STORE_FAST               6 (weather_data)

102     >> 1096 LOAD_CONST               2 (None)
           1098 LOAD_CONST               2 (None)
           1100 LOAD_CONST               2 (None)
           1102 UNPACK_SEQUENCE          2
           1106 CALL                     2
           1114 CACHE
           1116 GET_AWAITABLE            2
           1118 LOAD_CONST               2 (None)
        >> 1120 SEND                     3 (to 1130)
           1124 RESUME                   3
           1126 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1120)
           1128 POP_TOP
        >> 1130 JUMP_FORWARD            17 (to 1166)
        >> 1132 PUSH_EXC_INFO
           1134 WITH_EXCEPT_START
           1136 GET_AWAITABLE            2
           1138 LOAD_CONST               2 (None)
        >> 1140 SEND                     3 (to 1150)
           1144 RESUME                   3
           1146 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1140)
           1148 POP_JUMP_IF_TRUE         4 (to 1158)
        >> 1150 RERAISE                  2
        >> 1152 COPY                     3
           1154 POP_EXCEPT
           1156 RERAISE                  1
        >> 1158 POP_TOP
           1160 POP_EXCEPT
           1162 POP_TOP
           1164 POP_TOP

105     >> 1166 LOAD_CONST              20 ('current_weather')
           1168 LOAD_FAST                6 (weather_data)
           1170 CONTAINS_OP              0
           1172 POP_JUMP_IF_FALSE      111 (to 1396)

106        1174 LOAD_FAST                6 (weather_data)
           1176 LOAD_CONST              20 ('current_weather')
           1178 BINARY_SUBSCR
           1182 CACHE
           1184 CACHE
           1186 CACHE
           1188 STORE_FAST               7 (current)

107        1190 LOAD_FAST                4 (location)
           1192 STORE_SUBSCR
           1196 CACHE
           1198 CACHE
           1200 CACHE
           1202 CACHE
           1204 CACHE
           1206 CACHE
           1208 CACHE
           1210 CACHE
           1212 CACHE
           1214 LOAD_CONST              21 ('display_name')
           1216 LOAD_FAST                4 (location)
           1218 STORE_SUBSCR
           1222 CACHE
           1224 CACHE
           1226 CACHE
           1228 CACHE
           1230 CACHE
           1232 CACHE
           1234 CACHE
           1236 CACHE
           1238 CACHE
           1240 LOAD_CONST              22 ('name')
           1242 LOAD_FAST                0 (city)
           1244 UNPACK_SEQUENCE          2
           1248 CALL                     2
           1256 CACHE
           1258 UNPACK_SEQUENCE          2
           1262 CALL                     2
           1270 CACHE
           1272 STORE_FAST               8 (location_name)

109        1274 LOAD_FAST                8 (location_name)
           1276 FORMAT_VALUE             0
           1278 LOAD_CONST              23 (' का वर्तमान तापमान है ')
           1280 LOAD_FAST                7 (current)
           1282 LOAD_CONST              24 ('temperature')
           1284 BINARY_SUBSCR
           1288 CACHE
           1290 CACHE
           1292 CACHE
           1294 FORMAT_VALUE             0
           1296 LOAD_CONST              25 ('°C और पवन की गति है ')

110        1298 LOAD_FAST                7 (current)
           1300 LOAD_CONST              26 ('windspeed')
           1302 BINARY_SUBSCR
           1306 CACHE
           1308 CACHE
           1310 CACHE

109        1312 FORMAT_VALUE             0
           1314 LOAD_CONST              27 (' km/h।')
           1316 BUILD_STRING             6

108        1318 STORE_FAST               9 (result)

112        1320 LOAD_GLOBAL              1 (NULL + print)
           1330 CACHE
           1332 LOAD_CONST              28 ('✅ Weather result: ')
           1334 LOAD_FAST                9 (result)
           1336 FORMAT_VALUE             0
           1338 BUILD_STRING             2
           1340 UNPACK_SEQUENCE          1
           1344 CALL                     1
           1352 CACHE
           1354 POP_TOP

113        1356 LOAD_FAST                9 (result)

 76        1358 SWAP                     2
           1360 LOAD_CONST               2 (None)
           1362 LOAD_CONST               2 (None)
           1364 LOAD_CONST               2 (None)
           1366 UNPACK_SEQUENCE          2
           1370 CALL                     2
           1378 CACHE
           1380 GET_AWAITABLE            2
           1382 LOAD_CONST               2 (None)
        >> 1384 SEND                     3 (to 1394)
           1388 RESUME                   3
           1390 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1384)
           1392 POP_TOP
        >> 1394 RETURN_VALUE

115     >> 1396 LOAD_CONST              29 ('मौसम की जानकारी प्राप्त करने में असमर्थ: ')
           1398 LOAD_FAST                0 (city)
           1400 FORMAT_VALUE             0
           1402 BUILD_STRING             2

 76        1404 SWAP                     2
           1406 LOAD_CONST               2 (None)
           1408 LOAD_CONST               2 (None)
           1410 LOAD_CONST               2 (None)
           1412 UNPACK_SEQUENCE          2
           1416 CALL                     2
           1424 CACHE
           1426 GET_AWAITABLE            2
           1428 LOAD_CONST               2 (None)
        >> 1430 SEND                     3 (to 1440)
           1434 RESUME                   3
           1436 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1430)
           1438 POP_TOP
        >> 1440 RETURN_VALUE
        >> 1442 PUSH_EXC_INFO
           1444 WITH_EXCEPT_START
           1446 GET_AWAITABLE            2
           1448 LOAD_CONST               2 (None)
        >> 1450 SEND                     3 (to 1460)
           1454 RESUME                   3
           1456 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1450)
           1458 POP_JUMP_IF_TRUE         4 (to 1468)
        >> 1460 RERAISE                  2
        >> 1462 COPY                     3
           1464 POP_EXCEPT
           1466 RERAISE                  1
        >> 1468 POP_TOP
           1470 POP_EXCEPT
           1472 POP_TOP
           1474 POP_TOP
           1476 LOAD_CONST               2 (None)
           1478 RETURN_VALUE
        >> 1480 PUSH_EXC_INFO

116        1482 LOAD_GLOBAL             16 (Exception)
           1492 CACHE
           1494 CHECK_EXC_MATCH
           1496 POP_JUMP_IF_FALSE       40 (to 1578)
           1498 STORE_FAST              10 (e)

117        1500 LOAD_GLOBAL             18 (logger)
           1510 CACHE
           1512 STORE_SUBSCR
           1516 CACHE
           1518 CACHE
           1520 CACHE
           1522 CACHE
           1524 CACHE
           1526 CACHE
           1528 CACHE
           1530 CACHE
           1532 CACHE
           1534 LOAD_CONST              30 ('मौसम त्रुटि: ')
           1536 LOAD_FAST               10 (e)
           1538 FORMAT_VALUE             0
           1540 BUILD_STRING             2
           1542 UNPACK_SEQUENCE          1
           1546 CALL                     1
           1554 CACHE
           1556 POP_TOP

118        1558 POP_EXCEPT
           1560 LOAD_CONST               2 (None)
           1562 STORE_FAST              10 (e)
           1564 DELETE_FAST             10 (e)
           1566 LOAD_CONST              31 ('मौसम सेवा अस्थायी रूप से अनुपलब्ध है। कृपया बाद में प्रयास करें।')
           1568 RETURN_VALUE
        >> 1570 LOAD_CONST               2 (None)
           1572 STORE_FAST              10 (e)
           1574 DELETE_FAST             10 (e)
           1576 RERAISE                  1

116     >> 1578 RERAISE                  0
        >> 1580 COPY                     3
           1582 POP_EXCEPT
           1584 RERAISE                  1
ExceptionTable:
  8 to 92 -> 1480 [0]
  94 to 196 -> 1442 [1] lasti
  200 to 252 -> 290 [2] lasti
  254 to 288 -> 1442 [1] lasti
  290 to 308 -> 310 [4] lasti
  310 to 314 -> 1442 [1] lasti
  316 to 316 -> 310 [4] lasti
  318 to 468 -> 1442 [1] lasti
  472 to 538 -> 652 [2] lasti
  540 to 574 -> 1442 [1] lasti
  576 to 610 -> 1480 [0]
  616 to 650 -> 1442 [1] lasti
  652 to 670 -> 672 [4] lasti
  672 to 676 -> 1442 [1] lasti
  678 to 678 -> 672 [4] lasti
  680 to 1040 -> 1442 [1] lasti
  1042 to 1094 -> 1132 [2] lasti
  1096 to 1130 -> 1442 [1] lasti
  1132 to 1150 -> 1152 [4] lasti
  1152 to 1156 -> 1442 [1] lasti
  1158 to 1158 -> 1152 [4] lasti
  1160 to 1356 -> 1442 [1] lasti
  1358 to 1392 -> 1480 [0]
  1396 to 1402 -> 1442 [1] lasti
  1404 to 1438 -> 1480 [0]
  1442 to 1460 -> 1462 [3] lasti
  1462 to 1466 -> 1480 [0]
  1468 to 1468 -> 1462 [3] lasti
  1470 to 1474 -> 1480 [0]
  1480 to 1498 -> 1580 [1] lasti
  1500 to 1556 -> 1570 [1] lasti
  1570 to 1578 -> 1580 [1] lasti

Disassembly of <code object system_power_action at 0x000001EFF4B5A060, file "tools.py", line 120>:
120           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

137           6 NOP

138           8 LOAD_GLOBAL              1 (NULL + print)
             18 CACHE
             20 LOAD_CONST               1 ('🔧 Power action: ')
             22 LOAD_FAST                0 (action)
             24 FORMAT_VALUE             0
             26 BUILD_STRING             2
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

140          44 LOAD_GLOBAL              3 (NULL + platform)
             54 CACHE
             56 LOAD_ATTR                2 (platform)
             76 CACHE
             78 CACHE
             80 STORE_FAST               1 (system)

142          82 LOAD_FAST                0 (action)
             84 LOAD_CONST               2 ('shutdown')
             86 COMPARE_OP               2 (<)
             90 CACHE
             92 POP_JUMP_IF_FALSE      187 (to 468)

143          94 LOAD_FAST                1 (system)
             96 LOAD_CONST               3 ('Windows')
             98 COMPARE_OP               2 (<)
            102 CACHE
            104 POP_JUMP_IF_FALSE       56 (to 218)

144         106 LOAD_GLOBAL              7 (NULL + asyncio)
            116 CACHE
            118 LOAD_ATTR                4 (system)
            138 CACHE
            140 LOAD_ATTR                5 (NULL|self + system)
            160 CACHE
            162 LOAD_ATTR                2 (platform)
            182 CACHE
            184 CACHE
            186 CACHE
            188 UNPACK_SEQUENCE          1
            192 CALL                     1
            200 CACHE
            202 GET_AWAITABLE            0
            204 LOAD_CONST               5 (None)
        >>  206 SEND                     3 (to 216)
            210 RESUME                   3
            212 JUMP_BACKWARD_NO_INTERRUPT     4 (to 206)
            214 POP_TOP
        >>  216 JUMP_FORWARD           123 (to 464)

145     >>  218 LOAD_FAST                1 (system)
            220 LOAD_CONST               6 ('Linux')
            222 COMPARE_OP               2 (<)
            226 CACHE
            228 POP_JUMP_IF_FALSE       56 (to 342)

146         230 LOAD_GLOBAL              7 (NULL + asyncio)
            240 CACHE
            242 LOAD_ATTR                4 (system)
            262 CACHE
            264 LOAD_ATTR                5 (NULL|self + system)
            284 CACHE
            286 LOAD_ATTR                2 (platform)
            306 CACHE
            308 CACHE
            310 CACHE
            312 UNPACK_SEQUENCE          1
            316 CALL                     1
            324 CACHE
            326 GET_AWAITABLE            0
            328 LOAD_CONST               5 (None)
        >>  330 SEND                     3 (to 340)
            334 RESUME                   3
            336 JUMP_BACKWARD_NO_INTERRUPT     4 (to 330)
            338 POP_TOP
        >>  340 JUMP_FORWARD            61 (to 464)

147     >>  342 LOAD_FAST                1 (system)
            344 LOAD_CONST               8 ('Darwin')
            346 COMPARE_OP               2 (<)
            350 CACHE
            352 POP_JUMP_IF_FALSE       55 (to 464)

148         354 LOAD_GLOBAL              7 (NULL + asyncio)
            364 CACHE
            366 LOAD_ATTR                4 (system)
            386 CACHE
            388 LOAD_ATTR                5 (NULL|self + system)
            408 CACHE
            410 LOAD_ATTR                2 (platform)
            430 CACHE
            432 CACHE
            434 CACHE
            436 UNPACK_SEQUENCE          1
            440 CALL                     1
            448 CACHE
            450 GET_AWAITABLE            0
            452 LOAD_CONST               5 (None)
        >>  454 SEND                     3 (to 464)
            458 RESUME                   3
            460 JUMP_BACKWARD_NO_INTERRUPT     4 (to 454)
            462 POP_TOP

149     >>  464 LOAD_CONST              10 ('सिस्टम शटडाउन किया जा रहा है।')
            466 RETURN_VALUE

151     >>  468 LOAD_FAST                0 (action)
            470 LOAD_CONST              11 ('restart')
            472 COMPARE_OP               2 (<)
            476 CACHE
            478 POP_JUMP_IF_FALSE      187 (to 854)

152         480 LOAD_FAST                1 (system)
            482 LOAD_CONST               3 ('Windows')
            484 COMPARE_OP               2 (<)
            488 CACHE
            490 POP_JUMP_IF_FALSE       56 (to 604)

153         492 LOAD_GLOBAL              7 (NULL + asyncio)
            502 CACHE
            504 LOAD_ATTR                4 (system)
            524 CACHE
            526 LOAD_ATTR                5 (NULL|self + system)
            546 CACHE
            548 LOAD_ATTR                2 (platform)
            568 CACHE
            570 CACHE
            572 CACHE
            574 UNPACK_SEQUENCE          1
            578 CALL                     1
            586 CACHE
            588 GET_AWAITABLE            0
            590 LOAD_CONST               5 (None)
        >>  592 SEND                     3 (to 602)
            596 RESUME                   3
            598 JUMP_BACKWARD_NO_INTERRUPT     4 (to 592)
            600 POP_TOP
        >>  602 JUMP_FORWARD           123 (to 850)

154     >>  604 LOAD_FAST                1 (system)
            606 LOAD_CONST               6 ('Linux')
            608 COMPARE_OP               2 (<)
            612 CACHE
            614 POP_JUMP_IF_FALSE       56 (to 728)

155         616 LOAD_GLOBAL              7 (NULL + asyncio)
            626 CACHE
            628 LOAD_ATTR                4 (system)
            648 CACHE
            650 LOAD_ATTR                5 (NULL|self + system)
            670 CACHE
            672 LOAD_ATTR                2 (platform)
            692 CACHE
            694 CACHE
            696 CACHE
            698 UNPACK_SEQUENCE          1
            702 CALL                     1
            710 CACHE
            712 GET_AWAITABLE            0
            714 LOAD_CONST               5 (None)
        >>  716 SEND                     3 (to 726)
            720 RESUME                   3
            722 JUMP_BACKWARD_NO_INTERRUPT     4 (to 716)
            724 POP_TOP
        >>  726 JUMP_FORWARD            61 (to 850)

156     >>  728 LOAD_FAST                1 (system)
            730 LOAD_CONST               8 ('Darwin')
            732 COMPARE_OP               2 (<)
            736 CACHE
            738 POP_JUMP_IF_FALSE       55 (to 850)

157         740 LOAD_GLOBAL              7 (NULL + asyncio)
            750 CACHE
            752 LOAD_ATTR                4 (system)
            772 CACHE
            774 LOAD_ATTR                5 (NULL|self + system)
            794 CACHE
            796 LOAD_ATTR                2 (platform)
            816 CACHE
            818 CACHE
            820 CACHE
            822 UNPACK_SEQUENCE          1
            826 CALL                     1
            834 CACHE
            836 GET_AWAITABLE            0
            838 LOAD_CONST               5 (None)
        >>  840 SEND                     3 (to 850)
            844 RESUME                   3
            846 JUMP_BACKWARD_NO_INTERRUPT     4 (to 840)
            848 POP_TOP

158     >>  850 LOAD_CONST              15 ('सिस्टम रीस्टार्ट किया जा रहा है।')
            852 RETURN_VALUE

160     >>  854 LOAD_FAST                0 (action)
            856 LOAD_CONST              16 ('lock')
            858 COMPARE_OP               2 (<)
            862 CACHE
            864 POP_JUMP_IF_FALSE      202 (to 1270)

161         866 LOAD_FAST                1 (system)
            868 LOAD_CONST               3 ('Windows')
            870 COMPARE_OP               2 (<)
            874 CACHE
            876 POP_JUMP_IF_FALSE       65 (to 1008)

162         878 LOAD_GLOBAL              7 (NULL + asyncio)
            888 CACHE
            890 LOAD_ATTR                4 (system)
            910 CACHE
            912 LOAD_ATTR                5 (NULL|self + system)
            932 CACHE
            934 LOAD_ATTR                8 (create_task)
            954 LOAD_ATTR               10 (to_thread)
            974 CACHE
            976 CACHE
            978 UNPACK_SEQUENCE          1
            982 CALL                     1
            990 CACHE
            992 GET_AWAITABLE            0
            994 LOAD_CONST               5 (None)
        >>  996 SEND                     3 (to 1006)
           1000 RESUME                   3
           1002 JUMP_BACKWARD_NO_INTERRUPT     4 (to 996)
           1004 POP_TOP
        >> 1006 JUMP_FORWARD           129 (to 1266)

163     >> 1008 LOAD_FAST                1 (system)
           1010 LOAD_CONST               6 ('Linux')
           1012 COMPARE_OP               2 (<)
           1016 CACHE
           1018 POP_JUMP_IF_FALSE       60 (to 1140)

164        1020 LOAD_GLOBAL              7 (NULL + asyncio)
           1030 CACHE
           1032 LOAD_ATTR                4 (system)
           1052 CACHE
           1054 LOAD_ATTR                5 (NULL|self + system)
           1074 CACHE
           1076 LOAD_ATTR               12 (os)
           1096 UNPACK_SEQUENCE          3
           1100 CALL                     3
           1108 CACHE
           1110 UNPACK_SEQUENCE          1
           1114 CALL                     1
           1122 CACHE
           1124 GET_AWAITABLE            0
           1126 LOAD_CONST               5 (None)
        >> 1128 SEND                     3 (to 1138)
           1132 RESUME                   3
           1134 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1128)
           1136 POP_TOP
        >> 1138 JUMP_FORWARD            63 (to 1266)

167     >> 1140 LOAD_FAST                1 (system)
           1142 LOAD_CONST               8 ('Darwin')
           1144 COMPARE_OP               2 (<)
           1148 CACHE
           1150 POP_JUMP_IF_FALSE       57 (to 1266)

168        1152 LOAD_GLOBAL              7 (NULL + asyncio)
           1162 CACHE
           1164 LOAD_ATTR                4 (system)
           1184 CACHE
           1186 LOAD_ATTR                5 (NULL|self + system)
           1206 CACHE
           1208 LOAD_ATTR               12 (os)
           1228 CALL                     2
           1236 CACHE
           1238 UNPACK_SEQUENCE          1
           1242 CALL                     1
           1250 CACHE
           1252 GET_AWAITABLE            0
           1254 LOAD_CONST               5 (None)
        >> 1256 SEND                     3 (to 1266)
           1260 RESUME                   3
           1262 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1256)
           1264 POP_TOP

172     >> 1266 LOAD_CONST              23 ('🔒 स्क्रीन लॉक की गई है।')
           1268 RETURN_VALUE

160     >> 1270 LOAD_CONST               5 (None)
           1272 RETURN_VALUE
        >> 1274 PUSH_EXC_INFO

174        1276 LOAD_GLOBAL             26 (Exception)
           1286 CACHE
           1288 CHECK_EXC_MATCH
           1290 POP_JUMP_IF_FALSE       59 (to 1410)
           1292 STORE_FAST               2 (e)

175        1294 LOAD_GLOBAL             28 (logger)
           1304 CACHE
           1306 STORE_SUBSCR
           1310 CACHE
           1312 CACHE
           1314 CACHE
           1316 CACHE
           1318 CACHE
           1320 CACHE
           1322 CACHE
           1324 CACHE
           1326 CACHE
           1328 LOAD_CONST              24 ('पावर एक्शन विफल: ')
           1330 LOAD_FAST                2 (e)
           1332 FORMAT_VALUE             0
           1334 BUILD_STRING             2
           1336 UNPACK_SEQUENCE          1
           1340 CALL                     1
           1348 CACHE
           1350 POP_TOP

176        1352 LOAD_FAST                0 (action)
           1354 FORMAT_VALUE             0
           1356 LOAD_CONST              25 (' करने में समस्या आई: ')
           1358 LOAD_GLOBAL             33 (NULL + str)
           1368 CACHE
           1370 LOAD_FAST                2 (e)
           1372 UNPACK_SEQUENCE          1
           1376 CALL                     1
           1384 CACHE
           1386 FORMAT_VALUE             0
           1388 BUILD_STRING             3
           1390 SWAP                     2
           1392 POP_EXCEPT
           1394 LOAD_CONST               5 (None)
           1396 STORE_FAST               2 (e)
           1398 DELETE_FAST              2 (e)
           1400 RETURN_VALUE
        >> 1402 LOAD_CONST               5 (None)
           1404 STORE_FAST               2 (e)
           1406 DELETE_FAST              2 (e)
           1408 RERAISE                  1

174     >> 1410 RERAISE                  0
        >> 1412 COPY                     3
           1414 POP_EXCEPT
           1416 RERAISE                  1
ExceptionTable:
  8 to 462 -> 1274 [0]
  468 to 848 -> 1274 [0]
  854 to 1264 -> 1274 [0]
  1274 to 1292 -> 1412 [1] lasti
  1294 to 1388 -> 1402 [1] lasti
  1390 to 1390 -> 1412 [1] lasti
  1402 to 1410 -> 1412 [1] lasti

Disassembly of <code object manage_window at 0x000001EFF4AF8A40, file "tools.py", line 178>:
178           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

196           6 NOP

197           8 LOAD_GLOBAL              1 (NULL + print)
             18 CACHE
             20 LOAD_CONST               1 ('🪟 Window action: ')
             22 LOAD_FAST                0 (action)
             24 FORMAT_VALUE             0
             26 BUILD_STRING             2
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

200          44 LOAD_GLOBAL              3 (NULL + asyncio)
             54 CACHE
             56 LOAD_ATTR                2 (asyncio)
             76 CACHE
             78 LOAD_ATTR                3 (NULL|self + asyncio)
             98 CACHE
            100 LOAD_ATTR                5 (NULL|self + create_task)
            120 CACHE
            122 CACHE
            124 UNPACK_SEQUENCE          1
            128 CALL                     1
            136 CACHE
            138 GET_AWAITABLE            0
            140 LOAD_CONST               2 (None)
        >>  142 SEND                     3 (to 152)
            146 RESUME                   3
            148 JUMP_BACKWARD_NO_INTERRUPT     4 (to 142)
            150 STORE_FAST               1 (active_win)

201     >>  152 LOAD_FAST                1 (active_win)
            154 POP_JUMP_IF_TRUE         2 (to 160)

202         156 LOAD_CONST               3 ('❌ कोई सक्रिय विंडो नहीं मिली।')
            158 RETURN_VALUE

204     >>  160 LOAD_FAST                1 (active_win)
            162 LOAD_ATTR                6 (to_thread)
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 UNPACK_SEQUENCE          0
            198 CALL                     0
            206 CACHE
            208 LOAD_GLOBAL              1 (NULL + print)
            218 COMPARE_OP               2 (<)
            222 CACHE
            224 POP_JUMP_IF_FALSE       55 (to 336)

207         226 LOAD_GLOBAL              3 (NULL + asyncio)
            236 CACHE
            238 LOAD_ATTR                2 (asyncio)
            258 CACHE
            260 LOAD_ATTR                3 (NULL|self + asyncio)
            280 CACHE
            282 UNPACK_SEQUENCE          1
            286 CALL                     1
            294 CACHE
            296 UNPACK_SEQUENCE          1
            300 CALL                     1
            308 CACHE
            310 GET_AWAITABLE            0
            312 LOAD_CONST               2 (None)
        >>  314 SEND                     3 (to 324)
            318 RESUME                   3
            320 JUMP_BACKWARD_NO_INTERRUPT     4 (to 314)
            322 POP_TOP

208     >>  324 LOAD_CONST               6 ("✅ '")
            326 LOAD_FAST                2 (title)
            328 FORMAT_VALUE             0
            330 LOAD_CONST               7 ("' विंडो बंद कर दी गई।")
            332 BUILD_STRING             3
            334 RETURN_VALUE

210     >>  336 LOAD_FAST                0 (action)
            338 LOAD_CONST               8 ('minimize')
            340 COMPARE_OP               2 (<)
            344 CACHE
            346 POP_JUMP_IF_FALSE       55 (to 458)

211         348 LOAD_GLOBAL              3 (NULL + asyncio)
            358 CACHE
            360 LOAD_ATTR                2 (asyncio)
            380 CACHE
            382 LOAD_ATTR                3 (NULL|self + asyncio)
            402 CACHE
            404 UNPACK_SEQUENCE          1
            408 CALL                     1
            416 CACHE
            418 UNPACK_SEQUENCE          1
            422 CALL                     1
            430 CACHE
            432 GET_AWAITABLE            0
            434 LOAD_CONST               2 (None)
        >>  436 SEND                     3 (to 446)
            440 RESUME                   3
            442 JUMP_BACKWARD_NO_INTERRUPT     4 (to 436)
            444 POP_TOP

212     >>  446 LOAD_CONST               6 ("✅ '")
            448 LOAD_FAST                2 (title)
            450 FORMAT_VALUE             0
            452 LOAD_CONST               9 ("' विंडो छोटी की गई।")
            454 BUILD_STRING             3
            456 RETURN_VALUE

214     >>  458 LOAD_FAST                0 (action)
            460 LOAD_CONST              10 ('maximize')
            462 COMPARE_OP               2 (<)
            466 CACHE
            468 POP_JUMP_IF_FALSE       55 (to 580)

215         470 LOAD_GLOBAL              3 (NULL + asyncio)
            480 CACHE
            482 LOAD_ATTR                2 (asyncio)
            502 CACHE
            504 LOAD_ATTR                3 (NULL|self + asyncio)
            524 CACHE
            526 UNPACK_SEQUENCE          1
            530 CALL                     1
            538 CACHE
            540 UNPACK_SEQUENCE          1
            544 CALL                     1
            552 CACHE
            554 GET_AWAITABLE            0
            556 LOAD_CONST               2 (None)
        >>  558 SEND                     3 (to 568)
            562 RESUME                   3
            564 JUMP_BACKWARD_NO_INTERRUPT     4 (to 558)
            566 POP_TOP

216     >>  568 LOAD_CONST               6 ("✅ '")
            570 LOAD_FAST                2 (title)
            572 FORMAT_VALUE             0
            574 LOAD_CONST              11 ("' विंडो बड़ी की गई।")
            576 BUILD_STRING             3
            578 RETURN_VALUE

214     >>  580 LOAD_CONST               2 (None)
            582 RETURN_VALUE
        >>  584 PUSH_EXC_INFO

218         586 LOAD_GLOBAL             22 (Exception)
            596 CACHE
            598 CHECK_EXC_MATCH
            600 POP_JUMP_IF_FALSE       60 (to 722)
            602 STORE_FAST               3 (e)

219         604 LOAD_GLOBAL             24 (logger)
            614 CACHE
            616 STORE_SUBSCR
            620 CACHE
            622 CACHE
            624 CACHE
            626 CACHE
            628 CACHE
            630 CACHE
            632 CACHE
            634 CACHE
            636 CACHE
            638 LOAD_CONST              12 ('विंडो प्रबंधन विफल: ')
            640 LOAD_FAST                3 (e)
            642 FORMAT_VALUE             0
            644 BUILD_STRING             2
            646 UNPACK_SEQUENCE          1
            650 CALL                     1
            658 CACHE
            660 POP_TOP

220         662 LOAD_CONST              13 ('❌ विंडो ')
            664 LOAD_FAST                0 (action)
            666 FORMAT_VALUE             0
            668 LOAD_CONST              14 (' करने में समस्या आई: ')
            670 LOAD_GLOBAL             29 (NULL + str)
            680 CACHE
            682 LOAD_FAST                3 (e)
            684 UNPACK_SEQUENCE          1
            688 CALL                     1
            696 CACHE
            698 FORMAT_VALUE             0
            700 BUILD_STRING             4
            702 SWAP                     2
            704 POP_EXCEPT
            706 LOAD_CONST               2 (None)
            708 STORE_FAST               3 (e)
            710 DELETE_FAST              3 (e)
            712 RETURN_VALUE
        >>  714 LOAD_CONST               2 (None)
            716 STORE_FAST               3 (e)
            718 DELETE_FAST              3 (e)
            720 RERAISE                  1

218     >>  722 RERAISE                  0
        >>  724 COPY                     3
            726 POP_EXCEPT
            728 RERAISE                  1
ExceptionTable:
  8 to 154 -> 584 [0]
  160 to 332 -> 584 [0]
  336 to 454 -> 584 [0]
  458 to 576 -> 584 [0]
  584 to 602 -> 724 [1] lasti
  604 to 700 -> 714 [1] lasti
  702 to 702 -> 724 [1] lasti
  714 to 722 -> 724 [1] lasti

Disassembly of <code object get_time_info at 0x000001EFF3F22D30, file "tools.py", line 222>:
222           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

237           6 LOAD_GLOBAL              1 (NULL + datetime)
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + datetime)
             38 CACHE
             40 CACHE
             42 STORE_FAST               0 (now)

239          44 LOAD_CONST               1 ('आज की तारीख है ')
             46 LOAD_FAST                0 (now)
             48 STORE_SUBSCR
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 LOAD_CONST               2 ('%d-%m-%Y')
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 FORMAT_VALUE             0
             88 LOAD_CONST               3 ('। अभी का समय है ')

240          90 LOAD_FAST                0 (now)
             92 STORE_SUBSCR
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 LOAD_CONST               4 ('%I:%M %p')
            116 UNPACK_SEQUENCE          1
            120 CALL                     1
            128 CACHE

239         130 FORMAT_VALUE             0
            132 LOAD_CONST               5 ('। सप्ताह का दिन है ')

241         134 LOAD_FAST                0 (now)
            136 STORE_SUBSCR
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 LOAD_CONST               6 ('%A')
            160 UNPACK_SEQUENCE          1
            164 CALL                     1
            172 CACHE

239         174 FORMAT_VALUE             0
            176 LOAD_CONST               7 ('।')
            178 BUILD_STRING             7

238         180 STORE_FAST               1 (result)

244         182 LOAD_FAST                1 (result)
            184 RETURN_VALUE

Disassembly of <code object search_web at 0x000001EFF4B5A6C0, file "tools.py", line 246>:
246           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

265           6 NOP

266           8 LOAD_GLOBAL              1 (NULL + print)
             18 CACHE
             20 LOAD_CONST               1 ('🔍 Searching web for: ')
             22 LOAD_FAST                0 (query)
             24 FORMAT_VALUE             0
             26 BUILD_STRING             2
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

269          44 NOP

270          46 LOAD_GLOBAL              3 (NULL + asyncio)
             56 CACHE
             58 LOAD_ATTR                2 (asyncio)
             78 CACHE
             80 LOAD_ATTR                3 (NULL|self + asyncio)
            100 CACHE
            102 LOAD_ATTR                5 (NULL|self + create_task)
            122 CALL                     3
            130 CACHE
            132 UNPACK_SEQUENCE          1
            136 CALL                     1
            144 CACHE
            146 GET_AWAITABLE            0
            148 LOAD_CONST               4 (None)
        >>  150 SEND                     3 (to 160)
            154 RESUME                   3
            156 JUMP_BACKWARD_NO_INTERRUPT     4 (to 150)
            158 STORE_FAST               1 (summary)

271     >>  160 LOAD_GLOBAL              1 (NULL + print)
            170 CACHE
            172 LOAD_CONST               5 ('✅ Wikipedia result found')
            174 UNPACK_SEQUENCE          1
            178 CALL                     1
            186 CACHE
            188 POP_TOP

272         190 LOAD_CONST               6 ('📚 विकिपीडिया:\n')
            192 LOAD_FAST                1 (summary)
            194 FORMAT_VALUE             0
            196 BUILD_STRING             2
            198 RETURN_VALUE
        >>  200 PUSH_EXC_INFO

273         202 LOAD_GLOBAL             12 (Exception)
            212 CACHE
            214 CHECK_EXC_MATCH
            216 POP_JUMP_IF_FALSE       28 (to 274)
            218 STORE_FAST               2 (e)

274         220 LOAD_GLOBAL              1 (NULL + print)
            230 CACHE
            232 LOAD_CONST               7 ('⚠️ Wikipedia failed: ')
            234 LOAD_FAST                2 (e)
            236 FORMAT_VALUE             0
            238 BUILD_STRING             2
            240 UNPACK_SEQUENCE          1
            244 CALL                     1
            252 CACHE
            254 POP_TOP
            256 POP_EXCEPT
            258 LOAD_CONST               4 (None)
            260 STORE_FAST               2 (e)
            262 DELETE_FAST              2 (e)
            264 JUMP_FORWARD             8 (to 282)
        >>  266 LOAD_CONST               4 (None)
            268 STORE_FAST               2 (e)
            270 DELETE_FAST              2 (e)
            272 RERAISE                  1

273     >>  274 RERAISE                  0
        >>  276 COPY                     3
            278 POP_EXCEPT
            280 RERAISE                  1

277     >>  282 NOP

278         284 LOAD_GLOBAL             15 (NULL + aiohttp)
            294 CACHE
            296 LOAD_ATTR                8 (wikipedia)
            316 CACHE
            318 CACHE
            320 BEFORE_ASYNC_WITH
            322 GET_AWAITABLE            1
            324 LOAD_CONST               4 (None)
        >>  326 SEND                     3 (to 336)
            330 RESUME                   3
            332 JUMP_BACKWARD_NO_INTERRUPT     4 (to 326)
            334 STORE_FAST               3 (session)

279     >>  336 LOAD_CONST               8 ('https://api.duckduckgo.com/')
            338 STORE_FAST               4 (url)

280         340 LOAD_FAST                0 (query)
            342 LOAD_CONST               9 ('json')
            344 LOAD_CONST              10 ('1')
            346 LOAD_CONST              10 ('1')
            348 LOAD_CONST              11 (('q', 'format', 'no_redirect', 'no_html'))
            350 BUILD_CONST_KEY_MAP      4
            352 STORE_FAST               5 (params)

281         354 LOAD_FAST                3 (session)
            356 STORE_SUBSCR
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 LOAD_FAST                4 (url)
            380 LOAD_FAST                5 (params)
            382 LOAD_GLOBAL             15 (NULL + aiohttp)
            392 CACHE
            394 LOAD_ATTR               10 (summary)
            414 CACHE
            416 CACHE
            418 CACHE
            420 CACHE
            422 KW_NAMES                14 (('params', 'timeout'))
            424 UNPACK_SEQUENCE          3
            428 CALL                     3
            436 CACHE
            438 BEFORE_ASYNC_WITH
            440 GET_AWAITABLE            1
            442 LOAD_CONST               4 (None)
        >>  444 SEND                     3 (to 454)
            448 RESUME                   3
            450 JUMP_BACKWARD_NO_INTERRUPT     4 (to 444)
            452 STORE_FAST               6 (response)

282     >>  454 LOAD_FAST                6 (response)
            456 STORE_SUBSCR
            460 CACHE
            462 CACHE
            464 CACHE
            466 CACHE
            468 CACHE
            470 CACHE
            472 CACHE
            474 CACHE
            476 CACHE
            478 UNPACK_SEQUENCE          0
            482 CALL                     0
            490 CACHE
            492 GET_AWAITABLE            0
            494 LOAD_CONST               4 (None)
        >>  496 SEND                     3 (to 506)
            500 RESUME                   3
            502 JUMP_BACKWARD_NO_INTERRUPT     4 (to 496)
            504 STORE_FAST               7 (data)

281     >>  506 LOAD_CONST               4 (None)
            508 LOAD_CONST               4 (None)
            510 LOAD_CONST               4 (None)
            512 UNPACK_SEQUENCE          2
            516 CALL                     2
            524 CACHE
            526 GET_AWAITABLE            2
            528 LOAD_CONST               4 (None)
        >>  530 SEND                     3 (to 540)
            534 RESUME                   3
            536 JUMP_BACKWARD_NO_INTERRUPT     4 (to 530)
            538 POP_TOP
        >>  540 JUMP_FORWARD            17 (to 576)
        >>  542 PUSH_EXC_INFO
            544 WITH_EXCEPT_START
            546 GET_AWAITABLE            2
            548 LOAD_CONST               4 (None)
        >>  550 SEND                     3 (to 560)
            554 RESUME                   3
            556 JUMP_BACKWARD_NO_INTERRUPT     4 (to 550)
            558 POP_JUMP_IF_TRUE         4 (to 568)
        >>  560 RERAISE                  2
        >>  562 COPY                     3
            564 POP_EXCEPT
            566 RERAISE                  1
        >>  568 POP_TOP
            570 POP_EXCEPT
            572 POP_TOP
            574 POP_TOP

284     >>  576 LOAD_FAST                7 (data)
            578 STORE_SUBSCR
            582 CACHE
            584 CACHE
            586 CACHE
            588 CACHE
            590 CACHE
            592 CACHE
            594 CACHE
            596 CACHE
            598 CACHE
            600 LOAD_CONST              15 ('AbstractText')
            602 UNPACK_SEQUENCE          1
            606 CALL                     1
            614 CACHE
            616 POP_JUMP_IF_FALSE       44 (to 706)

285         618 LOAD_GLOBAL              1 (NULL + print)
            628 CACHE
            630 LOAD_CONST              16 ('✅ DuckDuckGo API result found')
            632 UNPACK_SEQUENCE          1
            636 CALL                     1
            644 CACHE
            646 POP_TOP

286         648 LOAD_CONST              17 ('🦆 DuckDuckGo:\n')
            650 LOAD_FAST                7 (data)
            652 LOAD_CONST              15 ('AbstractText')
            654 BINARY_SUBSCR
            658 CACHE
            660 CACHE
            662 CACHE
            664 FORMAT_VALUE             0
            666 BUILD_STRING             2

278         668 SWAP                     2
            670 LOAD_CONST               4 (None)
            672 LOAD_CONST               4 (None)
            674 LOAD_CONST               4 (None)
            676 UNPACK_SEQUENCE          2
            680 CALL                     2
            688 CACHE
            690 GET_AWAITABLE            2
            692 LOAD_CONST               4 (None)
        >>  694 SEND                     3 (to 704)
            698 RESUME                   3
            700 JUMP_BACKWARD_NO_INTERRUPT     4 (to 694)
            702 POP_TOP
        >>  704 RETURN_VALUE

287     >>  706 LOAD_FAST                7 (data)
            708 STORE_SUBSCR
            712 CACHE
            714 CACHE
            716 CACHE
            718 CACHE
            720 CACHE
            722 CACHE
            724 CACHE
            726 CACHE
            728 CACHE
            730 LOAD_CONST              18 ('RelatedTopics')
            732 UNPACK_SEQUENCE          1
            736 CALL                     1
            744 CACHE
            746 POP_JUMP_IF_FALSE       56 (to 860)

288         748 LOAD_GLOBAL              1 (NULL + print)
            758 CACHE
            760 LOAD_CONST              19 ('✅ DuckDuckGo related topics found')
            762 UNPACK_SEQUENCE          1
            766 CALL                     1
            774 CACHE
            776 POP_TOP

289         778 LOAD_CONST              20 ('🔍 संबंधित:\n')
            780 LOAD_FAST                7 (data)
            782 LOAD_CONST              18 ('RelatedTopics')
            784 BINARY_SUBSCR
            788 CACHE
            790 CACHE
            792 CACHE
            794 LOAD_CONST              21 (0)
            796 BINARY_SUBSCR
            800 CACHE
            802 CACHE
            804 CACHE
            806 LOAD_CONST              22 ('Text')
            808 BINARY_SUBSCR
            812 CACHE
            814 CACHE
            816 CACHE
            818 FORMAT_VALUE             0
            820 BUILD_STRING             2

278         822 SWAP                     2
            824 LOAD_CONST               4 (None)
            826 LOAD_CONST               4 (None)
            828 LOAD_CONST               4 (None)
            830 UNPACK_SEQUENCE          2
            834 CALL                     2
            842 CACHE
            844 GET_AWAITABLE            2
            846 LOAD_CONST               4 (None)
        >>  848 SEND                     3 (to 858)
            852 RESUME                   3
            854 JUMP_BACKWARD_NO_INTERRUPT     4 (to 848)
            856 POP_TOP
        >>  858 RETURN_VALUE

287     >>  860 NOP

278         862 LOAD_CONST               4 (None)
            864 LOAD_CONST               4 (None)
            866 LOAD_CONST               4 (None)
            868 UNPACK_SEQUENCE          2
            872 CALL                     2
            880 CACHE
            882 GET_AWAITABLE            2
            884 LOAD_CONST               4 (None)
        >>  886 SEND                     3 (to 896)
            890 RESUME                   3
            892 JUMP_BACKWARD_NO_INTERRUPT     4 (to 886)
            894 POP_TOP
        >>  896 JUMP_FORWARD            17 (to 932)
        >>  898 PUSH_EXC_INFO
            900 WITH_EXCEPT_START
            902 GET_AWAITABLE            2
            904 LOAD_CONST               4 (None)
        >>  906 SEND                     3 (to 916)
            910 RESUME                   3
            912 JUMP_BACKWARD_NO_INTERRUPT     4 (to 906)
            914 POP_JUMP_IF_TRUE         4 (to 924)
        >>  916 RERAISE                  2
        >>  918 COPY                     3
            920 POP_EXCEPT
            922 RERAISE                  1
        >>  924 POP_TOP
            926 POP_EXCEPT
            928 POP_TOP
            930 POP_TOP
        >>  932 JUMP_FORWARD            41 (to 1016)
        >>  934 PUSH_EXC_INFO

290         936 LOAD_GLOBAL             12 (Exception)
            946 CACHE
            948 CHECK_EXC_MATCH
            950 POP_JUMP_IF_FALSE       28 (to 1008)
            952 STORE_FAST               2 (e)

291         954 LOAD_GLOBAL              1 (NULL + print)
            964 CACHE
            966 LOAD_CONST              23 ('⚠️ DuckDuckGo API failed: ')
            968 LOAD_FAST                2 (e)
            970 FORMAT_VALUE             0
            972 BUILD_STRING             2
            974 UNPACK_SEQUENCE          1
            978 CALL                     1
            986 CACHE
            988 POP_TOP
            990 POP_EXCEPT
            992 LOAD_CONST               4 (None)
            994 STORE_FAST               2 (e)
            996 DELETE_FAST              2 (e)
            998 JUMP_FORWARD             8 (to 1016)
        >> 1000 LOAD_CONST               4 (None)
           1002 STORE_FAST               2 (e)
           1004 DELETE_FAST              2 (e)
           1006 RERAISE                  1

290     >> 1008 RERAISE                  0
        >> 1010 COPY                     3
           1012 POP_EXCEPT
           1014 RERAISE                  1

294     >> 1016 NOP

295        1018 LOAD_GLOBAL             25 (NULL + DuckDuckGoSearchRun)
           1028 CACHE
           1030 UNPACK_SEQUENCE          0
           1034 CALL                     0
           1042 CACHE
           1044 STORE_FAST               8 (search_tool)

296        1046 LOAD_GLOBAL              3 (NULL + asyncio)
           1056 CACHE
           1058 LOAD_ATTR                2 (asyncio)
           1078 CACHE
           1080 LOAD_ATTR                3 (NULL|self + asyncio)
           1100 CACHE
           1102 LOAD_FAST                0 (query)
           1104 UNPACK_SEQUENCE          2
           1108 CALL                     2
           1116 CACHE
           1118 UNPACK_SEQUENCE          1
           1122 CALL                     1
           1130 CACHE
           1132 GET_AWAITABLE            0
           1134 LOAD_CONST               4 (None)
        >> 1136 SEND                     3 (to 1146)
           1140 RESUME                   3
           1142 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1136)
           1144 STORE_FAST               9 (results)

297     >> 1146 LOAD_FAST                9 (results)
           1148 POP_JUMP_IF_FALSE       20 (to 1190)

298        1150 LOAD_GLOBAL              1 (NULL + print)
           1160 CACHE
           1162 LOAD_CONST              24 ('✅ DuckDuckGo search tool result found')
           1164 UNPACK_SEQUENCE          1
           1168 CALL                     1
           1176 CACHE
           1178 POP_TOP

299        1180 LOAD_CONST              25 ('🔎 परिणाम:\n')
           1182 LOAD_FAST                9 (results)
           1184 FORMAT_VALUE             0
           1186 BUILD_STRING             2
           1188 RETURN_VALUE

297     >> 1190 JUMP_FORWARD            41 (to 1274)
        >> 1192 PUSH_EXC_INFO

300        1194 LOAD_GLOBAL             12 (Exception)
           1204 CACHE
           1206 CHECK_EXC_MATCH
           1208 POP_JUMP_IF_FALSE       28 (to 1266)
           1210 STORE_FAST               2 (e)

301        1212 LOAD_GLOBAL              1 (NULL + print)
           1222 CACHE
           1224 LOAD_CONST              26 ('⚠️ DuckDuckGo search tool failed: ')
           1226 LOAD_FAST                2 (e)
           1228 FORMAT_VALUE             0
           1230 BUILD_STRING             2
           1232 UNPACK_SEQUENCE          1
           1236 CALL                     1
           1244 CACHE
           1246 POP_TOP
           1248 POP_EXCEPT
           1250 LOAD_CONST               4 (None)
           1252 STORE_FAST               2 (e)
           1254 DELETE_FAST              2 (e)
           1256 JUMP_FORWARD             8 (to 1274)
        >> 1258 LOAD_CONST               4 (None)
           1260 STORE_FAST               2 (e)
           1262 DELETE_FAST              2 (e)
           1264 RERAISE                  1

300     >> 1266 RERAISE                  0
        >> 1268 COPY                     3
           1270 POP_EXCEPT
           1272 RERAISE                  1

303     >> 1274 LOAD_CONST              27 ('❌ क्षमा करें, अभी कोई उपयोगी जानकारी नहीं मिली।')
           1276 RETURN_VALUE
        >> 1278 PUSH_EXC_INFO

304        1280 LOAD_GLOBAL             12 (Exception)
           1290 CACHE
           1292 CHECK_EXC_MATCH
           1294 POP_JUMP_IF_FALSE       44 (to 1384)
           1296 STORE_FAST               2 (e)

305        1298 LOAD_GLOBAL             28 (logger)
           1308 CACHE
           1310 STORE_SUBSCR
           1314 CACHE
           1316 CACHE
           1318 CACHE
           1320 CACHE
           1322 CACHE
           1324 CACHE
           1326 CACHE
           1328 CACHE
           1330 CACHE
           1332 LOAD_CONST              28 ('खोज त्रुटि: ')
           1334 LOAD_FAST                2 (e)
           1336 FORMAT_VALUE             0
           1338 BUILD_STRING             2
           1340 UNPACK_SEQUENCE          1
           1344 CALL                     1
           1352 CACHE
           1354 POP_TOP

306        1356 LOAD_CONST              29 ('❌ वेब खोज में त्रुटि: ')
           1358 LOAD_FAST                2 (e)
           1360 FORMAT_VALUE             0
           1362 BUILD_STRING             2
           1364 SWAP                     2
           1366 POP_EXCEPT
           1368 LOAD_CONST               4 (None)
           1370 STORE_FAST               2 (e)
           1372 DELETE_FAST              2 (e)
           1374 RETURN_VALUE
        >> 1376 LOAD_CONST               4 (None)
           1378 STORE_FAST               2 (e)
           1380 DELETE_FAST              2 (e)
           1382 RERAISE                  1

304     >> 1384 RERAISE                  0
        >> 1386 COPY                     3
           1388 POP_EXCEPT
           1390 RERAISE                  1
ExceptionTable:
  8 to 42 -> 1278 [0]
  46 to 196 -> 200 [0]
  200 to 218 -> 276 [1] lasti
  220 to 254 -> 266 [1] lasti
  256 to 264 -> 1278 [0]
  266 to 274 -> 276 [1] lasti
  276 to 280 -> 1278 [0]
  284 to 332 -> 934 [0]
  334 to 450 -> 898 [1] lasti
  452 to 504 -> 542 [2] lasti
  506 to 540 -> 898 [1] lasti
  542 to 560 -> 562 [4] lasti
  562 to 566 -> 898 [1] lasti
  568 to 568 -> 562 [4] lasti
  570 to 666 -> 898 [1] lasti
  668 to 702 -> 934 [0]
  706 to 820 -> 898 [1] lasti
  822 to 856 -> 934 [0]
  862 to 896 -> 934 [0]
  898 to 916 -> 918 [3] lasti
  918 to 922 -> 934 [0]
  924 to 924 -> 918 [3] lasti
  926 to 930 -> 934 [0]
  932 to 932 -> 1278 [0]
  934 to 952 -> 1010 [1] lasti
  954 to 988 -> 1000 [1] lasti
  990 to 998 -> 1278 [0]
  1000 to 1008 -> 1010 [1] lasti
  1010 to 1014 -> 1278 [0]
  1018 to 1186 -> 1192 [0]
  1190 to 1190 -> 1278 [0]
  1192 to 1210 -> 1268 [1] lasti
  1212 to 1246 -> 1258 [1] lasti
  1248 to 1256 -> 1278 [0]
  1258 to 1266 -> 1268 [1] lasti
  1268 to 1272 -> 1278 [0]
  1278 to 1296 -> 1386 [1] lasti
  1298 to 1362 -> 1376 [1] lasti
  1364 to 1364 -> 1386 [1] lasti
  1376 to 1384 -> 1386 [1] lasti

Disassembly of <code object play_media at 0x000001EFF4B5B2C0, file "tools.py", line 308>:
308           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

325           6 NOP

326           8 LOAD_GLOBAL              1 (NULL + print)
             18 CACHE
             20 LOAD_CONST               1 ('🎵 Playing media: ')
             22 LOAD_FAST                0 (media_name)
             24 FORMAT_VALUE             0
             26 LOAD_CONST               2 (' (type: ')
             28 LOAD_FAST                1 (media_type)
             30 FORMAT_VALUE             0
             32 LOAD_CONST               3 (')')
             34 BUILD_STRING             5
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE
             50 POP_TOP

328          52 LOAD_GLOBAL              2 (YOUTUBE_API_KEY)
             62 CACHE
             64 POP_JUMP_IF_TRUE        64 (to 194)

329          66 LOAD_GLOBAL              5 (NULL + asyncio)
             76 CACHE
             78 LOAD_ATTR                3 (NULL|self + YOUTUBE_API_KEY)
             98 CACHE
            100 LOAD_ATTR                4 (asyncio)
            120 CACHE
            122 LOAD_ATTR                6 (create_task)
            142 CACHE
            144 CALL                     2
            152 CACHE
            154 UNPACK_SEQUENCE          1
            158 CALL                     1
            166 CACHE
            168 GET_AWAITABLE            0
            170 LOAD_CONST               5 (None)
        >>  172 SEND                     3 (to 182)
            176 RESUME                   3
            178 JUMP_BACKWARD_NO_INTERRUPT     4 (to 172)
            180 POP_TOP

330     >>  182 LOAD_CONST               6 ("YouTube पर '")
            184 LOAD_FAST                0 (media_name)
            186 FORMAT_VALUE             0
            188 LOAD_CONST               7 ("' खोल रहा हूँ...")
            190 BUILD_STRING             3
            192 RETURN_VALUE

332     >>  194 LOAD_GLOBAL             15 (NULL + aiohttp)
            204 CACHE
            206 LOAD_ATTR                8 (to_thread)
            226 CACHE
            228 CACHE
            230 BEFORE_ASYNC_WITH
            232 GET_AWAITABLE            1
            234 LOAD_CONST               5 (None)
        >>  236 SEND                     3 (to 246)
            240 RESUME                   3
            242 JUMP_BACKWARD_NO_INTERRUPT     4 (to 236)
            244 STORE_FAST               2 (session)

333     >>  246 LOAD_FAST                2 (session)
            248 STORE_SUBSCR
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE

334         270 LOAD_CONST               8 ('https://www.googleapis.com/youtube/v3/search?part=snippet&q=')
            272 LOAD_FAST                0 (media_name)
            274 FORMAT_VALUE             0
            276 LOAD_CONST               9 ('&type=video&key=')
            278 LOAD_GLOBAL              2 (YOUTUBE_API_KEY)
            288 CACHE
            290 FORMAT_VALUE             0
            292 BUILD_STRING             4

335         294 LOAD_GLOBAL             15 (NULL + aiohttp)
            304 CACHE
            306 LOAD_ATTR               10 (webbrowser)
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE

333         334 KW_NAMES                12 (('timeout',))
            336 UNPACK_SEQUENCE          2
            340 CALL                     2
            348 CACHE
            350 BEFORE_ASYNC_WITH
            352 GET_AWAITABLE            1
            354 LOAD_CONST               5 (None)
        >>  356 SEND                     3 (to 366)
            360 RESUME                   3
            362 JUMP_BACKWARD_NO_INTERRUPT     4 (to 356)
            364 NOP

336     >>  366 STORE_FAST               3 (response)

337         368 LOAD_FAST                3 (response)
            370 STORE_SUBSCR
            374 CACHE
            376 CACHE
            378 CACHE
            380 CACHE
            382 CACHE
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 UNPACK_SEQUENCE          0
            396 CALL                     0
            404 CACHE
            406 GET_AWAITABLE            0
            408 LOAD_CONST               5 (None)
        >>  410 SEND                     3 (to 420)
            414 RESUME                   3
            416 JUMP_BACKWARD_NO_INTERRUPT     4 (to 410)
            418 STORE_FAST               4 (data)

333     >>  420 LOAD_CONST               5 (None)
            422 LOAD_CONST               5 (None)
            424 LOAD_CONST               5 (None)
            426 UNPACK_SEQUENCE          2
            430 CALL                     2
            438 CACHE
            440 GET_AWAITABLE            2
            442 LOAD_CONST               5 (None)
        >>  444 SEND                     3 (to 454)
            448 RESUME                   3
            450 JUMP_BACKWARD_NO_INTERRUPT     4 (to 444)
            452 POP_TOP
        >>  454 JUMP_FORWARD            17 (to 490)
        >>  456 PUSH_EXC_INFO
            458 WITH_EXCEPT_START
            460 GET_AWAITABLE            2
            462 LOAD_CONST               5 (None)
        >>  464 SEND                     3 (to 474)
            468 RESUME                   3
            470 JUMP_BACKWARD_NO_INTERRUPT     4 (to 464)
            472 POP_JUMP_IF_TRUE         4 (to 482)
        >>  474 RERAISE                  2
        >>  476 COPY                     3
            478 POP_EXCEPT
            480 RERAISE                  1
        >>  482 POP_TOP
            484 POP_EXCEPT
            486 POP_TOP
            488 POP_TOP

332     >>  490 LOAD_CONST               5 (None)
            492 LOAD_CONST               5 (None)
            494 LOAD_CONST               5 (None)
            496 UNPACK_SEQUENCE          2
            500 CALL                     2
            508 CACHE
            510 GET_AWAITABLE            2
            512 LOAD_CONST               5 (None)
        >>  514 SEND                     3 (to 524)
            518 RESUME                   3
            520 JUMP_BACKWARD_NO_INTERRUPT     4 (to 514)
            522 POP_TOP
        >>  524 JUMP_FORWARD            17 (to 560)
        >>  526 PUSH_EXC_INFO
            528 WITH_EXCEPT_START
            530 GET_AWAITABLE            2
            532 LOAD_CONST               5 (None)
        >>  534 SEND                     3 (to 544)
            538 RESUME                   3
            540 JUMP_BACKWARD_NO_INTERRUPT     4 (to 534)
            542 POP_JUMP_IF_TRUE         4 (to 552)
        >>  544 RERAISE                  2
        >>  546 COPY                     3
            548 POP_EXCEPT
            550 RERAISE                  1
        >>  552 POP_TOP
            554 POP_EXCEPT
            556 POP_TOP
            558 POP_TOP

339     >>  560 LOAD_FAST                4 (data)
            562 STORE_SUBSCR
            566 CACHE
            568 CACHE
            570 CACHE
            572 CACHE
            574 CACHE
            576 CACHE
            578 CACHE
            580 CACHE
            582 CACHE
            584 LOAD_CONST              13 ('items')
            586 UNPACK_SEQUENCE          1
            590 CALL                     1
            598 CACHE
            600 POP_JUMP_IF_FALSE      101 (to 804)

340         602 LOAD_FAST                4 (data)
            604 LOAD_CONST              13 ('items')
            606 BINARY_SUBSCR
            610 CACHE
            612 CACHE
            614 CACHE
            616 LOAD_CONST              14 (0)
            618 BINARY_SUBSCR
            622 CACHE
            624 CACHE
            626 CACHE
            628 STORE_FAST               5 (video)

341         630 LOAD_GLOBAL              5 (NULL + asyncio)
            640 CACHE
            642 LOAD_ATTR                3 (NULL|self + YOUTUBE_API_KEY)
            662 CACHE
            664 LOAD_ATTR                4 (asyncio)
            684 CACHE
            686 LOAD_ATTR                6 (create_task)
            706 CACHE
            708 CACHE
            710 CACHE
            712 LOAD_CONST              17 ('videoId')
            714 BINARY_SUBSCR
            718 CACHE
            720 CACHE
            722 CACHE
            724 FORMAT_VALUE             0
            726 BUILD_STRING             2
            728 UNPACK_SEQUENCE          2
            732 CALL                     2
            740 CACHE
            742 UNPACK_SEQUENCE          1
            746 CALL                     1
            754 CACHE
            756 GET_AWAITABLE            0
            758 LOAD_CONST               5 (None)
        >>  760 SEND                     3 (to 770)
            764 RESUME                   3
            766 JUMP_BACKWARD_NO_INTERRUPT     4 (to 760)
            768 POP_TOP

342     >>  770 LOAD_CONST              18 ('🎵 अब बज रहा है: ')
            772 LOAD_FAST                5 (video)
            774 LOAD_CONST              19 ('snippet')
            776 BINARY_SUBSCR
            780 CACHE
            782 CACHE
            784 CACHE
            786 LOAD_CONST              20 ('title')
            788 BINARY_SUBSCR
            792 CACHE
            794 CACHE
            796 CACHE
            798 FORMAT_VALUE             0
            800 BUILD_STRING             2
            802 RETURN_VALUE

344     >>  804 LOAD_GLOBAL              5 (NULL + asyncio)
            814 CACHE
            816 LOAD_ATTR                3 (NULL|self + YOUTUBE_API_KEY)
            836 CACHE
            838 LOAD_ATTR                4 (asyncio)
            858 CACHE
            860 LOAD_ATTR                6 (create_task)
            880 CACHE
            882 CALL                     2
            890 CACHE
            892 UNPACK_SEQUENCE          1
            896 CALL                     1
            904 CACHE
            906 GET_AWAITABLE            0
            908 LOAD_CONST               5 (None)
        >>  910 SEND                     3 (to 920)
            914 RESUME                   3
            916 JUMP_BACKWARD_NO_INTERRUPT     4 (to 910)
            918 POP_TOP

345     >>  920 LOAD_CONST               6 ("YouTube पर '")
            922 LOAD_FAST                0 (media_name)
            924 FORMAT_VALUE             0
            926 LOAD_CONST               7 ("' खोल रहा हूँ...")
            928 BUILD_STRING             3
            930 RETURN_VALUE
        >>  932 PUSH_EXC_INFO

346         934 LOAD_GLOBAL             24 (Exception)
            944 CACHE
            946 CHECK_EXC_MATCH
            948 POP_JUMP_IF_FALSE       57 (to 1064)
            950 STORE_FAST               6 (e)

347         952 LOAD_GLOBAL             26 (logger)
            962 CACHE
            964 STORE_SUBSCR
            968 CACHE
            970 CACHE
            972 CACHE
            974 CACHE
            976 CACHE
            978 CACHE
            980 CACHE
            982 CACHE
            984 CACHE
            986 LOAD_CONST              21 ('मीडिया त्रुटि: ')
            988 LOAD_FAST                6 (e)
            990 FORMAT_VALUE             0
            992 BUILD_STRING             2
            994 UNPACK_SEQUENCE          1
            998 CALL                     1
           1006 CACHE
           1008 POP_TOP

348        1010 LOAD_CONST              22 ('❌ मीडिया चलाने में समस्या आई: ')
           1012 LOAD_GLOBAL             31 (NULL + str)
           1022 CACHE
           1024 LOAD_FAST                6 (e)
           1026 UNPACK_SEQUENCE          1
           1030 CALL                     1
           1038 CACHE
           1040 FORMAT_VALUE             0
           1042 BUILD_STRING             2
           1044 SWAP                     2
           1046 POP_EXCEPT
           1048 LOAD_CONST               5 (None)
           1050 STORE_FAST               6 (e)
           1052 DELETE_FAST              6 (e)
           1054 RETURN_VALUE
        >> 1056 LOAD_CONST               5 (None)
           1058 STORE_FAST               6 (e)
           1060 DELETE_FAST              6 (e)
           1062 RERAISE                  1

346     >> 1064 RERAISE                  0
        >> 1066 COPY                     3
           1068 POP_EXCEPT
           1070 RERAISE                  1
ExceptionTable:
  8 to 190 -> 932 [0]
  194 to 242 -> 932 [0]
  244 to 362 -> 526 [1] lasti
  366 to 418 -> 456 [2] lasti
  420 to 454 -> 526 [1] lasti
  456 to 474 -> 476 [4] lasti
  476 to 480 -> 526 [1] lasti
  482 to 482 -> 476 [4] lasti
  484 to 488 -> 526 [1] lasti
  490 to 524 -> 932 [0]
  526 to 544 -> 546 [3] lasti
  546 to 550 -> 932 [0]
  552 to 552 -> 546 [3] lasti
  554 to 800 -> 932 [0]
  804 to 928 -> 932 [0]
  932 to 950 -> 1066 [1] lasti
  952 to 1042 -> 1056 [1] lasti
  1044 to 1044 -> 1066 [1] lasti
  1056 to 1064 -> 1066 [1] lasti

Disassembly of <code object desktop_control at 0x000001EFF4B75850, file "tools.py", line 350>:
350           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

366           6 NOP

367           8 LOAD_GLOBAL              1 (NULL + print)
             18 CACHE
             20 LOAD_CONST               1 ('🖥️ Desktop control: ')
             22 LOAD_FAST                0 (action)
             24 FORMAT_VALUE             0
             26 BUILD_STRING             2
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

369          44 LOAD_GLOBAL              3 (NULL + asyncio)
             54 CACHE
             56 LOAD_ATTR                2 (asyncio)
             76 CACHE
             78 LOAD_ATTR                3 (NULL|self + asyncio)
             98 CACHE
            100 LOAD_ATTR                5 (NULL|self + create_task)
            120 CACHE
            122 CACHE
            124 UNPACK_SEQUENCE          1
            128 CALL                     1
            136 CACHE
            138 GET_AWAITABLE            0
            140 LOAD_CONST               2 (None)
        >>  142 SEND                     3 (to 152)
            146 RESUME                   3
            148 JUMP_BACKWARD_NO_INTERRUPT     4 (to 142)
            150 STORE_FAST               3 (original_pos)

371     >>  152 LOAD_FAST                0 (action)
            154 LOAD_CONST               3 ('show')
            156 COMPARE_OP               2 (<)
            160 CACHE
            162 EXTENDED_ARG             1
            164 POP_JUMP_IF_FALSE      489 (to 1144)

372         166 NOP

373         168 LOAD_GLOBAL              3 (NULL + asyncio)
            178 CACHE
            180 LOAD_ATTR                2 (asyncio)
            200 CACHE
            202 LOAD_ATTR                3 (NULL|self + asyncio)
            222 CACHE
            224 LOAD_ATTR                6 (to_thread)
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 UNPACK_SEQUENCE          1
            256 CALL                     1
            264 CACHE
            266 GET_AWAITABLE            0
            268 LOAD_CONST               2 (None)
        >>  270 SEND                     3 (to 280)
            274 RESUME                   3
            276 JUMP_BACKWARD_NO_INTERRUPT     4 (to 270)
            278 POP_TOP

374     >>  280 NOP

403         282 NOP

404         284 LOAD_GLOBAL              3 (NULL + asyncio)
            294 CACHE
            296 LOAD_ATTR                2 (asyncio)
            316 CACHE
            318 LOAD_ATTR                3 (NULL|self + asyncio)
            338 CACHE
            340 LOAD_ATTR                7 (NULL|self + to_thread)
            360 CACHE
            362 LOAD_FAST                3 (original_pos)
            364 LOAD_ATTR                9 (NULL|self + pyautogui)
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 UNPACK_SEQUENCE          1
            396 CALL                     1
            404 CACHE
            406 GET_AWAITABLE            0
            408 LOAD_CONST               2 (None)
        >>  410 SEND                     3 (to 420)
            414 RESUME                   3
            416 JUMP_BACKWARD_NO_INTERRUPT     4 (to 410)
            418 POP_TOP
        >>  420 LOAD_CONST               8 ('🖥️ डेस्कटॉप दिखाया जा रहा है।')
            422 RETURN_VALUE
        >>  424 PUSH_EXC_INFO

405         426 POP_TOP

406         428 POP_EXCEPT
            430 LOAD_CONST               8 ('🖥️ डेस्कटॉप दिखाया जा रहा है।')
            432 RETURN_VALUE
        >>  434 COPY                     3
            436 POP_EXCEPT
            438 RERAISE                  1
        >>  440 PUSH_EXC_INFO

375         442 LOAD_GLOBAL             20 (Exception)
            452 CACHE
            454 CHECK_EXC_MATCH
            456 EXTENDED_ARG             1
            458 POP_JUMP_IF_FALSE      338 (to 1136)
            460 POP_TOP

376         462 NOP

377         464 LOAD_GLOBAL              3 (NULL + asyncio)
            474 CACHE
            476 LOAD_ATTR                2 (asyncio)
            496 CACHE
            498 LOAD_ATTR                3 (NULL|self + asyncio)
            518 CACHE
            520 LOAD_ATTR               11 (NULL|self + position)
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 UNPACK_SEQUENCE          1
            552 CALL                     1
            560 CACHE
            562 GET_AWAITABLE            0
            564 LOAD_CONST               2 (None)
        >>  566 SEND                     3 (to 576)
            570 RESUME                   3
            572 JUMP_BACKWARD_NO_INTERRUPT     4 (to 566)
            574 POP_TOP

378     >>  576 LOAD_GLOBAL              3 (NULL + asyncio)
            586 CACHE
            588 LOAD_ATTR               12 (hotkey)
            608 CACHE
            610 CACHE
            612 CACHE
            614 GET_AWAITABLE            0
            616 LOAD_CONST               2 (None)
        >>  618 SEND                     3 (to 628)
            622 RESUME                   3
            624 JUMP_BACKWARD_NO_INTERRUPT     4 (to 618)
            626 POP_TOP

379     >>  628 LOAD_GLOBAL              3 (NULL + asyncio)
            638 CACHE
            640 LOAD_ATTR                2 (asyncio)
            660 CACHE
            662 LOAD_ATTR                3 (NULL|self + asyncio)
            682 CACHE
            684 LOAD_ATTR               13 (NULL|self + hotkey)
            704 CACHE
            706 CACHE
            708 CACHE
            710 UNPACK_SEQUENCE          1
            714 CALL                     1
            722 CACHE
            724 GET_AWAITABLE            0
            726 LOAD_CONST               2 (None)
        >>  728 SEND                     3 (to 738)
            732 RESUME                   3
            734 JUMP_BACKWARD_NO_INTERRUPT     4 (to 728)
            736 POP_TOP

380     >>  738 POP_EXCEPT

403         740 NOP

404         742 LOAD_GLOBAL              3 (NULL + asyncio)
            752 CACHE
            754 LOAD_ATTR                2 (asyncio)
            774 CACHE
            776 LOAD_ATTR                3 (NULL|self + asyncio)
            796 CACHE
            798 LOAD_ATTR                7 (NULL|self + to_thread)
            818 CACHE
            820 LOAD_FAST                3 (original_pos)
            822 LOAD_ATTR                9 (NULL|self + pyautogui)
            842 CACHE
            844 CACHE
            846 CACHE
            848 CACHE
            850 UNPACK_SEQUENCE          1
            854 CALL                     1
            862 CACHE
            864 GET_AWAITABLE            0
            866 LOAD_CONST               2 (None)
        >>  868 SEND                     3 (to 878)
            872 RESUME                   3
            874 JUMP_BACKWARD_NO_INTERRUPT     4 (to 868)
            876 POP_TOP
        >>  878 LOAD_CONST               8 ('🖥️ डेस्कटॉप दिखाया जा रहा है।')
            880 RETURN_VALUE
        >>  882 PUSH_EXC_INFO

405         884 POP_TOP

406         886 POP_EXCEPT
            888 LOAD_CONST               8 ('🖥️ डेस्कटॉप दिखाया जा रहा है।')
            890 RETURN_VALUE
        >>  892 COPY                     3
            894 POP_EXCEPT
            896 RERAISE                  1
        >>  898 PUSH_EXC_INFO

381         900 LOAD_GLOBAL             20 (Exception)
            910 CACHE
            912 CHECK_EXC_MATCH
            914 POP_JUMP_IF_FALSE      106 (to 1128)
            916 STORE_FAST               4 (e)

382         918 LOAD_CONST              13 ('❌ डेस्कटॉप दिखाने में विफल: ')
            920 LOAD_GLOBAL             29 (NULL + str)
            930 CACHE
            932 LOAD_FAST                4 (e)
            934 UNPACK_SEQUENCE          1
            938 CALL                     1
            946 CACHE
            948 FORMAT_VALUE             0
            950 BUILD_STRING             2
            952 SWAP                     2
            954 POP_EXCEPT
            956 LOAD_CONST               2 (None)
            958 STORE_FAST               4 (e)
            960 DELETE_FAST              4 (e)
            962 SWAP                     2
            964 POP_EXCEPT

403         966 NOP

404         968 LOAD_GLOBAL              3 (NULL + asyncio)
            978 CACHE
            980 LOAD_ATTR                2 (asyncio)
           1000 CACHE
           1002 LOAD_ATTR                3 (NULL|self + asyncio)
           1022 CACHE
           1024 LOAD_ATTR                7 (NULL|self + to_thread)
           1044 CACHE
           1046 LOAD_FAST                3 (original_pos)
           1048 LOAD_ATTR                9 (NULL|self + pyautogui)
           1068 CACHE
           1070 CACHE
           1072 CACHE
           1074 CACHE
           1076 UNPACK_SEQUENCE          1
           1080 CALL                     1
           1088 CACHE
           1090 GET_AWAITABLE            0
           1092 LOAD_CONST               2 (None)
        >> 1094 SEND                     3 (to 1104)
           1098 RESUME                   3
           1100 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1094)
           1102 POP_TOP
        >> 1104 RETURN_VALUE
        >> 1106 PUSH_EXC_INFO

405        1108 POP_TOP

406        1110 POP_EXCEPT
           1112 RETURN_VALUE
        >> 1114 COPY                     3
           1116 POP_EXCEPT
           1118 RERAISE                  1
        >> 1120 LOAD_CONST               2 (None)
           1122 STORE_FAST               4 (e)
           1124 DELETE_FAST              4 (e)
           1126 RERAISE                  1

381     >> 1128 RERAISE                  0
        >> 1130 COPY                     3
           1132 POP_EXCEPT
           1134 RERAISE                  1

375     >> 1136 RERAISE                  0
        >> 1138 COPY                     3
           1140 POP_EXCEPT
           1142 RERAISE                  1

384     >> 1144 LOAD_FAST                0 (action)
           1146 LOAD_CONST              14 ('scroll')
           1148 COMPARE_OP               2 (<)
           1152 CACHE
           1154 EXTENDED_ARG             1
           1156 POP_JUMP_IF_FALSE      398 (to 1954)

385        1158 LOAD_FAST                1 (direction)
           1160 POP_JUMP_IF_TRUE         2 (to 1166)

386        1162 LOAD_CONST              15 ('up')
           1164 STORE_FAST               1 (direction)

387     >> 1166 LOAD_FAST                2 (amount)
           1168 POP_JUMP_IF_TRUE         2 (to 1174)

388        1170 LOAD_CONST              16 (3)
           1172 STORE_FAST               2 (amount)

390     >> 1174 NOP

391        1176 LOAD_GLOBAL              3 (NULL + asyncio)
           1186 CACHE
           1188 LOAD_ATTR                2 (asyncio)
           1208 CACHE
           1210 LOAD_ATTR                3 (NULL|self + asyncio)
           1230 CACHE
           1232 LOAD_ATTR               15 (NULL|self + moveTo)
           1252 CACHE
           1254 CACHE
           1256 UNPACK_SEQUENCE          1
           1260 CALL                     1
           1268 CACHE
           1270 GET_AWAITABLE            0
           1272 LOAD_CONST               2 (None)
        >> 1274 SEND                     3 (to 1284)
           1278 RESUME                   3
           1280 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1274)
           1282 UNPACK_SEQUENCE          2
           1286 STORE_FAST               5 (screen_width)
           1288 STORE_FAST               6 (screen_height)

392        1290 LOAD_GLOBAL              3 (NULL + asyncio)
           1300 CACHE
           1302 LOAD_ATTR                2 (asyncio)
           1322 CACHE
           1324 LOAD_ATTR                3 (NULL|self + asyncio)
           1344 CACHE
           1346 LOAD_ATTR                7 (NULL|self + to_thread)
           1366 LOAD_CONST              17 (2)
           1368 BINARY_OP                2 (//)
           1372 LOAD_CONST               6 (0.1)
           1374 KW_NAMES                 7 (('duration',))
           1376 UNPACK_SEQUENCE          4
           1380 CALL                     4
           1388 CACHE
           1390 UNPACK_SEQUENCE          1
           1394 CALL                     1
           1402 CACHE
           1404 GET_AWAITABLE            0
           1406 LOAD_CONST               2 (None)
        >> 1408 SEND                     3 (to 1418)
           1412 RESUME                   3
           1414 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1408)
           1416 POP_TOP

394     >> 1418 LOAD_FAST                1 (direction)
           1420 LOAD_CONST              15 ('up')
           1422 COMPARE_OP               2 (<)
           1426 CACHE
           1428 POP_JUMP_IF_FALSE        2 (to 1434)
           1430 LOAD_FAST                2 (amount)
           1432 JUMP_FORWARD             2 (to 1438)
        >> 1434 LOAD_FAST                2 (amount)
           1436 UNARY_NEGATIVE
        >> 1438 STORE_FAST               7 (scroll_amount)

395        1440 LOAD_GLOBAL              3 (NULL + asyncio)
           1450 CACHE
           1452 LOAD_ATTR                2 (asyncio)
           1472 CACHE
           1474 LOAD_ATTR                3 (NULL|self + asyncio)
           1494 CACHE
           1496 LOAD_ATTR               16 (x)
           1516 CACHE
           1518 CACHE
           1520 CACHE
           1522 UNPACK_SEQUENCE          1
           1526 CALL                     1
           1534 CACHE
           1536 GET_AWAITABLE            0
           1538 LOAD_CONST               2 (None)
        >> 1540 SEND                     3 (to 1550)
           1544 RESUME                   3
           1546 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1540)
           1548 POP_TOP

396     >> 1550 LOAD_CONST              18 ('✅ सफलतापूर्वक ')
           1552 LOAD_FAST                1 (direction)
           1554 FORMAT_VALUE             0
           1556 LOAD_CONST              19 (' की ओर ')
           1558 LOAD_FAST                2 (amount)
           1560 FORMAT_VALUE             0
           1562 LOAD_CONST              20 (' यूनिट स्क्रॉल किया।')
           1564 BUILD_STRING             5

403        1566 NOP

404        1568 LOAD_GLOBAL              3 (NULL + asyncio)
           1578 CACHE
           1580 LOAD_ATTR                2 (asyncio)
           1600 CACHE
           1602 LOAD_ATTR                3 (NULL|self + asyncio)
           1622 CACHE
           1624 LOAD_ATTR                7 (NULL|self + to_thread)
           1644 CACHE
           1646 LOAD_FAST                3 (original_pos)
           1648 LOAD_ATTR                9 (NULL|self + pyautogui)
           1668 CACHE
           1670 CACHE
           1672 CACHE
           1674 CACHE
           1676 UNPACK_SEQUENCE          1
           1680 CALL                     1
           1688 CACHE
           1690 GET_AWAITABLE            0
           1692 LOAD_CONST               2 (None)
        >> 1694 SEND                     3 (to 1704)
           1698 RESUME                   3
           1700 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1694)
           1702 POP_TOP
        >> 1704 RETURN_VALUE
        >> 1706 PUSH_EXC_INFO

405        1708 POP_TOP

406        1710 POP_EXCEPT
           1712 RETURN_VALUE
        >> 1714 COPY                     3
           1716 POP_EXCEPT
           1718 RERAISE                  1
        >> 1720 PUSH_EXC_INFO

397        1722 LOAD_GLOBAL             20 (Exception)
           1732 CACHE
           1734 CHECK_EXC_MATCH
           1736 POP_JUMP_IF_FALSE      104 (to 1946)
           1738 STORE_FAST               4 (e)

398        1740 LOAD_CONST              21 ('❌ स्क्रॉल करने में विफल: ')
           1742 LOAD_GLOBAL             29 (NULL + str)
           1752 CACHE
           1754 LOAD_FAST                4 (e)
           1756 UNPACK_SEQUENCE          1
           1760 CALL                     1
           1768 CACHE
           1770 FORMAT_VALUE             0
           1772 BUILD_STRING             2
           1774 SWAP                     2
           1776 POP_EXCEPT
           1778 LOAD_CONST               2 (None)
           1780 STORE_FAST               4 (e)
           1782 DELETE_FAST              4 (e)

403        1784 NOP

404        1786 LOAD_GLOBAL              3 (NULL + asyncio)
           1796 CACHE
           1798 LOAD_ATTR                2 (asyncio)
           1818 CACHE
           1820 LOAD_ATTR                3 (NULL|self + asyncio)
           1840 CACHE
           1842 LOAD_ATTR                7 (NULL|self + to_thread)
           1862 CACHE
           1864 LOAD_FAST                3 (original_pos)
           1866 LOAD_ATTR                9 (NULL|self + pyautogui)
           1886 CACHE
           1888 CACHE
           1890 CACHE
           1892 CACHE
           1894 UNPACK_SEQUENCE          1
           1898 CALL                     1
           1906 CACHE
           1908 GET_AWAITABLE            0
           1910 LOAD_CONST               2 (None)
        >> 1912 SEND                     3 (to 1922)
           1916 RESUME                   3
           1918 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1912)
           1920 POP_TOP
        >> 1922 RETURN_VALUE
        >> 1924 PUSH_EXC_INFO

405        1926 POP_TOP

406        1928 POP_EXCEPT
           1930 RETURN_VALUE
        >> 1932 COPY                     3
           1934 POP_EXCEPT
           1936 RERAISE                  1
        >> 1938 LOAD_CONST               2 (None)
           1940 STORE_FAST               4 (e)
           1942 DELETE_FAST              4 (e)
           1944 RERAISE                  1

397     >> 1946 RERAISE                  0
        >> 1948 COPY                     3
           1950 POP_EXCEPT
           1952 RERAISE                  1

384     >> 1954 JUMP_FORWARD           117 (to 2190)
        >> 1956 PUSH_EXC_INFO

400        1958 LOAD_GLOBAL             20 (Exception)
           1968 CACHE
           1970 CHECK_EXC_MATCH
           1972 POP_JUMP_IF_FALSE      104 (to 2182)
           1974 STORE_FAST               4 (e)

401        1976 LOAD_CONST              22 ('❌ डेस्कटॉप कंट्रोल में त्रुटि: ')
           1978 LOAD_GLOBAL             29 (NULL + str)
           1988 CACHE
           1990 LOAD_FAST                4 (e)
           1992 UNPACK_SEQUENCE          1
           1996 CALL                     1
           2004 CACHE
           2006 FORMAT_VALUE             0
           2008 BUILD_STRING             2
           2010 SWAP                     2
           2012 POP_EXCEPT
           2014 LOAD_CONST               2 (None)
           2016 STORE_FAST               4 (e)
           2018 DELETE_FAST              4 (e)

403        2020 NOP

404        2022 LOAD_GLOBAL              3 (NULL + asyncio)
           2032 CACHE
           2034 LOAD_ATTR                2 (asyncio)
           2054 CACHE
           2056 LOAD_ATTR                3 (NULL|self + asyncio)
           2076 CACHE
           2078 LOAD_ATTR                7 (NULL|self + to_thread)
           2098 CACHE
           2100 LOAD_FAST                3 (original_pos)
           2102 LOAD_ATTR                9 (NULL|self + pyautogui)
           2122 CACHE
           2124 CACHE
           2126 CACHE
           2128 CACHE
           2130 UNPACK_SEQUENCE          1
           2134 CALL                     1
           2142 CACHE
           2144 GET_AWAITABLE            0
           2146 LOAD_CONST               2 (None)
        >> 2148 SEND                     3 (to 2158)
           2152 RESUME                   3
           2154 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2148)
           2156 POP_TOP
        >> 2158 RETURN_VALUE
        >> 2160 PUSH_EXC_INFO

405        2162 POP_TOP

406        2164 POP_EXCEPT
           2166 RETURN_VALUE
        >> 2168 COPY                     3
           2170 POP_EXCEPT
           2172 RERAISE                  1
        >> 2174 LOAD_CONST               2 (None)
           2176 STORE_FAST               4 (e)
           2178 DELETE_FAST              4 (e)
           2180 RERAISE                  1

400     >> 2182 RERAISE                  0
        >> 2184 COPY                     3
           2186 POP_EXCEPT
           2188 RERAISE                  1

384     >> 2190 NOP

403        2192 NOP

404        2194 LOAD_GLOBAL              3 (NULL + asyncio)
           2204 CACHE
           2206 LOAD_ATTR                2 (asyncio)
           2226 CACHE
           2228 LOAD_ATTR                3 (NULL|self + asyncio)
           2248 CACHE
           2250 LOAD_ATTR                7 (NULL|self + to_thread)
           2270 CACHE
           2272 LOAD_FAST                3 (original_pos)
           2274 LOAD_ATTR                9 (NULL|self + pyautogui)
           2294 CACHE
           2296 CACHE
           2298 CACHE
           2300 CACHE
           2302 UNPACK_SEQUENCE          1
           2306 CALL                     1
           2314 CACHE
           2316 GET_AWAITABLE            0
           2318 LOAD_CONST               2 (None)
        >> 2320 SEND                     3 (to 2330)
           2324 RESUME                   3
           2326 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2320)
           2328 POP_TOP
        >> 2330 LOAD_CONST               2 (None)
           2332 RETURN_VALUE
        >> 2334 PUSH_EXC_INFO

405        2336 POP_TOP

406        2338 POP_EXCEPT
           2340 LOAD_CONST               2 (None)
           2342 RETURN_VALUE
        >> 2344 COPY                     3
           2346 POP_EXCEPT
           2348 RERAISE                  1
        >> 2350 PUSH_EXC_INFO

403        2352 NOP

404        2354 LOAD_GLOBAL              3 (NULL + asyncio)
           2364 CACHE
           2366 LOAD_ATTR                2 (asyncio)
           2386 CACHE
           2388 LOAD_ATTR                3 (NULL|self + asyncio)
           2408 CACHE
           2410 LOAD_ATTR                7 (NULL|self + to_thread)
           2430 CACHE
           2432 LOAD_FAST                3 (original_pos)
           2434 LOAD_ATTR                9 (NULL|self + pyautogui)
           2454 CACHE
           2456 CACHE
           2458 CACHE
           2460 CACHE
           2462 UNPACK_SEQUENCE          1
           2466 CALL                     1
           2474 CACHE
           2476 GET_AWAITABLE            0
           2478 LOAD_CONST               2 (None)
        >> 2480 SEND                     3 (to 2490)
           2484 RESUME                   3
           2486 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2480)
           2488 POP_TOP
        >> 2490 RERAISE                  0
        >> 2492 PUSH_EXC_INFO

405        2494 POP_TOP

406        2496 POP_EXCEPT
           2498 RERAISE                  0
        >> 2500 COPY                     3
           2502 POP_EXCEPT
           2504 RERAISE                  1
        >> 2506 COPY                     3
           2508 POP_EXCEPT
           2510 RERAISE                  1
ExceptionTable:
  8 to 164 -> 1956 [0]
  168 to 278 -> 440 [0]
  284 to 418 -> 424 [0]
  424 to 426 -> 434 [1] lasti
  440 to 460 -> 1138 [1] lasti
  464 to 736 -> 898 [1]
  738 to 738 -> 1956 [0]
  742 to 876 -> 882 [0]
  882 to 884 -> 892 [1] lasti
  898 to 916 -> 1130 [2] lasti
  918 to 950 -> 1120 [2] lasti
  952 to 952 -> 1130 [2] lasti
  954 to 962 -> 1138 [1] lasti
  964 to 964 -> 1956 [0]
  968 to 1102 -> 1106 [1]
  1106 to 1108 -> 1114 [2] lasti
  1120 to 1128 -> 1130 [2] lasti
  1130 to 1136 -> 1138 [1] lasti
  1138 to 1172 -> 1956 [0]
  1176 to 1564 -> 1720 [0]
  1568 to 1702 -> 1706 [1]
  1706 to 1708 -> 1714 [2] lasti
  1720 to 1738 -> 1948 [1] lasti
  1740 to 1772 -> 1938 [1] lasti
  1774 to 1774 -> 1948 [1] lasti
  1776 to 1782 -> 1956 [0]
  1786 to 1920 -> 1924 [1]
  1924 to 1926 -> 1932 [2] lasti
  1938 to 1946 -> 1948 [1] lasti
  1948 to 1952 -> 1956 [0]
  1954 to 1954 -> 2350 [0]
  1956 to 1974 -> 2184 [1] lasti
  1976 to 2008 -> 2174 [1] lasti
  2010 to 2010 -> 2184 [1] lasti
  2012 to 2018 -> 2350 [0]
  2022 to 2156 -> 2160 [1]
  2160 to 2162 -> 2168 [2] lasti
  2174 to 2182 -> 2184 [1] lasti
  2184 to 2188 -> 2350 [0]
  2194 to 2328 -> 2334 [0]
  2334 to 2336 -> 2344 [1] lasti
  2350 to 2350 -> 2506 [1] lasti
  2354 to 2488 -> 2492 [2]
  2490 to 2490 -> 2506 [1] lasti
  2492 to 2494 -> 2500 [3] lasti
  2496 to 2504 -> 2506 [1] lasti

Disassembly of <code object send_email at 0x000001EFF41E12E0, file "tools.py", line 408>:
              0 MAKE_CELL                0 (to_email)
              2 MAKE_CELL                1 (subject)
              4 MAKE_CELL                2 (message)
              6 MAKE_CELL                3 (cc_email)

408           8 RETURN_GENERATOR
             10 POP_TOP
             12 RESUME                   0

426          14 NOP

427          16 LOAD_GLOBAL              1 (NULL + print)
             26 CACHE
             28 LOAD_CONST               1 ('📧 Sending email to: ')
             30 LOAD_DEREF               0 (to_email)
             32 FORMAT_VALUE             0
             34 BUILD_STRING             2
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE
             50 POP_TOP

429          52 LOAD_GLOBAL              3 (NULL + validate_email)
             62 CACHE
             64 LOAD_DEREF               0 (to_email)
             66 UNPACK_SEQUENCE          1
             70 CALL                     1
             78 CACHE
             80 POP_JUMP_IF_TRUE         5 (to 92)

430          82 LOAD_CONST               2 ('❌ अमान्य प्राप्तकर्ता ईमेल: ')
             84 LOAD_DEREF               0 (to_email)
             86 FORMAT_VALUE             0
             88 BUILD_STRING             2
             90 RETURN_VALUE

432     >>   92 LOAD_DEREF               3 (cc_email)
             94 POP_JUMP_IF_FALSE       20 (to 136)
             96 LOAD_GLOBAL              3 (NULL + validate_email)
            106 CACHE
            108 LOAD_DEREF               3 (cc_email)
            110 UNPACK_SEQUENCE          1
            114 CALL                     1
            122 CACHE
            124 POP_JUMP_IF_TRUE         5 (to 136)

433         126 LOAD_CONST               3 ('❌ अमान्य CC ईमेल: ')
            128 LOAD_DEREF               3 (cc_email)
            130 FORMAT_VALUE             0
            132 BUILD_STRING             2
            134 RETURN_VALUE

435     >>  136 LOAD_GLOBAL              4 (GMAIL_USER)
            146 CACHE
            148 POP_JUMP_IF_FALSE        7 (to 164)
            150 LOAD_GLOBAL              6 (GMAIL_PASSWORD)
            160 CACHE
            162 POP_JUMP_IF_TRUE         2 (to 168)

436     >>  164 LOAD_CONST               4 ('❌ ईमेल credentials नहीं मिले। कृपया .env फाइल चेक करें।')
            166 RETURN_VALUE

438     >>  168 LOAD_CLOSURE             3 (cc_email)
            170 LOAD_CLOSURE             2 (message)
            172 LOAD_CLOSURE             1 (subject)
            174 LOAD_CLOSURE             0 (to_email)
            176 BUILD_TUPLE              4
            178 LOAD_CONST               5 (<code object send_email_sync at 0x000001EFF4BD5580, file "tools.py", line 438>)
            180 MAKE_FUNCTION            8 (closure)
            182 STORE_FAST               4 (send_email_sync)

456         184 LOAD_GLOBAL              9 (NULL + asyncio)
            194 CACHE
            196 LOAD_ATTR                5 (NULL|self + GMAIL_USER)
            216 CACHE
            218 LOAD_ATTR                6 (GMAIL_PASSWORD)
            238 CACHE
            240 CACHE
            242 CACHE
            244 UNPACK_SEQUENCE          1
            248 CALL                     1
            256 CACHE
            258 GET_AWAITABLE            0
            260 LOAD_CONST               6 (None)
        >>  262 SEND                     3 (to 272)
            266 RESUME                   3
            268 JUMP_BACKWARD_NO_INTERRUPT     4 (to 262)
            270 STORE_FAST               5 (recipients)

457     >>  272 LOAD_CONST               7 ('✅ ईमेल सफलतापूर्वक भेजा गया: ')
            274 LOAD_CONST               8 (', ')
            276 STORE_SUBSCR
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 LOAD_FAST                5 (recipients)
            300 UNPACK_SEQUENCE          1
            304 CALL                     1
            312 CACHE
            314 FORMAT_VALUE             0
            316 BUILD_STRING             2
            318 RETURN_VALUE
        >>  320 PUSH_EXC_INFO

458         322 LOAD_GLOBAL             16 (Exception)
            332 CACHE
            334 CHECK_EXC_MATCH
            336 POP_JUMP_IF_FALSE       57 (to 452)
            338 STORE_FAST               6 (e)

459         340 LOAD_GLOBAL             18 (logger)
            350 CACHE
            352 STORE_SUBSCR
            356 CACHE
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 LOAD_CONST               9 ('ईमेल त्रुटि: ')
            376 LOAD_FAST                6 (e)
            378 FORMAT_VALUE             0
            380 BUILD_STRING             2
            382 UNPACK_SEQUENCE          1
            386 CALL                     1
            394 CACHE
            396 POP_TOP

460         398 LOAD_CONST              10 ('❌ ईमेल भेजने में त्रुटि: ')
            400 LOAD_GLOBAL             23 (NULL + str)
            410 CACHE
            412 LOAD_FAST                6 (e)
            414 UNPACK_SEQUENCE          1
            418 CALL                     1
            426 CACHE
            428 FORMAT_VALUE             0
            430 BUILD_STRING             2
            432 SWAP                     2
            434 POP_EXCEPT
            436 LOAD_CONST               6 (None)
            438 STORE_FAST               6 (e)
            440 DELETE_FAST              6 (e)
            442 RETURN_VALUE
        >>  444 LOAD_CONST               6 (None)
            446 STORE_FAST               6 (e)
            448 DELETE_FAST              6 (e)
            450 RERAISE                  1

458     >>  452 RERAISE                  0
        >>  454 COPY                     3
            456 POP_EXCEPT
            458 RERAISE                  1
ExceptionTable:
  16 to 88 -> 320 [0]
  92 to 132 -> 320 [0]
  136 to 162 -> 320 [0]
  168 to 316 -> 320 [0]
  320 to 338 -> 454 [1] lasti
  340 to 430 -> 444 [1] lasti
  432 to 432 -> 454 [1] lasti
  444 to 452 -> 454 [1] lasti

Disassembly of <code object send_email_sync at 0x000001EFF4BD5580, file "tools.py", line 438>:
              0 COPY_FREE_VARS           4

438           2 RESUME                   0

439           4 LOAD_GLOBAL              1 (NULL + MIMEMultipart)
             14 CACHE
             16 UNPACK_SEQUENCE          0
             20 CALL                     0
             28 CACHE
             30 STORE_FAST               0 (msg)

440          32 LOAD_GLOBAL              2 (GMAIL_USER)
             42 CACHE
             44 LOAD_FAST                0 (msg)
             46 LOAD_CONST               1 ('From')
             48 STORE_SUBSCR

441          52 LOAD_DEREF               6 (to_email)
             54 LOAD_FAST                0 (msg)
             56 LOAD_CONST               2 ('To')
             58 STORE_SUBSCR

442          62 LOAD_DEREF               5 (subject)
             64 LOAD_FAST                0 (msg)
             66 LOAD_CONST               3 ('Subject')
             68 STORE_SUBSCR

444          72 LOAD_DEREF               3 (cc_email)
             74 POP_JUMP_IF_FALSE        5 (to 86)

445          76 LOAD_DEREF               3 (cc_email)
             78 LOAD_FAST                0 (msg)
             80 LOAD_CONST               4 ('Cc')
             82 STORE_SUBSCR

447     >>   86 LOAD_FAST                0 (msg)
             88 STORE_SUBSCR
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 LOAD_GLOBAL              7 (NULL + MIMEText)
            120 CACHE
            122 LOAD_DEREF               4 (message)
            124 LOAD_CONST               5 ('plain')
            126 UNPACK_SEQUENCE          2
            130 CALL                     2
            138 CACHE
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 POP_TOP

449         156 LOAD_GLOBAL              9 (NULL + smtplib)
            166 CACHE
            168 LOAD_ATTR                5 (NULL|self + attach)
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 BEFORE_WITH
            198 STORE_FAST               1 (server)

450         200 LOAD_FAST                1 (server)
            202 STORE_SUBSCR
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 UNPACK_SEQUENCE          0
            228 CALL                     0
            236 CACHE
            238 POP_TOP

451         240 LOAD_FAST                1 (server)
            242 STORE_SUBSCR
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 LOAD_GLOBAL              2 (GMAIL_USER)
            274 CACHE
            276 LOAD_GLOBAL             16 (GMAIL_PASSWORD)
            286 CACHE
            288 UNPACK_SEQUENCE          2
            292 CALL                     2
            300 CACHE
            302 POP_TOP

452         304 LOAD_DEREF               6 (to_email)
            306 BUILD_LIST               1
            308 LOAD_DEREF               3 (cc_email)
            310 POP_JUMP_IF_FALSE        3 (to 318)
            312 LOAD_DEREF               3 (cc_email)
            314 BUILD_LIST               1
            316 JUMP_FORWARD             1 (to 320)
        >>  318 BUILD_LIST               0
        >>  320 BINARY_OP                0 (+)
            324 STORE_FAST               2 (recipients)

453         326 LOAD_FAST                1 (server)
            328 STORE_SUBSCR
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 LOAD_GLOBAL              2 (GMAIL_USER)
            360 CACHE
            362 LOAD_FAST                2 (recipients)
            364 LOAD_FAST                0 (msg)
            366 STORE_SUBSCR
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 CACHE
            382 CACHE
            384 CACHE
            386 CACHE
            388 UNPACK_SEQUENCE          0
            392 CALL                     0
            400 CACHE
            402 UNPACK_SEQUENCE          3
            406 CALL                     3
            414 CACHE
            416 POP_TOP

454         418 LOAD_FAST                2 (recipients)

449         420 SWAP                     2
            422 LOAD_CONST               0 (None)
            424 LOAD_CONST               0 (None)
            426 LOAD_CONST               0 (None)
            428 UNPACK_SEQUENCE          2
            432 CALL                     2
            440 CACHE
            442 POP_TOP
            444 RETURN_VALUE
        >>  446 PUSH_EXC_INFO
            448 WITH_EXCEPT_START
            450 POP_JUMP_IF_TRUE         4 (to 460)
            452 RERAISE                  2
        >>  454 COPY                     3
            456 POP_EXCEPT
            458 RERAISE                  1
        >>  460 POP_TOP
            462 POP_EXCEPT
            464 POP_TOP
            466 POP_TOP
            468 LOAD_CONST               0 (None)
            470 RETURN_VALUE
ExceptionTable:
  198 to 418 -> 446 [1] lasti
  446 to 452 -> 454 [3] lasti
  460 to 460 -> 454 [3] lasti

Disassembly of <code object list_active_windows at 0x000001EFF4B69330, file "tools.py", line 462>:
              0 MAKE_CELL                6 (window)

462           2 RETURN_GENERATOR
              4 POP_TOP
              6 RESUME                   0

475           8 NOP

476          10 LOAD_GLOBAL              1 (NULL + print)
             20 CACHE
             22 LOAD_CONST               1 ('🪟 Listing active windows')
             24 UNPACK_SEQUENCE          1
             28 CALL                     1
             36 CACHE
             38 POP_TOP

478          40 LOAD_GLOBAL              3 (NULL + asyncio)
             50 CACHE
             52 LOAD_ATTR                2 (asyncio)
             72 CACHE
             74 LOAD_ATTR                3 (NULL|self + asyncio)
             94 CACHE
             96 LOAD_ATTR                5 (NULL|self + create_task)
            116 CACHE
            118 CACHE
            120 UNPACK_SEQUENCE          1
            124 CALL                     1
            132 CACHE
            134 GET_AWAITABLE            0
            136 LOAD_CONST               2 (None)
        >>  138 SEND                     3 (to 148)
            142 RESUME                   3
            144 JUMP_BACKWARD_NO_INTERRUPT     4 (to 138)
            146 STORE_FAST               0 (windows)

479     >>  148 BUILD_LIST               0
            150 STORE_FAST               1 (result)

481         152 LOAD_FAST                0 (windows)
            154 GET_ITER
        >>  156 FOR_ITER               184 (to 528)

482         160 LOAD_DEREF               6 (window)
            162 POP_JUMP_IF_FALSE      180 (to 524)
            164 LOAD_DEREF               6 (window)
            166 LOAD_ATTR                6 (to_thread)
            186 CACHE
            188 CACHE
            190 CACHE
            192 LOAD_ATTR                2 (asyncio)
            212 CACHE
            214 LOAD_ATTR                3 (NULL|self + asyncio)
            234 CACHE
            236 CALL                     1
            244 CACHE
            246 UNPACK_SEQUENCE          1
            250 CALL                     1
            258 CACHE
            260 GET_AWAITABLE            0
            262 LOAD_CONST               2 (None)
        >>  264 SEND                     3 (to 274)
            268 RESUME                   3
            270 JUMP_BACKWARD_NO_INTERRUPT     4 (to 264)
            272 STORE_FAST               2 (is_minimized)

485     >>  274 LOAD_GLOBAL              3 (NULL + asyncio)
            284 CACHE
            286 LOAD_ATTR                2 (asyncio)
            306 CACHE
            308 LOAD_ATTR                3 (NULL|self + asyncio)
            328 CACHE
            330 CALL                     1
            338 CACHE
            340 UNPACK_SEQUENCE          1
            344 CALL                     1
            352 CACHE
            354 GET_AWAITABLE            0
            356 LOAD_CONST               2 (None)
        >>  358 SEND                     3 (to 368)
            362 RESUME                   3
            364 JUMP_BACKWARD_NO_INTERRUPT     4 (to 358)
            366 STORE_FAST               3 (is_maximized)

486     >>  368 LOAD_FAST                2 (is_minimized)
            370 POP_JUMP_IF_FALSE        2 (to 376)
            372 LOAD_CONST               5 ('Minimized')
            374 JUMP_FORWARD             5 (to 386)
        >>  376 LOAD_FAST                3 (is_maximized)
            378 POP_JUMP_IF_FALSE        2 (to 384)
            380 LOAD_CONST               6 ('Maximized')
            382 JUMP_FORWARD             1 (to 386)
        >>  384 LOAD_CONST               7 ('Active')
        >>  386 STORE_FAST               4 (status)

487         388 LOAD_FAST                1 (result)
            390 STORE_SUBSCR
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE
            402 CACHE
            404 CACHE
            406 CACHE
            408 CACHE
            410 CACHE
            412 LOAD_CONST               8 ('• ')
            414 LOAD_DEREF               6 (window)
            416 LOAD_ATTR                6 (to_thread)
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 UNPACK_SEQUENCE          0
            452 CALL                     0
            460 CACHE
            462 FORMAT_VALUE             0
            464 LOAD_CONST               9 (' (')
            466 LOAD_FAST                4 (status)
            468 FORMAT_VALUE             0
            470 LOAD_CONST              10 (')')
            472 BUILD_STRING             5
            474 UNPACK_SEQUENCE          1
            478 CALL                     1
            486 CACHE
            488 POP_TOP
            490 JUMP_BACKWARD          168 (to 156)
        >>  492 PUSH_EXC_INFO

488         494 LOAD_GLOBAL             18 (Exception)
            504 CACHE
            506 CHECK_EXC_MATCH
            508 POP_JUMP_IF_FALSE        3 (to 516)
            510 POP_TOP

489         512 POP_EXCEPT
            514 JUMP_BACKWARD          180 (to 156)

488     >>  516 RERAISE                  0
        >>  518 COPY                     3
            520 POP_EXCEPT
            522 RERAISE                  1
        >>  524 JUMP_BACKWARD          185 (to 156)

491         526 LOAD_FAST                1 (result)
        >>  528 POP_JUMP_IF_FALSE       24 (to 578)

492         530 LOAD_CONST              11 ('📋 खुली हुई विंडोज:\n')
            532 LOAD_CONST              12 ('\n')
            534 STORE_SUBSCR
            538 CACHE
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 CACHE
            550 CACHE
            552 CACHE
            554 CACHE
            556 LOAD_FAST                1 (result)
            558 UNPACK_SEQUENCE          1
            562 CALL                     1
            570 CACHE
            572 BINARY_OP                0 (+)
            576 RETURN_VALUE

494     >>  578 LOAD_CONST              13 ('❌ कोई विंडो नहीं मिली')
            580 RETURN_VALUE
        >>  582 PUSH_EXC_INFO

496         584 LOAD_GLOBAL             18 (Exception)
            594 CACHE
            596 CHECK_EXC_MATCH
            598 POP_JUMP_IF_FALSE       57 (to 714)
            600 STORE_FAST               5 (e)

497         602 LOAD_GLOBAL             22 (logger)
            612 CACHE
            614 STORE_SUBSCR
            618 CACHE
            620 CACHE
            622 CACHE
            624 CACHE
            626 CACHE
            628 CACHE
            630 CACHE
            632 CACHE
            634 CACHE
            636 LOAD_CONST              14 ('विंडो सूची त्रुटि: ')
            638 LOAD_FAST                5 (e)
            640 FORMAT_VALUE             0
            642 BUILD_STRING             2
            644 UNPACK_SEQUENCE          1
            648 CALL                     1
            656 CACHE
            658 POP_TOP

498         660 LOAD_CONST              15 ('❌ विंडो डिटेक्शन विफल: ')
            662 LOAD_GLOBAL             27 (NULL + str)
            672 CACHE
            674 LOAD_FAST                5 (e)
            676 UNPACK_SEQUENCE          1
            680 CALL                     1
            688 CACHE
            690 FORMAT_VALUE             0
            692 BUILD_STRING             2
            694 SWAP                     2
            696 POP_EXCEPT
            698 LOAD_CONST               2 (None)
            700 STORE_FAST               5 (e)
            702 DELETE_FAST              5 (e)
            704 RETURN_VALUE
        >>  706 LOAD_CONST               2 (None)
            708 STORE_FAST               5 (e)
            710 DELETE_FAST              5 (e)
            712 RERAISE                  1

496     >>  714 RERAISE                  0
        >>  716 COPY                     3
            718 POP_EXCEPT
            720 RERAISE                  1
ExceptionTable:
  10 to 176 -> 582 [0]
  180 to 488 -> 492 [1]
  490 to 490 -> 582 [0]
  492 to 510 -> 518 [2] lasti
  512 to 514 -> 582 [0]
  516 to 516 -> 518 [2] lasti
  518 to 574 -> 582 [0]
  582 to 600 -> 716 [1] lasti
  602 to 692 -> 706 [1] lasti
  694 to 694 -> 716 [1] lasti
  706 to 714 -> 716 [1] lasti

Disassembly of <code object <lambda> at 0x000001EFF68C5A70, file "tools.py", line 484>:
              0 COPY_FREE_VARS           1

484           2 RESUME                   0
              4 LOAD_DEREF               0 (window)
              6 LOAD_ATTR                0 (isMinimized)

Disassembly of <code object <lambda> at 0x000001EFF68C6CD0, file "tools.py", line 485>:
              0 COPY_FREE_VARS           1

485           2 RESUME                   0
              4 LOAD_DEREF               0 (window)
              6 LOAD_ATTR                0 (isMaximized)

Disassembly of <code object manage_window_state at 0x000001EFF4BB19D0, file "tools.py", line 500>:
500           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

503           6 NOP

504           8 LOAD_GLOBAL              1 (NULL + print)
             18 CACHE
             20 LOAD_CONST               1 ('🪟 Managing window state: ')
             22 LOAD_FAST                0 (action)
             24 FORMAT_VALUE             0
             26 LOAD_CONST               2 (' for ')
             28 LOAD_FAST                1 (window_title)
             30 LOAD_GLOBAL              1 (NULL + print)
             40 CACHE
             42 CALL                     1
             50 CACHE
             52 POP_TOP

506          54 LOAD_FAST                1 (window_title)
             56 POP_JUMP_IF_FALSE      153 (to 364)

508          58 LOAD_GLOBAL              3 (NULL + asyncio)
             68 CACHE
             70 LOAD_ATTR                2 (asyncio)
             90 CACHE
             92 LOAD_ATTR                3 (NULL|self + asyncio)
            112 CACHE
            114 LOAD_ATTR                5 (NULL|self + create_task)
            134 CACHE
            136 CACHE
            138 UNPACK_SEQUENCE          1
            142 CALL                     1
            150 CACHE
            152 GET_AWAITABLE            0
            154 LOAD_CONST               4 (None)
        >>  156 SEND                     3 (to 166)
            160 RESUME                   3
            162 JUMP_BACKWARD_NO_INTERRUPT     4 (to 156)
            164 STORE_FAST               2 (all_windows)

509     >>  166 BUILD_LIST               0
            168 STORE_FAST               3 (candidates)

510         170 LOAD_FAST                2 (all_windows)
            172 GET_ITER
        >>  174 FOR_ITER                77 (to 332)

511         178 LOAD_FAST                4 (win)
            180 POP_JUMP_IF_FALSE       73 (to 328)
            182 LOAD_FAST                4 (win)
            184 LOAD_ATTR                6 (to_thread)
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 UNPACK_SEQUENCE          0
            224 CALL                     0
            232 CACHE
            234 LOAD_FAST                4 (win)
            236 LOAD_ATTR                6 (to_thread)
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 UNPACK_SEQUENCE          0
            272 CALL                     0
            280 CACHE
            282 CONTAINS_OP              0
            284 POP_JUMP_IF_FALSE       21 (to 328)

512         286 LOAD_FAST                3 (candidates)
            288 STORE_SUBSCR
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 LOAD_FAST                4 (win)
            312 UNPACK_SEQUENCE          1
            316 CALL                     1
            324 CACHE
            326 POP_TOP
        >>  328 JUMP_BACKWARD           78 (to 174)

514         330 LOAD_FAST                3 (candidates)
        >>  332 POP_JUMP_IF_TRUE         6 (to 346)

515         334 LOAD_CONST               5 ("❌ '")
            336 LOAD_FAST                1 (window_title)
            338 FORMAT_VALUE             0
            340 LOAD_CONST               6 ("' नाम की कोई विंडो नहीं मिली")
            342 BUILD_STRING             3
            344 RETURN_VALUE

517     >>  346 LOAD_FAST                3 (candidates)
            348 LOAD_CONST               7 (0)
            350 BINARY_SUBSCR
            354 CACHE
            356 CACHE
            358 CACHE
            360 STORE_FAST               5 (target_window)
            362 JUMP_FORWARD            58 (to 480)

520     >>  364 LOAD_GLOBAL              3 (NULL + asyncio)
            374 CACHE
            376 LOAD_ATTR                2 (asyncio)
            396 CACHE
            398 LOAD_ATTR                3 (NULL|self + asyncio)
            418 CACHE
            420 LOAD_ATTR                9 (NULL|self + gw)
            440 CACHE
            442 CACHE
            444 UNPACK_SEQUENCE          1
            448 CALL                     1
            456 CACHE
            458 GET_AWAITABLE            0
            460 LOAD_CONST               4 (None)
        >>  462 SEND                     3 (to 472)
            466 RESUME                   3
            468 JUMP_BACKWARD_NO_INTERRUPT     4 (to 462)
            470 STORE_FAST               5 (target_window)

521     >>  472 LOAD_FAST                5 (target_window)
            474 POP_JUMP_IF_TRUE         2 (to 480)

522         476 LOAD_CONST               8 ('❌ कोई सक्रिय विंडो नहीं मिली')
            478 RETURN_VALUE

524     >>  480 NOP

525         482 LOAD_FAST                0 (action)
            484 LOAD_CONST               9 ('maximize')
            486 COMPARE_OP               2 (<)
            490 CACHE
            492 POP_JUMP_IF_FALSE       50 (to 594)

526         494 LOAD_GLOBAL              3 (NULL + asyncio)
            504 CACHE
            506 LOAD_ATTR                2 (asyncio)
            526 CACHE
            528 LOAD_ATTR                3 (NULL|self + asyncio)
            548 CACHE
            550 UNPACK_SEQUENCE          1
            554 CALL                     1
            562 CACHE
            564 UNPACK_SEQUENCE          1
            568 CALL                     1
            576 CACHE
            578 GET_AWAITABLE            0
            580 LOAD_CONST               4 (None)
        >>  582 SEND                     3 (to 592)
            586 RESUME                   3
            588 JUMP_BACKWARD_NO_INTERRUPT     4 (to 582)
            590 POP_TOP
        >>  592 JUMP_FORWARD           111 (to 816)

527     >>  594 LOAD_FAST                0 (action)
            596 LOAD_CONST              10 ('minimize')
            598 COMPARE_OP               2 (<)
            602 CACHE
            604 POP_JUMP_IF_FALSE       50 (to 706)

528         606 LOAD_GLOBAL              3 (NULL + asyncio)
            616 CACHE
            618 LOAD_ATTR                2 (asyncio)
            638 CACHE
            640 LOAD_ATTR                3 (NULL|self + asyncio)
            660 CACHE
            662 UNPACK_SEQUENCE          1
            666 CALL                     1
            674 CACHE
            676 UNPACK_SEQUENCE          1
            680 CALL                     1
            688 CACHE
            690 GET_AWAITABLE            0
            692 LOAD_CONST               4 (None)
        >>  694 SEND                     3 (to 704)
            698 RESUME                   3
            700 JUMP_BACKWARD_NO_INTERRUPT     4 (to 694)
            702 POP_TOP
        >>  704 JUMP_FORWARD            55 (to 816)

529     >>  706 LOAD_FAST                0 (action)
            708 LOAD_CONST              11 ('restore')
            710 COMPARE_OP               2 (<)
            714 CACHE
            716 POP_JUMP_IF_FALSE       49 (to 816)

530         718 LOAD_GLOBAL              3 (NULL + asyncio)
            728 CACHE
            730 LOAD_ATTR                2 (asyncio)
            750 CACHE
            752 LOAD_ATTR                3 (NULL|self + asyncio)
            772 CACHE
            774 UNPACK_SEQUENCE          1
            778 CALL                     1
            786 CACHE
            788 UNPACK_SEQUENCE          1
            792 CALL                     1
            800 CACHE
            802 GET_AWAITABLE            0
            804 LOAD_CONST               4 (None)
        >>  806 SEND                     3 (to 816)
            810 RESUME                   3
            812 JUMP_BACKWARD_NO_INTERRUPT     4 (to 806)
            814 POP_TOP

532     >>  816 LOAD_CONST              12 ("✅ विंडो '")
            818 LOAD_FAST                5 (target_window)
            820 LOAD_ATTR                6 (to_thread)
            840 BUILD_STRING             5
            842 RETURN_VALUE
        >>  844 PUSH_EXC_INFO

533         846 LOAD_GLOBAL             26 (Exception)
            856 CACHE
            858 CHECK_EXC_MATCH
            860 POP_JUMP_IF_FALSE       31 (to 924)
            862 STORE_FAST               6 (e)

534         864 LOAD_CONST              15 ('❌ विंडो ')
            866 LOAD_FAST                0 (action)
            868 FORMAT_VALUE             0
            870 LOAD_CONST              16 (' करने में विफल: ')
            872 LOAD_GLOBAL             29 (NULL + str)
            882 CACHE
            884 LOAD_FAST                6 (e)
            886 UNPACK_SEQUENCE          1
            890 CALL                     1
            898 CACHE
            900 FORMAT_VALUE             0
            902 BUILD_STRING             4
            904 SWAP                     2
            906 POP_EXCEPT
            908 LOAD_CONST               4 (None)
            910 STORE_FAST               6 (e)
            912 DELETE_FAST              6 (e)
            914 RETURN_VALUE
        >>  916 LOAD_CONST               4 (None)
            918 STORE_FAST               6 (e)
            920 DELETE_FAST              6 (e)
            922 RERAISE                  1

533     >>  924 RERAISE                  0
        >>  926 COPY                     3
            928 POP_EXCEPT
            930 RERAISE                  1
        >>  932 PUSH_EXC_INFO

536         934 LOAD_GLOBAL             26 (Exception)
            944 CACHE
            946 CHECK_EXC_MATCH
            948 POP_JUMP_IF_FALSE       28 (to 1006)
            950 STORE_FAST               6 (e)

537         952 LOAD_CONST              17 ('❌ त्रुटि: ')
            954 LOAD_GLOBAL             29 (NULL + str)
            964 CACHE
            966 LOAD_FAST                6 (e)
            968 UNPACK_SEQUENCE          1
            972 CALL                     1
            980 CACHE
            982 FORMAT_VALUE             0
            984 BUILD_STRING             2
            986 SWAP                     2
            988 POP_EXCEPT
            990 LOAD_CONST               4 (None)
            992 STORE_FAST               6 (e)
            994 DELETE_FAST              6 (e)
            996 RETURN_VALUE
        >>  998 LOAD_CONST               4 (None)
           1000 STORE_FAST               6 (e)
           1002 DELETE_FAST              6 (e)
           1004 RERAISE                  1

536     >> 1006 RERAISE                  0
        >> 1008 COPY                     3
           1010 POP_EXCEPT
           1012 RERAISE                  1
ExceptionTable:
  8 to 342 -> 932 [0]
  346 to 474 -> 932 [0]
  482 to 840 -> 844 [0]
  844 to 862 -> 926 [1] lasti
  864 to 902 -> 916 [1] lasti
  904 to 904 -> 926 [1] lasti
  906 to 912 -> 932 [0]
  916 to 924 -> 926 [1] lasti
  926 to 930 -> 932 [0]
  932 to 950 -> 1008 [1] lasti
  952 to 984 -> 998 [1] lasti
  986 to 986 -> 1008 [1] lasti
  998 to 1006 -> 1008 [1] lasti

Disassembly of <code object say_reminder at 0x000001EFF690E730, file "tools.py", line 539>:
539           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

553           6 LOAD_GLOBAL              1 (NULL + print)
             16 CACHE
             18 LOAD_CONST               1 ('🔔 Reminder: ')
             20 LOAD_FAST                0 (msg)
             22 FORMAT_VALUE             0
             24 BUILD_STRING             2
             26 UNPACK_SEQUENCE          1
             30 CALL                     1
             38 CACHE
             40 POP_TOP

554          42 LOAD_CONST               2 ('🔔 याद दिलाना: ')
             44 LOAD_FAST                0 (msg)
             46 FORMAT_VALUE             0
             48 BUILD_STRING             2
             50 RETURN_VALUE

Disassembly of <code object get_today_reminder_message_from_db at 0x000001EFF4BD4CD0, file "tools.py", line 563>:
563           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

565           6 LOAD_GLOBAL              1 (NULL + datetime)
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + datetime)
             38 CACHE
             40 CACHE
             42 STORE_SUBSCR
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 UNPACK_SEQUENCE          0
             68 CALL                     0
             76 CACHE
             78 STORE_FAST               0 (today)

566          80 NOP

567          82 LOAD_GLOBAL              7 (NULL + print)
             92 CACHE
             94 LOAD_CONST               1 ('🔍 Checking reminders for ')
             96 LOAD_FAST                0 (today)
             98 FORMAT_VALUE             0
            100 BUILD_STRING             2
            102 UNPACK_SEQUENCE          1
            106 CALL                     1
            114 CACHE
            116 POP_TOP

569         118 LOAD_CONST               2 (<code object db_operation at 0x000001EFF3F7EDC0, file "tools.py", line 569>)
            120 MAKE_FUNCTION            0
            122 STORE_FAST               1 (db_operation)

578         124 LOAD_GLOBAL              9 (NULL + asyncio)
            134 CACHE
            136 LOAD_ATTR                5 (NULL|self + date)
            156 CACHE
            158 LOAD_ATTR                6 (print)
            178 CACHE
            180 CACHE
            182 CACHE
            184 UNPACK_SEQUENCE          1
            188 CALL                     1
            196 CACHE
            198 GET_AWAITABLE            0
            200 LOAD_CONST               3 (None)
        >>  202 SEND                     3 (to 212)
            206 RESUME                   3
            208 JUMP_BACKWARD_NO_INTERRUPT     4 (to 202)
            210 STORE_FAST               2 (rows)

579     >>  212 BUILD_LIST               0
            214 STORE_FAST               3 (reminders)

581         216 LOAD_FAST                2 (rows)
            218 GET_ITER
        >>  220 FOR_ITER               155 (to 534)
            224 CACHE
            226 STORE_FAST               4 (role)
            228 STORE_FAST               5 (content_json)

582         230 LOAD_FAST                4 (role)
            232 LOAD_CONST               4 ('user')
            234 COMPARE_OP               3 (<)
            238 CACHE
            240 POP_JUMP_IF_FALSE        1 (to 244)

583         242 JUMP_BACKWARD           12 (to 220)

585     >>  244 NOP

586         246 LOAD_GLOBAL             15 (NULL + json)
            256 CACHE
            258 LOAD_ATTR                8 (asyncio)
            278 CACHE
            280 CACHE
            282 CACHE
            284 STORE_FAST               6 (content_items)

587         286 LOAD_FAST                6 (content_items)
            288 GET_ITER
        >>  290 FOR_ITER                78 (to 450)

588         294 LOAD_FAST                7 (item)
            296 STORE_SUBSCR
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 CACHE
            318 UNPACK_SEQUENCE          0
            322 CALL                     0
            330 CACHE
            332 STORE_FAST               8 (item_lower)

590         334 LOAD_CONST               5 ('remind')
            336 LOAD_FAST                8 (item_lower)
            338 CONTAINS_OP              0
            340 POP_JUMP_IF_TRUE         8 (to 358)
            342 LOAD_CONST               6 ('remember')
            344 LOAD_FAST                8 (item_lower)
            346 CONTAINS_OP              0
            348 POP_JUMP_IF_TRUE         4 (to 358)
            350 LOAD_CONST               7 ('याद दिला')
            352 LOAD_FAST                8 (item_lower)
            354 CONTAINS_OP              0
            356 POP_JUMP_IF_FALSE       44 (to 446)

591     >>  358 LOAD_GLOBAL             21 (NULL + extract_date_from_text)
            368 CACHE
            370 LOAD_FAST                8 (item_lower)
            372 UNPACK_SEQUENCE          1
            376 CALL                     1
            384 CACHE
            386 STORE_FAST               9 (date)

592         388 LOAD_FAST                9 (date)
            390 POP_JUMP_IF_FALSE       27 (to 446)
            392 LOAD_FAST                9 (date)
            394 LOAD_FAST                0 (today)
            396 COMPARE_OP               2 (<)
            400 CACHE
            402 POP_JUMP_IF_FALSE       21 (to 446)

593         404 LOAD_FAST                3 (reminders)
            406 STORE_SUBSCR
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 LOAD_FAST                7 (item)
            430 UNPACK_SEQUENCE          1
            434 CALL                     1
            442 CACHE
            444 POP_TOP
        >>  446 JUMP_BACKWARD           79 (to 290)

587         448 JUMP_BACKWARD          115 (to 220)
        >>  450 PUSH_EXC_INFO

594         452 LOAD_GLOBAL             24 (Exception)
            462 CACHE
            464 CHECK_EXC_MATCH
            466 POP_JUMP_IF_FALSE       28 (to 524)
            468 STORE_FAST              10 (e)

595         470 LOAD_GLOBAL              7 (NULL + print)
            480 CACHE
            482 LOAD_CONST               8 ('⚠️ Error parsing content: ')
            484 LOAD_FAST               10 (e)
            486 FORMAT_VALUE             0
            488 BUILD_STRING             2
            490 UNPACK_SEQUENCE          1
            494 CALL                     1
            502 CACHE
            504 POP_TOP

596         506 POP_EXCEPT
            508 LOAD_CONST               3 (None)
            510 STORE_FAST              10 (e)
            512 DELETE_FAST             10 (e)
            514 JUMP_BACKWARD          148 (to 220)
        >>  516 LOAD_CONST               3 (None)
            518 STORE_FAST              10 (e)
            520 DELETE_FAST             10 (e)
            522 RERAISE                  1

594     >>  524 RERAISE                  0
        >>  526 COPY                     3
            528 POP_EXCEPT
            530 RERAISE                  1

598         532 LOAD_FAST                3 (reminders)
        >>  534 POP_JUMP_IF_FALSE       36 (to 608)

599         536 LOAD_CONST               9 ('\n')
            538 STORE_SUBSCR
            542 CACHE
            544 CACHE
            546 CACHE
            548 CACHE
            550 CACHE
            552 CACHE
            554 CACHE
            556 CACHE
            558 CACHE
            560 LOAD_CONST              10 (<code object <genexpr> at 0x000001EFF68C7E50, file "tools.py", line 599>)
            562 MAKE_FUNCTION            0
            564 LOAD_FAST                3 (reminders)
            566 GET_ITER
            568 UNPACK_SEQUENCE          0
            572 CALL                     0
            580 CACHE
            582 UNPACK_SEQUENCE          1
            586 CALL                     1
            594 CACHE
            596 STORE_FAST              11 (combined)

600         598 LOAD_CONST              11 ('🧠 सर, आज आपको याद है न — ')
            600 LOAD_FAST               11 (combined)
            602 FORMAT_VALUE             0
            604 BUILD_STRING             2
            606 RETURN_VALUE

602     >>  608 LOAD_CONST               3 (None)
            610 RETURN_VALUE
        >>  612 PUSH_EXC_INFO

604         614 LOAD_GLOBAL             24 (Exception)
            624 CACHE
            626 CHECK_EXC_MATCH
            628 POP_JUMP_IF_FALSE       29 (to 688)
            630 STORE_FAST              10 (e)

605         632 LOAD_GLOBAL              7 (NULL + print)
            642 CACHE
            644 LOAD_CONST              12 ('❌ Error while checking reminders: ')
            646 LOAD_FAST               10 (e)
            648 FORMAT_VALUE             0
            650 BUILD_STRING             2
            652 UNPACK_SEQUENCE          1
            656 CALL                     1
            664 CACHE
            666 POP_TOP

606         668 POP_EXCEPT
            670 LOAD_CONST               3 (None)
            672 STORE_FAST              10 (e)
            674 DELETE_FAST             10 (e)
            676 LOAD_CONST               3 (None)
            678 RETURN_VALUE
        >>  680 LOAD_CONST               3 (None)
            682 STORE_FAST              10 (e)
            684 DELETE_FAST             10 (e)
            686 RERAISE                  1

604     >>  688 RERAISE                  0
        >>  690 COPY                     3
            692 POP_EXCEPT
            694 RERAISE                  1
ExceptionTable:
  82 to 242 -> 612 [0]
  246 to 446 -> 450 [1]
  448 to 448 -> 612 [0]
  450 to 468 -> 526 [2] lasti
  470 to 504 -> 516 [2] lasti
  506 to 514 -> 612 [0]
  516 to 524 -> 526 [2] lasti
  526 to 604 -> 612 [0]
  612 to 630 -> 690 [1] lasti
  632 to 666 -> 680 [1] lasti
  680 to 688 -> 690 [1] lasti

Disassembly of <code object db_operation at 0x000001EFF3F7EDC0, file "tools.py", line 569>:
569           0 RESUME                   0

570           2 LOAD_GLOBAL              1 (NULL + sqlite3)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sqlite3)
             34 CACHE
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE
             50 STORE_FAST               0 (conn)

571          52 LOAD_FAST                0 (conn)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 STORE_FAST               1 (cursor)

573          92 LOAD_FAST                1 (cursor)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_CONST               1 ('SELECT role, content FROM ')
            118 LOAD_GLOBAL             10 (TABLE_NAME)
            128 CACHE
            130 FORMAT_VALUE             0
            132 LOAD_CONST               2 (' ORDER BY created_at ASC')
            134 BUILD_STRING             3
            136 UNPACK_SEQUENCE          1
            140 CALL                     1
            148 CACHE
            150 POP_TOP

574         152 LOAD_FAST                1 (cursor)
            154 STORE_SUBSCR
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 UNPACK_SEQUENCE          0
            180 CALL                     0
            188 CACHE
            190 STORE_FAST               2 (rows)

575         192 LOAD_FAST                0 (conn)
            194 STORE_SUBSCR
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 UNPACK_SEQUENCE          0
            220 CALL                     0
            228 CACHE
            230 POP_TOP

576         232 LOAD_FAST                2 (rows)
            234 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001EFF68C7E50, file "tools.py", line 599>:
599           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                 9 (to 30)
             12 LOAD_CONST               0 ('🔔 ')
             14 LOAD_FAST                1 (r)
             16 FORMAT_VALUE             0
             18 BUILD_STRING             2
             20 LOAD_FAST                0 (.0)
             22 RESUME                   1
             24 POP_TOP
             26 JUMP_BACKWARD           10 (to 8)
             28 LOAD_CONST               1 (None)
        >>   30 RETURN_VALUE

Disassembly of <code object extract_date_from_text at 0x000001EFF6991430, file "tools.py", line 608>:
608           0 RESUME                   0

610           2 LOAD_GLOBAL              1 (NULL + datetime)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + datetime)
             34 CACHE
             36 CACHE
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 UNPACK_SEQUENCE          0
             64 CALL                     0
             72 CACHE
             74 STORE_FAST               1 (today)

612          76 LOAD_GLOBAL              7 (NULL + re)
             86 CACHE
             88 LOAD_ATTR                4 (date)
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 STORE_FAST               2 (date_match)

613         118 LOAD_FAST                2 (date_match)
            120 POP_JUMP_IF_FALSE       66 (to 254)

614         122 NOP

615         124 LOAD_GLOBAL              1 (NULL + datetime)
            134 CACHE
            136 LOAD_ATTR                5 (NULL|self + date)
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 UNPACK_SEQUENCE          0
            174 CALL                     0
            182 CACHE
            184 LOAD_CONST               2 ('%Y-%m-%d')
            186 UNPACK_SEQUENCE          2
            190 CALL                     2
            198 CACHE
            200 STORE_SUBSCR
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 UNPACK_SEQUENCE          0
            226 CALL                     0
            234 CACHE
            236 RETURN_VALUE
        >>  238 PUSH_EXC_INFO

616         240 POP_TOP

617         242 POP_EXCEPT
            244 LOAD_CONST               3 (None)
            246 RETURN_VALUE
        >>  248 COPY                     3
            250 POP_EXCEPT
            252 RERAISE                  1

619     >>  254 LOAD_CONST               4 ('आज')
            256 LOAD_FAST                0 (text)
            258 CONTAINS_OP              0
            260 POP_JUMP_IF_FALSE        2 (to 266)

620         262 LOAD_FAST                1 (today)
            264 RETURN_VALUE

621     >>  266 LOAD_CONST               5 ('कल')
            268 LOAD_FAST                0 (text)
            270 CONTAINS_OP              0
            272 POP_JUMP_IF_FALSE       21 (to 316)

622         274 LOAD_CONST               6 (0)
            276 LOAD_CONST               7 (('timedelta',))
            278 IMPORT_NAME              0 (datetime)
            280 IMPORT_FROM              7 (timedelta)
            282 STORE_FAST               3 (timedelta)
            284 POP_TOP

623         286 LOAD_FAST                1 (today)
            288 PUSH_NULL
            290 LOAD_FAST                3 (timedelta)
            292 LOAD_CONST               8 (1)
            294 KW_NAMES                 9 (('days',))
            296 UNPACK_SEQUENCE          1
            300 CALL                     1
            308 CACHE
            310 BINARY_OP                0 (+)
            314 RETURN_VALUE

625     >>  316 LOAD_CONST               3 (None)
            318 RETURN_VALUE
ExceptionTable:
  124 to 234 -> 238 [0]
  238 to 240 -> 248 [1] lasti

Disassembly of <code object send_whatsapp_message at 0x000001EFF4B5C1D0, file "tools.py", line 636>:
636           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

654           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              0 (pyautogui)
             12 STORE_FAST               2 (pyautogui)

655          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              1 (asyncio)
             20 STORE_FAST               3 (asyncio)

656          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              2 (os)
             28 STORE_FAST               4 (os)

658          30 NOP

659          32 LOAD_GLOBAL              7 (NULL + print)
             42 CACHE
             44 LOAD_CONST               3 ('📨 WhatsApp भेजने की प्रक्रिया शुरू: ')
             46 LOAD_FAST                0 (contact)
             48 FORMAT_VALUE             0
             50 LOAD_CONST               4 (' -> ')
             52 LOAD_FAST                1 (message)
             54 FORMAT_VALUE             0
             56 BUILD_STRING             4
             58 UNPACK_SEQUENCE          1
             62 CALL                     1
             70 CACHE
             72 POP_TOP

660          74 PUSH_NULL
             76 LOAD_FAST                3 (asyncio)
             78 LOAD_ATTR                4 (os)
             98 CACHE
            100 UNPACK_SEQUENCE          1
            104 CALL                     1
            112 CACHE
            114 GET_AWAITABLE            0
            116 LOAD_CONST               2 (None)
        >>  118 SEND                     3 (to 128)
            122 RESUME                   3
            124 JUMP_BACKWARD_NO_INTERRUPT     4 (to 118)
            126 STORE_FAST               5 (original_pos)

663     >>  128 PUSH_NULL
            130 LOAD_FAST                3 (asyncio)
            132 LOAD_ATTR                4 (os)
            152 CACHE
            154 LOAD_CONST               5 ('win')
            156 UNPACK_SEQUENCE          2
            160 CALL                     2
            168 CACHE
            170 GET_AWAITABLE            0
            172 LOAD_CONST               2 (None)
        >>  174 SEND                     3 (to 184)
            178 RESUME                   3
            180 JUMP_BACKWARD_NO_INTERRUPT     4 (to 174)
            182 POP_TOP

664     >>  184 PUSH_NULL
            186 LOAD_FAST                3 (asyncio)
            188 LOAD_ATTR                7 (NULL|self + print)
            208 CACHE
            210 CACHE
            212 CACHE
            214 GET_AWAITABLE            0
            216 LOAD_CONST               2 (None)
        >>  218 SEND                     3 (to 228)
            222 RESUME                   3
            224 JUMP_BACKWARD_NO_INTERRUPT     4 (to 218)
            226 POP_TOP

667     >>  228 PUSH_NULL
            230 LOAD_FAST                3 (asyncio)
            232 LOAD_ATTR                4 (os)
            252 CACHE
            254 LOAD_CONST               7 ('whatsapp')
            256 LOAD_CONST               8 (0.1)
            258 KW_NAMES                 9 (('interval',))
            260 UNPACK_SEQUENCE          3
            264 CALL                     3
            272 CACHE
            274 GET_AWAITABLE            0
            276 LOAD_CONST               2 (None)
        >>  278 SEND                     3 (to 288)
            282 RESUME                   3
            284 JUMP_BACKWARD_NO_INTERRUPT     4 (to 278)
            286 POP_TOP

668     >>  288 PUSH_NULL
            290 LOAD_FAST                3 (asyncio)
            292 LOAD_ATTR                7 (NULL|self + print)
            312 CACHE
            314 CACHE
            316 CACHE
            318 GET_AWAITABLE            0
            320 LOAD_CONST               2 (None)
        >>  322 SEND                     3 (to 332)
            326 RESUME                   3
            328 JUMP_BACKWARD_NO_INTERRUPT     4 (to 322)
            330 POP_TOP

669     >>  332 PUSH_NULL
            334 LOAD_FAST                3 (asyncio)
            336 LOAD_ATTR                4 (os)
            356 CACHE
            358 LOAD_CONST              10 ('enter')
            360 UNPACK_SEQUENCE          2
            364 CALL                     2
            372 CACHE
            374 GET_AWAITABLE            0
            376 LOAD_CONST               2 (None)
        >>  378 SEND                     3 (to 388)
            382 RESUME                   3
            384 JUMP_BACKWARD_NO_INTERRUPT     4 (to 378)
            386 POP_TOP

670     >>  388 PUSH_NULL
            390 LOAD_FAST                3 (asyncio)
            392 LOAD_ATTR                7 (NULL|self + print)
            412 CACHE
            414 CACHE
            416 CACHE
            418 GET_AWAITABLE            0
            420 LOAD_CONST               2 (None)
        >>  422 SEND                     3 (to 432)
            426 RESUME                   3
            428 JUMP_BACKWARD_NO_INTERRUPT     4 (to 422)
            430 POP_TOP

673     >>  432 PUSH_NULL
            434 LOAD_FAST                3 (asyncio)
            436 LOAD_ATTR                4 (os)
            456 CACHE
            458 LOAD_CONST              12 ('ctrl')
            460 LOAD_CONST              13 ('f')
            462 UNPACK_SEQUENCE          3
            466 CALL                     3
            474 CACHE
            476 GET_AWAITABLE            0
            478 LOAD_CONST               2 (None)
        >>  480 SEND                     3 (to 490)
            484 RESUME                   3
            486 JUMP_BACKWARD_NO_INTERRUPT     4 (to 480)
            488 POP_TOP

674     >>  490 PUSH_NULL
            492 LOAD_FAST                3 (asyncio)
            494 LOAD_ATTR                7 (NULL|self + print)
            514 CACHE
            516 CACHE
            518 CACHE
            520 GET_AWAITABLE            0
            522 LOAD_CONST               2 (None)
        >>  524 SEND                     3 (to 534)
            528 RESUME                   3
            530 JUMP_BACKWARD_NO_INTERRUPT     4 (to 524)
            532 POP_TOP

677     >>  534 PUSH_NULL
            536 LOAD_FAST                3 (asyncio)
            538 LOAD_ATTR                4 (os)
            558 CACHE
            560 LOAD_FAST                0 (contact)
            562 LOAD_CONST               8 (0.1)
            564 KW_NAMES                 9 (('interval',))
            566 UNPACK_SEQUENCE          3
            570 CALL                     3
            578 CACHE
            580 GET_AWAITABLE            0
            582 LOAD_CONST               2 (None)
        >>  584 SEND                     3 (to 594)
            588 RESUME                   3
            590 JUMP_BACKWARD_NO_INTERRUPT     4 (to 584)
            592 POP_TOP

678     >>  594 PUSH_NULL
            596 LOAD_FAST                3 (asyncio)
            598 LOAD_ATTR                7 (NULL|self + print)
            618 CACHE
            620 CACHE
            622 CACHE
            624 GET_AWAITABLE            0
            626 LOAD_CONST               2 (None)
        >>  628 SEND                     3 (to 638)
            632 RESUME                   3
            634 JUMP_BACKWARD_NO_INTERRUPT     4 (to 628)
            636 POP_TOP

679     >>  638 PUSH_NULL
            640 LOAD_FAST                3 (asyncio)
            642 LOAD_ATTR                4 (os)
            662 CACHE
            664 LOAD_CONST              16 ('down')
            666 UNPACK_SEQUENCE          2
            670 CALL                     2
            678 CACHE
            680 GET_AWAITABLE            0
            682 LOAD_CONST               2 (None)
        >>  684 SEND                     3 (to 694)
            688 RESUME                   3
            690 JUMP_BACKWARD_NO_INTERRUPT     4 (to 684)
            692 POP_TOP

680     >>  694 PUSH_NULL
            696 LOAD_FAST                3 (asyncio)
            698 LOAD_ATTR                7 (NULL|self + print)
            718 CACHE
            720 CACHE
            722 CACHE
            724 GET_AWAITABLE            0
            726 LOAD_CONST               2 (None)
        >>  728 SEND                     3 (to 738)
            732 RESUME                   3
            734 JUMP_BACKWARD_NO_INTERRUPT     4 (to 728)
            736 POP_TOP

681     >>  738 PUSH_NULL
            740 LOAD_FAST                3 (asyncio)
            742 LOAD_ATTR                4 (os)
            762 CACHE
            764 LOAD_CONST              10 ('enter')
            766 UNPACK_SEQUENCE          2
            770 CALL                     2
            778 CACHE
            780 GET_AWAITABLE            0
            782 LOAD_CONST               2 (None)
        >>  784 SEND                     3 (to 794)
            788 RESUME                   3
            790 JUMP_BACKWARD_NO_INTERRUPT     4 (to 784)
            792 POP_TOP

682     >>  794 PUSH_NULL
            796 LOAD_FAST                3 (asyncio)
            798 LOAD_ATTR                7 (NULL|self + print)
            818 CACHE
            820 CACHE
            822 CACHE
            824 GET_AWAITABLE            0
            826 LOAD_CONST               2 (None)
        >>  828 SEND                     3 (to 838)
            832 RESUME                   3
            834 JUMP_BACKWARD_NO_INTERRUPT     4 (to 828)
            836 POP_TOP

685     >>  838 PUSH_NULL
            840 LOAD_FAST                3 (asyncio)
            842 LOAD_ATTR                4 (os)
            862 CACHE
            864 LOAD_FAST                1 (message)
            866 LOAD_CONST              18 (0.06)
            868 KW_NAMES                 9 (('interval',))
            870 UNPACK_SEQUENCE          3
            874 CALL                     3
            882 CACHE
            884 GET_AWAITABLE            0
            886 LOAD_CONST               2 (None)
        >>  888 SEND                     3 (to 898)
            892 RESUME                   3
            894 JUMP_BACKWARD_NO_INTERRUPT     4 (to 888)
            896 POP_TOP

686     >>  898 PUSH_NULL
            900 LOAD_FAST                3 (asyncio)
            902 LOAD_ATTR                4 (os)
            922 CACHE
            924 LOAD_CONST              10 ('enter')
            926 UNPACK_SEQUENCE          2
            930 CALL                     2
            938 CACHE
            940 GET_AWAITABLE            0
            942 LOAD_CONST               2 (None)
        >>  944 SEND                     3 (to 954)
            948 RESUME                   3
            950 JUMP_BACKWARD_NO_INTERRUPT     4 (to 944)
            952 POP_TOP

687     >>  954 PUSH_NULL
            956 LOAD_FAST                3 (asyncio)
            958 LOAD_ATTR                7 (NULL|self + print)
            978 CACHE
            980 CACHE
            982 CACHE
            984 GET_AWAITABLE            0
            986 LOAD_CONST               2 (None)
        >>  988 SEND                     3 (to 998)
            992 RESUME                   3
            994 JUMP_BACKWARD_NO_INTERRUPT     4 (to 988)
            996 POP_TOP

691     >>  998 LOAD_CONST              19 ("✅ '")
           1000 LOAD_FAST                0 (contact)
           1002 FORMAT_VALUE             0
           1004 LOAD_CONST              20 ('\' को संदेश भेजा गया: "')
           1006 LOAD_FAST                1 (message)
           1008 FORMAT_VALUE             0
           1010 LOAD_CONST              21 ('"\n🧠 क्या कुछ और भेजना है sir? जवाब दें — Nova उस संदेश को भेज देगा।')
           1012 BUILD_STRING             5

690        1014 NOP

699        1016 NOP

700        1018 PUSH_NULL
           1020 LOAD_FAST                3 (asyncio)
           1022 LOAD_ATTR                4 (os)
           1042 CACHE
           1044 LOAD_FAST                5 (original_pos)
           1046 LOAD_ATTR               11 (NULL|self + position)
           1066 CACHE
           1068 LOAD_CONST               8 (0.1)
           1070 KW_NAMES                22 (('duration',))
           1072 UNPACK_SEQUENCE          4
           1076 CALL                     4
           1084 CACHE
           1086 GET_AWAITABLE            0
           1088 LOAD_CONST               2 (None)
        >> 1090 SEND                     3 (to 1100)
           1094 RESUME                   3
           1096 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1090)
           1098 POP_TOP
        >> 1100 RETURN_VALUE
        >> 1102 PUSH_EXC_INFO

701        1104 POP_TOP

702        1106 POP_EXCEPT
           1108 RETURN_VALUE
        >> 1110 COPY                     3
           1112 POP_EXCEPT
           1114 RERAISE                  1
        >> 1116 PUSH_EXC_INFO

695        1118 LOAD_GLOBAL             26 (Exception)
           1128 CACHE
           1130 CHECK_EXC_MATCH
           1132 POP_JUMP_IF_FALSE       77 (to 1288)
           1134 STORE_FAST               6 (e)

696        1136 LOAD_CONST              23 ('❌ संदेश भेजने में त्रुटि: ')
           1138 LOAD_GLOBAL             29 (NULL + str)
           1148 CACHE
           1150 LOAD_FAST                6 (e)
           1152 UNPACK_SEQUENCE          1
           1156 CALL                     1
           1164 CACHE
           1166 FORMAT_VALUE             0
           1168 BUILD_STRING             2
           1170 SWAP                     2
           1172 POP_EXCEPT
           1174 LOAD_CONST               2 (None)
           1176 STORE_FAST               6 (e)
           1178 DELETE_FAST              6 (e)

699        1180 NOP

700        1182 PUSH_NULL
           1184 LOAD_FAST                3 (asyncio)
           1186 LOAD_ATTR                4 (os)
           1206 CACHE
           1208 LOAD_FAST                5 (original_pos)
           1210 LOAD_ATTR               11 (NULL|self + position)
           1230 CACHE
           1232 LOAD_CONST               8 (0.1)
           1234 KW_NAMES                22 (('duration',))
           1236 UNPACK_SEQUENCE          4
           1240 CALL                     4
           1248 CACHE
           1250 GET_AWAITABLE            0
           1252 LOAD_CONST               2 (None)
        >> 1254 SEND                     3 (to 1264)
           1258 RESUME                   3
           1260 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1254)
           1262 POP_TOP
        >> 1264 RETURN_VALUE
        >> 1266 PUSH_EXC_INFO

701        1268 POP_TOP

702        1270 POP_EXCEPT
           1272 RETURN_VALUE
        >> 1274 COPY                     3
           1276 POP_EXCEPT
           1278 RERAISE                  1
        >> 1280 LOAD_CONST               2 (None)
           1282 STORE_FAST               6 (e)
           1284 DELETE_FAST              6 (e)
           1286 RERAISE                  1

695     >> 1288 RERAISE                  0
        >> 1290 COPY                     3
           1292 POP_EXCEPT
           1294 RERAISE                  1
        >> 1296 PUSH_EXC_INFO

699        1298 NOP

700        1300 PUSH_NULL
           1302 LOAD_FAST                3 (asyncio)
           1304 LOAD_ATTR                4 (os)
           1324 CACHE
           1326 LOAD_FAST                5 (original_pos)
           1328 LOAD_ATTR               11 (NULL|self + position)
           1348 CACHE
           1350 LOAD_CONST               8 (0.1)
           1352 KW_NAMES                22 (('duration',))
           1354 UNPACK_SEQUENCE          4
           1358 CALL                     4
           1366 CACHE
           1368 GET_AWAITABLE            0
           1370 LOAD_CONST               2 (None)
        >> 1372 SEND                     3 (to 1382)
           1376 RESUME                   3
           1378 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1372)
           1380 POP_TOP
        >> 1382 RERAISE                  0
        >> 1384 PUSH_EXC_INFO

701        1386 POP_TOP

702        1388 POP_EXCEPT
           1390 RERAISE                  0
        >> 1392 COPY                     3
           1394 POP_EXCEPT
           1396 RERAISE                  1
        >> 1398 COPY                     3
           1400 POP_EXCEPT
           1402 RERAISE                  1
ExceptionTable:
  32 to 1012 -> 1116 [0]
  1018 to 1098 -> 1102 [1]
  1102 to 1104 -> 1110 [2] lasti
  1116 to 1134 -> 1290 [1] lasti
  1136 to 1168 -> 1280 [1] lasti
  1170 to 1170 -> 1290 [1] lasti
  1172 to 1178 -> 1296 [0]
  1182 to 1262 -> 1266 [1]
  1266 to 1268 -> 1274 [2] lasti
  1280 to 1288 -> 1290 [1] lasti
  1290 to 1294 -> 1296 [0]
  1296 to 1296 -> 1398 [1] lasti
  1300 to 1380 -> 1384 [2]
  1382 to 1382 -> 1398 [1] lasti
  1384 to 1386 -> 1392 [3] lasti
  1388 to 1396 -> 1398 [1] lasti

Disassembly of <code object write_in_notepad at 0x000001EFF4B614E0, file "tools.py", line 710>:
710           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

726           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              0 (pyautogui)
             12 STORE_FAST               3 (pyautogui)

727          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              1 (asyncio)
             20 STORE_FAST               4 (asyncio)

728          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              2 (datetime)
             28 STORE_FAST               5 (datetime)

730          30 NOP

731          32 LOAD_GLOBAL              7 (NULL + print)
             42 CACHE
             44 LOAD_CONST               3 ('📝 Starting Notepad writing process: ')
             46 LOAD_FAST                2 (document_type)
             48 FORMAT_VALUE             0
             50 LOAD_CONST               4 (' - ')
             52 LOAD_FAST                0 (title)
             54 FORMAT_VALUE             0
             56 BUILD_STRING             4
             58 UNPACK_SEQUENCE          1
             62 CALL                     1
             70 CACHE
             72 POP_TOP

732          74 PUSH_NULL
             76 LOAD_FAST                4 (asyncio)
             78 LOAD_ATTR                4 (datetime)
             98 CACHE
            100 UNPACK_SEQUENCE          1
            104 CALL                     1
            112 CACHE
            114 GET_AWAITABLE            0
            116 LOAD_CONST               2 (None)
        >>  118 SEND                     3 (to 128)
            122 RESUME                   3
            124 JUMP_BACKWARD_NO_INTERRUPT     4 (to 118)
            126 STORE_FAST               6 (original_pos)

735     >>  128 LOAD_GLOBAL              7 (NULL + print)
            138 CACHE
            140 LOAD_CONST               5 ('🔧 Opening Notepad...')
            142 UNPACK_SEQUENCE          1
            146 CALL                     1
            154 CACHE
            156 POP_TOP

736         158 PUSH_NULL
            160 LOAD_FAST                4 (asyncio)
            162 LOAD_ATTR                4 (datetime)
            182 CACHE
            184 LOAD_CONST               6 ('win')
            186 UNPACK_SEQUENCE          2
            190 CALL                     2
            198 CACHE
            200 GET_AWAITABLE            0
            202 LOAD_CONST               2 (None)
        >>  204 SEND                     3 (to 214)
            208 RESUME                   3
            210 JUMP_BACKWARD_NO_INTERRUPT     4 (to 204)
            212 POP_TOP

737     >>  214 PUSH_NULL
            216 LOAD_FAST                4 (asyncio)
            218 LOAD_ATTR                7 (NULL|self + print)
            238 CACHE
            240 CACHE
            242 CACHE
            244 GET_AWAITABLE            0
            246 LOAD_CONST               2 (None)
        >>  248 SEND                     3 (to 258)
            252 RESUME                   3
            254 JUMP_BACKWARD_NO_INTERRUPT     4 (to 248)
            256 POP_TOP

740     >>  258 PUSH_NULL
            260 LOAD_FAST                4 (asyncio)
            262 LOAD_ATTR                4 (datetime)
            282 CACHE
            284 LOAD_CONST               8 ('notepad')
            286 LOAD_CONST               9 (0.1)
            288 KW_NAMES                10 (('interval',))
            290 UNPACK_SEQUENCE          3
            294 CALL                     3
            302 CACHE
            304 GET_AWAITABLE            0
            306 LOAD_CONST               2 (None)
        >>  308 SEND                     3 (to 318)
            312 RESUME                   3
            314 JUMP_BACKWARD_NO_INTERRUPT     4 (to 308)
            316 POP_TOP

741     >>  318 PUSH_NULL
            320 LOAD_FAST                4 (asyncio)
            322 LOAD_ATTR                7 (NULL|self + print)
            342 CACHE
            344 CACHE
            346 CACHE
            348 GET_AWAITABLE            0
            350 LOAD_CONST               2 (None)
        >>  352 SEND                     3 (to 362)
            356 RESUME                   3
            358 JUMP_BACKWARD_NO_INTERRUPT     4 (to 352)
            360 POP_TOP

742     >>  362 PUSH_NULL
            364 LOAD_FAST                4 (asyncio)
            366 LOAD_ATTR                4 (datetime)
            386 CACHE
            388 LOAD_CONST              11 ('enter')
            390 UNPACK_SEQUENCE          2
            394 CALL                     2
            402 CACHE
            404 GET_AWAITABLE            0
            406 LOAD_CONST               2 (None)
        >>  408 SEND                     3 (to 418)
            412 RESUME                   3
            414 JUMP_BACKWARD_NO_INTERRUPT     4 (to 408)
            416 POP_TOP

743     >>  418 PUSH_NULL
            420 LOAD_FAST                4 (asyncio)
            422 LOAD_ATTR                7 (NULL|self + print)
            442 CACHE
            444 CACHE
            446 CACHE
            448 GET_AWAITABLE            0
            450 LOAD_CONST               2 (None)
        >>  452 SEND                     3 (to 462)
            456 RESUME                   3
            458 JUMP_BACKWARD_NO_INTERRUPT     4 (to 452)
            460 POP_TOP

746     >>  462 LOAD_GLOBAL              7 (NULL + print)
            472 CACHE
            474 LOAD_CONST              13 ('📄 Creating new file...')
            476 UNPACK_SEQUENCE          1
            480 CALL                     1
            488 CACHE
            490 POP_TOP

747         492 PUSH_NULL
            494 LOAD_FAST                4 (asyncio)
            496 LOAD_ATTR                4 (datetime)
            516 CACHE
            518 LOAD_CONST              14 ('ctrl')
            520 LOAD_CONST              15 ('n')
            522 UNPACK_SEQUENCE          3
            526 CALL                     3
            534 CACHE
            536 GET_AWAITABLE            0
            538 LOAD_CONST               2 (None)
        >>  540 SEND                     3 (to 550)
            544 RESUME                   3
            546 JUMP_BACKWARD_NO_INTERRUPT     4 (to 540)
            548 POP_TOP

748     >>  550 PUSH_NULL
            552 LOAD_FAST                4 (asyncio)
            554 LOAD_ATTR                7 (NULL|self + print)
            574 CACHE
            576 CACHE
            578 CACHE
            580 GET_AWAITABLE            0
            582 LOAD_CONST               2 (None)
        >>  584 SEND                     3 (to 594)
            588 RESUME                   3
            590 JUMP_BACKWARD_NO_INTERRUPT     4 (to 584)
            592 POP_TOP

751     >>  594 PUSH_NULL
            596 LOAD_FAST                4 (asyncio)
            598 LOAD_ATTR                4 (datetime)
            618 CACHE
            620 LOAD_CONST              14 ('ctrl')
            622 LOAD_CONST              16 ('a')
            624 UNPACK_SEQUENCE          3
            628 CALL                     3
            636 CACHE
            638 GET_AWAITABLE            0
            640 LOAD_CONST               2 (None)
        >>  642 SEND                     3 (to 652)
            646 RESUME                   3
            648 JUMP_BACKWARD_NO_INTERRUPT     4 (to 642)
            650 POP_TOP

752     >>  652 PUSH_NULL
            654 LOAD_FAST                4 (asyncio)
            656 LOAD_ATTR                7 (NULL|self + print)
            676 CACHE
            678 CACHE
            680 CACHE
            682 GET_AWAITABLE            0
            684 LOAD_CONST               2 (None)
        >>  686 SEND                     3 (to 696)
            690 RESUME                   3
            692 JUMP_BACKWARD_NO_INTERRUPT     4 (to 686)
            694 POP_TOP

753     >>  696 PUSH_NULL
            698 LOAD_FAST                4 (asyncio)
            700 LOAD_ATTR                4 (datetime)
            720 CACHE
            722 LOAD_CONST              18 ('delete')
            724 UNPACK_SEQUENCE          2
            728 CALL                     2
            736 CACHE
            738 GET_AWAITABLE            0
            740 LOAD_CONST               2 (None)
        >>  742 SEND                     3 (to 752)
            746 RESUME                   3
            748 JUMP_BACKWARD_NO_INTERRUPT     4 (to 742)
            750 POP_TOP

754     >>  752 PUSH_NULL
            754 LOAD_FAST                4 (asyncio)
            756 LOAD_ATTR                7 (NULL|self + print)
            776 CACHE
            778 CACHE
            780 CACHE
            782 GET_AWAITABLE            0
            784 LOAD_CONST               2 (None)
        >>  786 SEND                     3 (to 796)
            790 RESUME                   3
            792 JUMP_BACKWARD_NO_INTERRUPT     4 (to 786)
            794 POP_TOP

757     >>  796 LOAD_GLOBAL              7 (NULL + print)
            806 CACHE
            808 LOAD_CONST              19 ('✍️ Writing document content...')
            810 UNPACK_SEQUENCE          1
            814 CALL                     1
            822 CACHE
            824 POP_TOP

760         826 LOAD_FAST                5 (datetime)
            828 LOAD_ATTR                2 (asyncio)
            848 CACHE
            850 CACHE
            852 CACHE
            854 CACHE
            856 CACHE
            858 CACHE
            860 UNPACK_SEQUENCE          0
            864 CALL                     0
            872 CACHE
            874 STORE_SUBSCR
            878 CACHE
            880 CACHE
            882 CACHE
            884 CACHE
            886 CACHE
            888 CACHE
            890 CACHE
            892 CACHE
            894 CACHE
            896 LOAD_CONST              20 ('%d/%m/%Y')
            898 UNPACK_SEQUENCE          1
            902 CALL                     1
            910 CACHE
            912 STORE_FAST               7 (current_date)

761         914 PUSH_NULL
            916 LOAD_FAST                4 (asyncio)
            918 LOAD_ATTR                4 (datetime)
            938 CACHE
            940 LOAD_CONST              21 ('Date: ')
            942 LOAD_FAST                7 (current_date)
            944 FORMAT_VALUE             0
            946 BUILD_STRING             2
            948 LOAD_CONST              22 (0.05)
            950 KW_NAMES                10 (('interval',))
            952 UNPACK_SEQUENCE          3
            956 CALL                     3
            964 CACHE
            966 GET_AWAITABLE            0
            968 LOAD_CONST               2 (None)
        >>  970 SEND                     3 (to 980)
            974 RESUME                   3
            976 JUMP_BACKWARD_NO_INTERRUPT     4 (to 970)
            978 POP_TOP

762     >>  980 PUSH_NULL
            982 LOAD_FAST                4 (asyncio)
            984 LOAD_ATTR                4 (datetime)
           1004 CACHE
           1006 LOAD_CONST              11 ('enter')
           1008 UNPACK_SEQUENCE          2
           1012 CALL                     2
           1020 CACHE
           1022 GET_AWAITABLE            0
           1024 LOAD_CONST               2 (None)
        >> 1026 SEND                     3 (to 1036)
           1030 RESUME                   3
           1032 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1026)
           1034 POP_TOP

763     >> 1036 PUSH_NULL
           1038 LOAD_FAST                4 (asyncio)
           1040 LOAD_ATTR                4 (datetime)
           1060 CACHE
           1062 LOAD_CONST              11 ('enter')
           1064 UNPACK_SEQUENCE          2
           1068 CALL                     2
           1076 CACHE
           1078 GET_AWAITABLE            0
           1080 LOAD_CONST               2 (None)
        >> 1082 SEND                     3 (to 1092)
           1086 RESUME                   3
           1088 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1082)
           1090 POP_TOP

766     >> 1092 PUSH_NULL
           1094 LOAD_FAST                4 (asyncio)
           1096 LOAD_ATTR                4 (datetime)
           1116 CACHE
           1118 LOAD_CONST              23 ('Subject: ')
           1120 LOAD_FAST                0 (title)
           1122 FORMAT_VALUE             0
           1124 BUILD_STRING             2
           1126 LOAD_CONST              22 (0.05)
           1128 KW_NAMES                10 (('interval',))
           1130 UNPACK_SEQUENCE          3
           1134 CALL                     3
           1142 CACHE
           1144 GET_AWAITABLE            0
           1146 LOAD_CONST               2 (None)
        >> 1148 SEND                     3 (to 1158)
           1152 RESUME                   3
           1154 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1148)
           1156 POP_TOP

767     >> 1158 PUSH_NULL
           1160 LOAD_FAST                4 (asyncio)
           1162 LOAD_ATTR                4 (datetime)
           1182 CACHE
           1184 LOAD_CONST              11 ('enter')
           1186 UNPACK_SEQUENCE          2
           1190 CALL                     2
           1198 CACHE
           1200 GET_AWAITABLE            0
           1202 LOAD_CONST               2 (None)
        >> 1204 SEND                     3 (to 1214)
           1208 RESUME                   3
           1210 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1204)
           1212 POP_TOP

768     >> 1214 PUSH_NULL
           1216 LOAD_FAST                4 (asyncio)
           1218 LOAD_ATTR                4 (datetime)
           1238 CACHE
           1240 LOAD_CONST              11 ('enter')
           1242 UNPACK_SEQUENCE          2
           1246 CALL                     2
           1254 CACHE
           1256 GET_AWAITABLE            0
           1258 LOAD_CONST               2 (None)
        >> 1260 SEND                     3 (to 1270)
           1264 RESUME                   3
           1266 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1260)
           1268 POP_TOP

771     >> 1270 LOAD_FAST                2 (document_type)
           1272 STORE_SUBSCR
           1276 CACHE
           1278 CACHE
           1280 CACHE
           1282 CACHE
           1284 CACHE
           1286 CACHE
           1288 CACHE
           1290 CACHE
           1292 CACHE
           1294 UNPACK_SEQUENCE          0
           1298 CALL                     0
           1306 CACHE
           1308 LOAD_CONST              24 (('letter', 'application'))
           1310 CONTAINS_OP              0
           1312 POP_JUMP_IF_FALSE       86 (to 1486)

772        1314 PUSH_NULL
           1316 LOAD_FAST                4 (asyncio)
           1318 LOAD_ATTR                4 (datetime)
           1338 CACHE
           1340 LOAD_CONST              25 ('Dear Sir/Madam,')
           1342 LOAD_CONST              22 (0.05)
           1344 KW_NAMES                10 (('interval',))
           1346 UNPACK_SEQUENCE          3
           1350 CALL                     3
           1358 CACHE
           1360 GET_AWAITABLE            0
           1362 LOAD_CONST               2 (None)
        >> 1364 SEND                     3 (to 1374)
           1368 RESUME                   3
           1370 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1364)
           1372 POP_TOP

773     >> 1374 PUSH_NULL
           1376 LOAD_FAST                4 (asyncio)
           1378 LOAD_ATTR                4 (datetime)
           1398 CACHE
           1400 LOAD_CONST              11 ('enter')
           1402 UNPACK_SEQUENCE          2
           1406 CALL                     2
           1414 CACHE
           1416 GET_AWAITABLE            0
           1418 LOAD_CONST               2 (None)
        >> 1420 SEND                     3 (to 1430)
           1424 RESUME                   3
           1426 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1420)
           1428 POP_TOP

774     >> 1430 PUSH_NULL
           1432 LOAD_FAST                4 (asyncio)
           1434 LOAD_ATTR                4 (datetime)
           1454 CACHE
           1456 LOAD_CONST              11 ('enter')
           1458 UNPACK_SEQUENCE          2
           1462 CALL                     2
           1470 CACHE
           1472 GET_AWAITABLE            0
           1474 LOAD_CONST               2 (None)
        >> 1476 SEND                     3 (to 1486)
           1480 RESUME                   3
           1482 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1476)
           1484 POP_TOP

777     >> 1486 LOAD_FAST                1 (content)
           1488 STORE_SUBSCR
           1492 CACHE
           1494 CACHE
           1496 CACHE
           1498 CACHE
           1500 CACHE
           1502 CACHE
           1504 CACHE
           1506 CACHE
           1508 CACHE
           1510 LOAD_CONST              26 ('\n\n')
           1512 UNPACK_SEQUENCE          1
           1516 CALL                     1
           1524 CACHE
           1526 STORE_FAST               8 (paragraphs)

778        1528 LOAD_GLOBAL             29 (NULL + enumerate)
           1538 CACHE
           1540 LOAD_FAST                8 (paragraphs)
           1542 UNPACK_SEQUENCE          1
           1546 CALL                     1
           1554 CACHE
           1556 GET_ITER
        >> 1558 FOR_ITER               131 (to 1824)
           1562 CACHE
           1564 STORE_FAST               9 (i)
           1566 STORE_FAST              10 (paragraph)

779        1568 LOAD_FAST               10 (paragraph)
           1570 STORE_SUBSCR
           1574 CACHE
           1576 CACHE
           1578 CACHE
           1580 CACHE
           1582 CACHE
           1584 CACHE
           1586 CACHE
           1588 CACHE
           1590 CACHE
           1592 UNPACK_SEQUENCE          0
           1596 CALL                     0
           1604 CACHE
           1606 POP_JUMP_IF_FALSE      106 (to 1820)

781        1608 LOAD_FAST               10 (paragraph)
           1610 STORE_SUBSCR
           1614 CACHE
           1616 CACHE
           1618 CACHE
           1620 CACHE
           1622 CACHE
           1624 CACHE
           1626 CACHE
           1628 CACHE
           1630 CACHE
           1632 UNPACK_SEQUENCE          0
           1636 CALL                     0
           1644 CACHE
           1646 STORE_FAST              11 (clean_paragraph)

782        1648 PUSH_NULL
           1650 LOAD_FAST                4 (asyncio)
           1652 LOAD_ATTR                4 (datetime)
           1672 CACHE
           1674 LOAD_FAST               11 (clean_paragraph)
           1676 LOAD_CONST              27 (0.03)
           1678 KW_NAMES                10 (('interval',))
           1680 UNPACK_SEQUENCE          3
           1684 CALL                     3
           1692 CACHE
           1694 GET_AWAITABLE            0
           1696 LOAD_CONST               2 (None)
        >> 1698 SEND                     3 (to 1708)
           1702 RESUME                   3
           1704 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1698)
           1706 POP_TOP

783     >> 1708 PUSH_NULL
           1710 LOAD_FAST                4 (asyncio)
           1712 LOAD_ATTR                4 (datetime)
           1732 CACHE
           1734 LOAD_CONST              11 ('enter')
           1736 UNPACK_SEQUENCE          2
           1740 CALL                     2
           1748 CACHE
           1750 GET_AWAITABLE            0
           1752 LOAD_CONST               2 (None)
        >> 1754 SEND                     3 (to 1764)
           1758 RESUME                   3
           1760 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1754)
           1762 POP_TOP

784     >> 1764 PUSH_NULL
           1766 LOAD_FAST                4 (asyncio)
           1768 LOAD_ATTR                4 (datetime)
           1788 CACHE
           1790 LOAD_CONST              11 ('enter')
           1792 UNPACK_SEQUENCE          2
           1796 CALL                     2
           1804 CACHE
           1806 GET_AWAITABLE            0
           1808 LOAD_CONST               2 (None)
        >> 1810 SEND                     3 (to 1820)
           1814 RESUME                   3
           1816 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1810)
           1818 POP_TOP
        >> 1820 JUMP_BACKWARD          132 (to 1558)

787        1822 LOAD_FAST                2 (document_type)
        >> 1824 STORE_SUBSCR
           1828 CACHE
           1830 CACHE
           1832 CACHE
           1834 CACHE
           1836 CACHE
           1838 CACHE
           1840 CACHE
           1842 CACHE
           1844 CACHE
           1846 UNPACK_SEQUENCE          0
           1850 CALL                     0
           1858 CACHE
           1860 LOAD_CONST              24 (('letter', 'application'))
           1862 CONTAINS_OP              0
           1864 POP_JUMP_IF_FALSE      202 (to 2270)

788        1866 PUSH_NULL
           1868 LOAD_FAST                4 (asyncio)
           1870 LOAD_ATTR                4 (datetime)
           1890 CACHE
           1892 LOAD_CONST              28 ('Thank you for your time and consideration.')
           1894 LOAD_CONST              22 (0.05)
           1896 KW_NAMES                10 (('interval',))
           1898 UNPACK_SEQUENCE          3
           1902 CALL                     3
           1910 CACHE
           1912 GET_AWAITABLE            0
           1914 LOAD_CONST               2 (None)
        >> 1916 SEND                     3 (to 1926)
           1920 RESUME                   3
           1922 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1916)
           1924 POP_TOP

789     >> 1926 PUSH_NULL
           1928 LOAD_FAST                4 (asyncio)
           1930 LOAD_ATTR                4 (datetime)
           1950 CACHE
           1952 LOAD_CONST              11 ('enter')
           1954 UNPACK_SEQUENCE          2
           1958 CALL                     2
           1966 CACHE
           1968 GET_AWAITABLE            0
           1970 LOAD_CONST               2 (None)
        >> 1972 SEND                     3 (to 1982)
           1976 RESUME                   3
           1978 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1972)
           1980 POP_TOP

790     >> 1982 PUSH_NULL
           1984 LOAD_FAST                4 (asyncio)
           1986 LOAD_ATTR                4 (datetime)
           2006 CACHE
           2008 LOAD_CONST              11 ('enter')
           2010 UNPACK_SEQUENCE          2
           2014 CALL                     2
           2022 CACHE
           2024 GET_AWAITABLE            0
           2026 LOAD_CONST               2 (None)
        >> 2028 SEND                     3 (to 2038)
           2032 RESUME                   3
           2034 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2028)
           2036 POP_TOP

791     >> 2038 PUSH_NULL
           2040 LOAD_FAST                4 (asyncio)
           2042 LOAD_ATTR                4 (datetime)
           2062 CACHE
           2064 LOAD_CONST              29 ('Yours sincerely,')
           2066 LOAD_CONST              22 (0.05)
           2068 KW_NAMES                10 (('interval',))
           2070 UNPACK_SEQUENCE          3
           2074 CALL                     3
           2082 CACHE
           2084 GET_AWAITABLE            0
           2086 LOAD_CONST               2 (None)
        >> 2088 SEND                     3 (to 2098)
           2092 RESUME                   3
           2094 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2088)
           2096 POP_TOP

792     >> 2098 PUSH_NULL
           2100 LOAD_FAST                4 (asyncio)
           2102 LOAD_ATTR                4 (datetime)
           2122 CACHE
           2124 LOAD_CONST              11 ('enter')
           2126 UNPACK_SEQUENCE          2
           2130 CALL                     2
           2138 CACHE
           2140 GET_AWAITABLE            0
           2142 LOAD_CONST               2 (None)
        >> 2144 SEND                     3 (to 2154)
           2148 RESUME                   3
           2150 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2144)
           2152 POP_TOP

793     >> 2154 PUSH_NULL
           2156 LOAD_FAST                4 (asyncio)
           2158 LOAD_ATTR                4 (datetime)
           2178 CACHE
           2180 LOAD_CONST              11 ('enter')
           2182 UNPACK_SEQUENCE          2
           2186 CALL                     2
           2194 CACHE
           2196 GET_AWAITABLE            0
           2198 LOAD_CONST               2 (None)
        >> 2200 SEND                     3 (to 2210)
           2204 RESUME                   3
           2206 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2200)
           2208 POP_TOP

794     >> 2210 PUSH_NULL
           2212 LOAD_FAST                4 (asyncio)
           2214 LOAD_ATTR                4 (datetime)
           2234 CACHE
           2236 LOAD_CONST              30 ('[Your Name]')
           2238 LOAD_CONST              22 (0.05)
           2240 KW_NAMES                10 (('interval',))
           2242 UNPACK_SEQUENCE          3
           2246 CALL                     3
           2254 CACHE
           2256 GET_AWAITABLE            0
           2258 LOAD_CONST               2 (None)
        >> 2260 SEND                     3 (to 2270)
           2264 RESUME                   3
           2266 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2260)
           2268 POP_TOP

797     >> 2270 LOAD_GLOBAL              7 (NULL + print)
           2280 CACHE
           2282 LOAD_CONST              31 ('💾 Saving document...')
           2284 UNPACK_SEQUENCE          1
           2288 CALL                     1
           2296 CACHE
           2298 POP_TOP

798        2300 PUSH_NULL
           2302 LOAD_FAST                4 (asyncio)
           2304 LOAD_ATTR                7 (NULL|self + print)
           2324 CACHE
           2326 CACHE
           2328 CACHE
           2330 GET_AWAITABLE            0
           2332 LOAD_CONST               2 (None)
        >> 2334 SEND                     3 (to 2344)
           2338 RESUME                   3
           2340 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2334)
           2342 POP_TOP

799     >> 2344 PUSH_NULL
           2346 LOAD_FAST                4 (asyncio)
           2348 LOAD_ATTR                4 (datetime)
           2368 CACHE
           2370 LOAD_CONST              14 ('ctrl')
           2372 LOAD_CONST              32 ('s')
           2374 UNPACK_SEQUENCE          3
           2378 CALL                     3
           2386 CACHE
           2388 GET_AWAITABLE            0
           2390 LOAD_CONST               2 (None)
        >> 2392 SEND                     3 (to 2402)
           2396 RESUME                   3
           2398 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2392)
           2400 POP_TOP

800     >> 2402 PUSH_NULL
           2404 LOAD_FAST                4 (asyncio)
           2406 LOAD_ATTR                7 (NULL|self + print)
           2426 CACHE
           2428 CACHE
           2430 CACHE
           2432 GET_AWAITABLE            0
           2434 LOAD_CONST               2 (None)
        >> 2436 SEND                     3 (to 2446)
           2440 RESUME                   3
           2442 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2436)
           2444 POP_TOP

803     >> 2446 LOAD_FAST                0 (title)
           2448 STORE_SUBSCR
           2452 CACHE
           2454 CACHE
           2456 CACHE
           2458 CACHE
           2460 CACHE
           2462 CACHE
           2464 CACHE
           2466 CACHE
           2468 CACHE
           2470 LOAD_CONST              34 (' ')
           2472 LOAD_CONST              35 ('_')
           2474 UNPACK_SEQUENCE          2
           2478 CALL                     2
           2486 CACHE
           2488 STORE_SUBSCR
           2492 CACHE
           2494 CACHE
           2496 CACHE
           2498 CACHE
           2500 CACHE
           2502 CACHE
           2504 CACHE
           2506 CACHE
           2508 CACHE
           2510 LOAD_CONST              36 ('/')
           2512 LOAD_CONST              35 ('_')
           2514 UNPACK_SEQUENCE          2
           2518 CALL                     2
           2526 CACHE
           2528 STORE_SUBSCR
           2532 CACHE
           2534 CACHE
           2536 CACHE
           2538 CACHE
           2540 CACHE
           2542 CACHE
           2544 CACHE
           2546 CACHE
           2548 CACHE
           2550 LOAD_CONST              37 ('\\')
           2552 LOAD_CONST              35 ('_')
           2554 UNPACK_SEQUENCE          2
           2558 CALL                     2
           2566 CACHE
           2568 STORE_FAST              12 (safe_title)

804        2570 LOAD_FAST                2 (document_type)
           2572 FORMAT_VALUE             0
           2574 LOAD_CONST              35 ('_')
           2576 LOAD_FAST               12 (safe_title)
           2578 FORMAT_VALUE             0
           2580 LOAD_CONST              35 ('_')
           2582 LOAD_FAST                7 (current_date)
           2584 STORE_SUBSCR
           2588 CACHE
           2590 CACHE
           2592 CACHE
           2594 CACHE
           2596 CACHE
           2598 CACHE
           2600 CACHE
           2602 CACHE
           2604 CACHE
           2606 LOAD_CONST              36 ('/')
           2608 LOAD_CONST              35 ('_')
           2610 UNPACK_SEQUENCE          2
           2614 CALL                     2
           2622 CACHE
           2624 FORMAT_VALUE             0
           2626 LOAD_CONST              38 ('.txt')
           2628 BUILD_STRING             6
           2630 STORE_FAST              13 (filename)

806        2632 PUSH_NULL
           2634 LOAD_FAST                4 (asyncio)
           2636 LOAD_ATTR                4 (datetime)
           2656 CACHE
           2658 LOAD_FAST               13 (filename)
           2660 LOAD_CONST              22 (0.05)
           2662 KW_NAMES                10 (('interval',))
           2664 UNPACK_SEQUENCE          3
           2668 CALL                     3
           2676 CACHE
           2678 GET_AWAITABLE            0
           2680 LOAD_CONST               2 (None)
        >> 2682 SEND                     3 (to 2692)
           2686 RESUME                   3
           2688 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2682)
           2690 POP_TOP

807     >> 2692 PUSH_NULL
           2694 LOAD_FAST                4 (asyncio)
           2696 LOAD_ATTR                4 (datetime)
           2716 CACHE
           2718 LOAD_CONST              11 ('enter')
           2720 UNPACK_SEQUENCE          2
           2724 CALL                     2
           2732 CACHE
           2734 GET_AWAITABLE            0
           2736 LOAD_CONST               2 (None)
        >> 2738 SEND                     3 (to 2748)
           2742 RESUME                   3
           2744 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2738)
           2746 POP_TOP

808     >> 2748 PUSH_NULL
           2750 LOAD_FAST                4 (asyncio)
           2752 LOAD_ATTR                7 (NULL|self + print)
           2772 CACHE
           2774 CACHE
           2776 CACHE
           2778 GET_AWAITABLE            0
           2780 LOAD_CONST               2 (None)
        >> 2782 SEND                     3 (to 2792)
           2786 RESUME                   3
           2788 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2782)
           2790 POP_TOP

810     >> 2792 LOAD_GLOBAL              7 (NULL + print)
           2802 CACHE
           2804 LOAD_CONST              39 ('✅ Document created successfully!')
           2806 UNPACK_SEQUENCE          1
           2810 CALL                     1
           2818 CACHE
           2820 POP_TOP

812        2822 LOAD_CONST              40 ("✅ '")
           2824 LOAD_FAST                0 (title)
           2826 FORMAT_VALUE             0
           2828 LOAD_CONST              41 ("' ")
           2830 LOAD_FAST                2 (document_type)
           2832 FORMAT_VALUE             0
           2834 LOAD_CONST              42 (' successfully created in Notepad\n📄 File saved as: ')

813        2836 LOAD_FAST               13 (filename)

812        2838 FORMAT_VALUE             0
           2840 LOAD_CONST              43 ('\n🎯 Document type: ')

814        2842 LOAD_FAST                2 (document_type)
           2844 STORE_SUBSCR
           2848 CACHE
           2850 CACHE
           2852 CACHE
           2854 CACHE
           2856 CACHE
           2858 CACHE
           2860 CACHE
           2862 CACHE
           2864 CACHE
           2866 UNPACK_SEQUENCE          0
           2870 CALL                     0
           2878 CACHE

812        2880 FORMAT_VALUE             0
           2882 LOAD_CONST              44 ('\n📝 Content written with proper formatting\n🔄 New file created for clean writing experience')
           2884 BUILD_STRING             9

811        2886 NOP

825        2888 NOP

827        2890 PUSH_NULL
           2892 LOAD_FAST                4 (asyncio)
           2894 LOAD_ATTR                4 (datetime)
           2914 CACHE
           2916 LOAD_FAST                6 (original_pos)
           2918 LOAD_ATTR               19 (NULL|self + hotkey)
           2938 CACHE
           2940 LOAD_CONST               9 (0.1)
           2942 KW_NAMES                45 (('duration',))
           2944 UNPACK_SEQUENCE          4
           2948 CALL                     4
           2956 CACHE
           2958 GET_AWAITABLE            0
           2960 LOAD_CONST               2 (None)
        >> 2962 SEND                     3 (to 2972)
           2966 RESUME                   3
           2968 JUMP_BACKWARD_NO_INTERRUPT     4 (to 2962)
           2970 POP_TOP
        >> 2972 RETURN_VALUE
        >> 2974 PUSH_EXC_INFO

828        2976 POP_TOP

829        2978 POP_EXCEPT
           2980 RETURN_VALUE
        >> 2982 COPY                     3
           2984 POP_EXCEPT
           2986 RERAISE                  1
        >> 2988 PUSH_EXC_INFO

819        2990 LOAD_GLOBAL             42 (Exception)
           3000 CACHE
           3002 CHECK_EXC_MATCH
           3004 POP_JUMP_IF_FALSE       94 (to 3194)
           3006 STORE_FAST              14 (e)

820        3008 LOAD_CONST              46 ('❌ Error writing to Notepad: ')
           3010 LOAD_GLOBAL             45 (NULL + str)
           3020 CACHE
           3022 LOAD_FAST               14 (e)
           3024 UNPACK_SEQUENCE          1
           3028 CALL                     1
           3036 CACHE
           3038 FORMAT_VALUE             0
           3040 BUILD_STRING             2
           3042 STORE_FAST              15 (error_msg)

821        3044 LOAD_GLOBAL              7 (NULL + print)
           3054 CACHE
           3056 LOAD_FAST               15 (error_msg)
           3058 UNPACK_SEQUENCE          1
           3062 CALL                     1
           3070 CACHE
           3072 POP_TOP

822        3074 LOAD_FAST               15 (error_msg)
           3076 SWAP                     2
           3078 POP_EXCEPT
           3080 LOAD_CONST               2 (None)
           3082 STORE_FAST              14 (e)
           3084 DELETE_FAST             14 (e)

825        3086 NOP

827        3088 PUSH_NULL
           3090 LOAD_FAST                4 (asyncio)
           3092 LOAD_ATTR                4 (datetime)
           3112 CACHE
           3114 LOAD_FAST                6 (original_pos)
           3116 LOAD_ATTR               19 (NULL|self + hotkey)
           3136 CACHE
           3138 LOAD_CONST               9 (0.1)
           3140 KW_NAMES                45 (('duration',))
           3142 UNPACK_SEQUENCE          4
           3146 CALL                     4
           3154 CACHE
           3156 GET_AWAITABLE            0
           3158 LOAD_CONST               2 (None)
        >> 3160 SEND                     3 (to 3170)
           3164 RESUME                   3
           3166 JUMP_BACKWARD_NO_INTERRUPT     4 (to 3160)
           3168 POP_TOP
        >> 3170 RETURN_VALUE
        >> 3172 PUSH_EXC_INFO

828        3174 POP_TOP

829        3176 POP_EXCEPT
           3178 RETURN_VALUE
        >> 3180 COPY                     3
           3182 POP_EXCEPT
           3184 RERAISE                  1
        >> 3186 LOAD_CONST               2 (None)
           3188 STORE_FAST              14 (e)
           3190 DELETE_FAST             14 (e)
           3192 RERAISE                  1

819     >> 3194 RERAISE                  0
        >> 3196 COPY                     3
           3198 POP_EXCEPT
           3200 RERAISE                  1
        >> 3202 PUSH_EXC_INFO

825        3204 NOP

827        3206 PUSH_NULL
           3208 LOAD_FAST                4 (asyncio)
           3210 LOAD_ATTR                4 (datetime)
           3230 CACHE
           3232 LOAD_FAST                6 (original_pos)
           3234 LOAD_ATTR               19 (NULL|self + hotkey)
           3254 CACHE
           3256 LOAD_CONST               9 (0.1)
           3258 KW_NAMES                45 (('duration',))
           3260 UNPACK_SEQUENCE          4
           3264 CALL                     4
           3272 CACHE
           3274 GET_AWAITABLE            0
           3276 LOAD_CONST               2 (None)
        >> 3278 SEND                     3 (to 3288)
           3282 RESUME                   3
           3284 JUMP_BACKWARD_NO_INTERRUPT     4 (to 3278)
           3286 POP_TOP
        >> 3288 RERAISE                  0
        >> 3290 PUSH_EXC_INFO

828        3292 POP_TOP

829        3294 POP_EXCEPT
           3296 RERAISE                  0
        >> 3298 COPY                     3
           3300 POP_EXCEPT
           3302 RERAISE                  1
        >> 3304 COPY                     3
           3306 POP_EXCEPT
           3308 RERAISE                  1
ExceptionTable:
  32 to 2884 -> 2988 [0]
  2890 to 2970 -> 2974 [1]
  2974 to 2976 -> 2982 [2] lasti
  2988 to 3006 -> 3196 [1] lasti
  3008 to 3074 -> 3186 [1] lasti
  3076 to 3076 -> 3196 [1] lasti
  3078 to 3084 -> 3202 [0]
  3088 to 3168 -> 3172 [1]
  3172 to 3174 -> 3180 [2] lasti
  3186 to 3194 -> 3196 [1] lasti
  3196 to 3200 -> 3202 [0]
  3202 to 3202 -> 3304 [1] lasti
  3206 to 3286 -> 3290 [2]
  3288 to 3288 -> 3304 [1] lasti
  3290 to 3292 -> 3298 [3] lasti
  3294 to 3302 -> 3304 [1] lasti

Disassembly of <code object open_app at 0x000001EFF4BA7480, file "tools.py", line 839>:
839           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

853           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              0 (pyautogui)
             12 STORE_FAST               1 (pyautogui)

854          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              1 (asyncio)
             20 STORE_FAST               2 (asyncio)

856          22 NOP

857          24 LOAD_GLOBAL              5 (NULL + print)
             34 CACHE
             36 LOAD_CONST               3 ('🚀 ऐप खोलने का प्रयास: ')
             38 LOAD_FAST                0 (app_name)
             40 FORMAT_VALUE             0
             42 BUILD_STRING             2
             44 UNPACK_SEQUENCE          1
             48 CALL                     1
             56 CACHE
             58 POP_TOP

858          60 PUSH_NULL
             62 LOAD_FAST                2 (asyncio)
             64 LOAD_ATTR                3 (NULL|self + asyncio)
             84 CACHE
             86 UNPACK_SEQUENCE          1
             90 CALL                     1
             98 CACHE
            100 GET_AWAITABLE            0
            102 LOAD_CONST               2 (None)
        >>  104 SEND                     3 (to 114)
            108 RESUME                   3
            110 JUMP_BACKWARD_NO_INTERRUPT     4 (to 104)
            112 STORE_FAST               3 (original_pos)

861     >>  114 PUSH_NULL
            116 LOAD_FAST                2 (asyncio)
            118 LOAD_ATTR                3 (NULL|self + asyncio)
            138 CACHE
            140 LOAD_CONST               4 ('win')
            142 UNPACK_SEQUENCE          2
            146 CALL                     2
            154 CACHE
            156 GET_AWAITABLE            0
            158 LOAD_CONST               2 (None)
        >>  160 SEND                     3 (to 170)
            164 RESUME                   3
            166 JUMP_BACKWARD_NO_INTERRUPT     4 (to 160)
            168 POP_TOP

862     >>  170 PUSH_NULL
            172 LOAD_FAST                2 (asyncio)
            174 LOAD_ATTR                6 (to_thread)
            194 CACHE
            196 CACHE
            198 CACHE
            200 GET_AWAITABLE            0
            202 LOAD_CONST               2 (None)
        >>  204 SEND                     3 (to 214)
            208 RESUME                   3
            210 JUMP_BACKWARD_NO_INTERRUPT     4 (to 204)
            212 POP_TOP

865     >>  214 PUSH_NULL
            216 LOAD_FAST                2 (asyncio)
            218 LOAD_ATTR                3 (NULL|self + asyncio)
            238 CACHE
            240 LOAD_FAST                0 (app_name)
            242 LOAD_CONST               6 (0.1)
            244 KW_NAMES                 7 (('interval',))
            246 UNPACK_SEQUENCE          3
            250 CALL                     3
            258 CACHE
            260 GET_AWAITABLE            0
            262 LOAD_CONST               2 (None)
        >>  264 SEND                     3 (to 274)
            268 RESUME                   3
            270 JUMP_BACKWARD_NO_INTERRUPT     4 (to 264)
            272 POP_TOP

866     >>  274 PUSH_NULL
            276 LOAD_FAST                2 (asyncio)
            278 LOAD_ATTR                6 (to_thread)
            298 CACHE
            300 CACHE
            302 CACHE
            304 GET_AWAITABLE            0
            306 LOAD_CONST               2 (None)
        >>  308 SEND                     3 (to 318)
            312 RESUME                   3
            314 JUMP_BACKWARD_NO_INTERRUPT     4 (to 308)
            316 POP_TOP

869     >>  318 PUSH_NULL
            320 LOAD_FAST                2 (asyncio)
            322 LOAD_ATTR                3 (NULL|self + asyncio)
            342 CACHE
            344 LOAD_CONST               8 ('enter')
            346 UNPACK_SEQUENCE          2
            350 CALL                     2
            358 CACHE
            360 GET_AWAITABLE            0
            362 LOAD_CONST               2 (None)
        >>  364 SEND                     3 (to 374)
            368 RESUME                   3
            370 JUMP_BACKWARD_NO_INTERRUPT     4 (to 364)
            372 POP_TOP

871     >>  374 LOAD_CONST               9 ("✅ '")
            376 LOAD_FAST                0 (app_name)
            378 FORMAT_VALUE             0
            380 LOAD_CONST              10 ("' खोल दिया गया है।")
            382 BUILD_STRING             3

877         384 NOP

878         386 PUSH_NULL
            388 LOAD_FAST                2 (asyncio)
            390 LOAD_ATTR                3 (NULL|self + asyncio)
            410 CACHE
            412 LOAD_FAST                3 (original_pos)
            414 LOAD_ATTR                9 (NULL|self + position)
            434 CACHE
            436 LOAD_CONST               6 (0.1)
            438 KW_NAMES                11 (('duration',))
            440 UNPACK_SEQUENCE          4
            444 CALL                     4
            452 CACHE
            454 GET_AWAITABLE            0
            456 LOAD_CONST               2 (None)
        >>  458 SEND                     3 (to 468)
            462 RESUME                   3
            464 JUMP_BACKWARD_NO_INTERRUPT     4 (to 458)
            466 POP_TOP
        >>  468 RETURN_VALUE
        >>  470 PUSH_EXC_INFO

879         472 POP_TOP

880         474 POP_EXCEPT
            476 RETURN_VALUE
        >>  478 COPY                     3
            480 POP_EXCEPT
            482 RERAISE                  1
        >>  484 PUSH_EXC_INFO

873         486 LOAD_GLOBAL             22 (Exception)
            496 CACHE
            498 CHECK_EXC_MATCH
            500 POP_JUMP_IF_FALSE       77 (to 656)
            502 STORE_FAST               4 (e)

874         504 LOAD_CONST              12 ('❌ ऐप खोलने में त्रुटि: ')
            506 LOAD_GLOBAL             25 (NULL + str)
            516 CACHE
            518 LOAD_FAST                4 (e)
            520 UNPACK_SEQUENCE          1
            524 CALL                     1
            532 CACHE
            534 FORMAT_VALUE             0
            536 BUILD_STRING             2
            538 SWAP                     2
            540 POP_EXCEPT
            542 LOAD_CONST               2 (None)
            544 STORE_FAST               4 (e)
            546 DELETE_FAST              4 (e)

877         548 NOP

878         550 PUSH_NULL
            552 LOAD_FAST                2 (asyncio)
            554 LOAD_ATTR                3 (NULL|self + asyncio)
            574 CACHE
            576 LOAD_FAST                3 (original_pos)
            578 LOAD_ATTR                9 (NULL|self + position)
            598 CACHE
            600 LOAD_CONST               6 (0.1)
            602 KW_NAMES                11 (('duration',))
            604 UNPACK_SEQUENCE          4
            608 CALL                     4
            616 CACHE
            618 GET_AWAITABLE            0
            620 LOAD_CONST               2 (None)
        >>  622 SEND                     3 (to 632)
            626 RESUME                   3
            628 JUMP_BACKWARD_NO_INTERRUPT     4 (to 622)
            630 POP_TOP
        >>  632 RETURN_VALUE
        >>  634 PUSH_EXC_INFO

879         636 POP_TOP

880         638 POP_EXCEPT
            640 RETURN_VALUE
        >>  642 COPY                     3
            644 POP_EXCEPT
            646 RERAISE                  1
        >>  648 LOAD_CONST               2 (None)
            650 STORE_FAST               4 (e)
            652 DELETE_FAST              4 (e)
            654 RERAISE                  1

873     >>  656 RERAISE                  0
        >>  658 COPY                     3
            660 POP_EXCEPT
            662 RERAISE                  1
        >>  664 PUSH_EXC_INFO

877         666 NOP

878         668 PUSH_NULL
            670 LOAD_FAST                2 (asyncio)
            672 LOAD_ATTR                3 (NULL|self + asyncio)
            692 CACHE
            694 LOAD_FAST                3 (original_pos)
            696 LOAD_ATTR                9 (NULL|self + position)
            716 CACHE
            718 LOAD_CONST               6 (0.1)
            720 KW_NAMES                11 (('duration',))
            722 UNPACK_SEQUENCE          4
            726 CALL                     4
            734 CACHE
            736 GET_AWAITABLE            0
            738 LOAD_CONST               2 (None)
        >>  740 SEND                     3 (to 750)
            744 RESUME                   3
            746 JUMP_BACKWARD_NO_INTERRUPT     4 (to 740)
            748 POP_TOP
        >>  750 RERAISE                  0
        >>  752 PUSH_EXC_INFO

879         754 POP_TOP

880         756 POP_EXCEPT
            758 RERAISE                  0
        >>  760 COPY                     3
            762 POP_EXCEPT
            764 RERAISE                  1
        >>  766 COPY                     3
            768 POP_EXCEPT
            770 RERAISE                  1
ExceptionTable:
  24 to 382 -> 484 [0]
  386 to 466 -> 470 [1]
  470 to 472 -> 478 [2] lasti
  484 to 502 -> 658 [1] lasti
  504 to 536 -> 648 [1] lasti
  538 to 538 -> 658 [1] lasti
  540 to 546 -> 664 [0]
  550 to 630 -> 634 [1]
  634 to 636 -> 642 [2] lasti
  648 to 656 -> 658 [1] lasti
  658 to 662 -> 664 [0]
  664 to 664 -> 766 [1] lasti
  668 to 748 -> 752 [2]
  750 to 750 -> 766 [1] lasti
  752 to 754 -> 760 [3] lasti
  756 to 764 -> 766 [1] lasti

Disassembly of <code object press_key at 0x000001EFF4C456D0, file "tools.py", line 883>:
883           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

897           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              0 (pyautogui)
             12 STORE_FAST               1 (pyautogui)

898          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              1 (asyncio)
             20 STORE_FAST               2 (asyncio)

900          22 NOP

902          24 LOAD_FAST                0 (key)
             26 STORE_SUBSCR
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 UNPACK_SEQUENCE          0
             52 CALL                     0
             60 CACHE
             62 STORE_SUBSCR
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 UNPACK_SEQUENCE          0
             88 CALL                     0
             96 CACHE
             98 STORE_FAST               0 (key)

905         100 LOAD_CONST               3 ('+')
            102 LOAD_FAST                0 (key)
            104 CONTAINS_OP              0
            106 POP_JUMP_IF_FALSE       57 (to 222)

906         108 LOAD_CONST               4 (<code object <listcomp> at 0x000001EFF695DF30, file "tools.py", line 906>)
            110 MAKE_FUNCTION            0
            112 LOAD_FAST                0 (key)
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 LOAD_CONST               3 ('+')
            138 UNPACK_SEQUENCE          1
            142 CALL                     1
            150 CACHE
            152 GET_ITER
            154 UNPACK_SEQUENCE          0
            158 CALL                     0
            166 CACHE
            168 STORE_FAST               3 (keys)

907         170 PUSH_NULL
            172 LOAD_FAST                2 (asyncio)
            174 LOAD_ATTR                5 (NULL|self + strip)
            194 CACHE
            196 BUILD_LIST               1
            198 LOAD_FAST                3 (keys)
            200 LIST_EXTEND              1
            202 LOAD_ATTR                0 (pyautogui)

909     >>  222 PUSH_NULL
            224 LOAD_FAST                2 (asyncio)
            226 LOAD_ATTR                5 (NULL|self + strip)
            246 CACHE
            248 LOAD_FAST                0 (key)
            250 UNPACK_SEQUENCE          2
            254 CALL                     2
            262 CACHE
            264 GET_AWAITABLE            0
            266 LOAD_CONST               2 (None)
        >>  268 SEND                     3 (to 278)
            272 RESUME                   3
            274 JUMP_BACKWARD_NO_INTERRUPT     4 (to 268)
            276 POP_TOP

911     >>  278 LOAD_CONST               5 ("✅ '")
            280 LOAD_FAST                0 (key)
            282 FORMAT_VALUE             0
            284 LOAD_CONST               6 ("' दबा दिया गया है।")
            286 BUILD_STRING             3
            288 RETURN_VALUE
        >>  290 PUSH_EXC_INFO

913         292 LOAD_GLOBAL             16 (Exception)
            302 CACHE
            304 CHECK_EXC_MATCH
            306 POP_JUMP_IF_FALSE       28 (to 364)
            308 STORE_FAST               4 (e)

914         310 LOAD_CONST               7 ('❌ कुंजी दबाने में त्रुटि: ')
            312 LOAD_GLOBAL             19 (NULL + str)
            322 CACHE
            324 LOAD_FAST                4 (e)
            326 UNPACK_SEQUENCE          1
            330 CALL                     1
            338 CACHE
            340 FORMAT_VALUE             0
            342 BUILD_STRING             2
            344 SWAP                     2
            346 POP_EXCEPT
            348 LOAD_CONST               2 (None)
            350 STORE_FAST               4 (e)
            352 DELETE_FAST              4 (e)
            354 RETURN_VALUE
        >>  356 LOAD_CONST               2 (None)
            358 STORE_FAST               4 (e)
            360 DELETE_FAST              4 (e)
            362 RERAISE                  1

913     >>  364 RERAISE                  0
        >>  366 COPY                     3
            368 POP_EXCEPT
            370 RERAISE                  1
ExceptionTable:
  24 to 286 -> 290 [0]
  290 to 308 -> 366 [1] lasti
  310 to 342 -> 356 [1] lasti
  344 to 344 -> 366 [1] lasti
  356 to 364 -> 366 [1] lasti

Disassembly of <code object <listcomp> at 0x000001EFF695DF30, file "tools.py", line 906>:
906           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                22 (to 54)
             10 LOAD_FAST                1 (k)
             12 STORE_SUBSCR
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 UNPACK_SEQUENCE          0
             38 CALL                     0
             46 CACHE
             48 LIST_APPEND              2
             50 JUMP_BACKWARD           23 (to 6)
             52 RETURN_VALUE

Disassembly of <code object get_system_info at 0x000001EFF4B5DE50, file "tools.py", line 918>:
918           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

933           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              0 (psutil)
             12 STORE_FAST               0 (psutil)

934          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              1 (socket)
             20 STORE_FAST               1 (socket)

935          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              2 (platform)
             28 STORE_FAST               2 (platform)

936          30 LOAD_CONST               1 (0)
             32 LOAD_CONST               2 (None)
             34 IMPORT_NAME              3 (shutil)
             36 STORE_FAST               3 (shutil)

938          38 NOP

940          40 PUSH_NULL
             42 LOAD_FAST                0 (psutil)
             44 LOAD_ATTR                4 (platform)
             64 CACHE
             66 CACHE
             68 STORE_FAST               4 (battery)

941          70 LOAD_FAST                4 (battery)
             72 POP_JUMP_IF_FALSE       19 (to 112)

942          74 LOAD_FAST                4 (battery)
             76 LOAD_ATTR                5 (NULL|self + platform)
             96 CACHE
             98 CACHE
            100 POP_JUMP_IF_FALSE        2 (to 106)
            102 LOAD_CONST               3 ('⚡ Charging')
            104 JUMP_FORWARD             1 (to 108)
        >>  106 LOAD_CONST               4 ('🔋 On Battery')
        >>  108 STORE_FAST               6 (charging)
            110 JUMP_FORWARD             4 (to 120)

945     >>  112 LOAD_CONST               5 ('N/A')
            114 STORE_FAST               5 (battery_percent)

946         116 LOAD_CONST               5 ('N/A')
            118 STORE_FAST               6 (charging)

949     >>  120 LOAD_FAST                3 (shutil)
            122 STORE_SUBSCR
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 LOAD_CONST               6 ('/')
            146 UNPACK_SEQUENCE          1
            150 CALL                     1
            158 CACHE
            160 UNPACK_SEQUENCE          3
            164 STORE_FAST               7 (total)
            166 STORE_FAST               8 (used)
            168 STORE_FAST               9 (free)

950         170 LOAD_FAST                7 (total)
            172 LOAD_CONST               7 (1073741824)
            174 BINARY_OP                2 (//)
            178 STORE_FAST              10 (total_gb)

951         180 LOAD_FAST                9 (free)
            182 LOAD_CONST               7 (1073741824)
            184 BINARY_OP                2 (//)
            188 STORE_FAST              11 (free_gb)

954         190 NOP

955         192 PUSH_NULL
            194 LOAD_FAST                1 (socket)
            196 LOAD_ATTR                8 (sensors_battery)
            216 CACHE
            218 CACHE
            220 STORE_FAST              12 (hostname)

956         222 PUSH_NULL
            224 LOAD_FAST                1 (socket)
            226 LOAD_ATTR                9 (NULL|self + sensors_battery)
            246 CACHE
            248 CACHE
            250 CACHE
            252 STORE_FAST              13 (ip_address)

957         254 LOAD_CONST               8 ('Connected (IP: ')
            256 LOAD_FAST               13 (ip_address)
            258 FORMAT_VALUE             0
            260 LOAD_CONST               9 (')')
            262 BUILD_STRING             3
            264 STORE_FAST              14 (network_status)
            266 JUMP_FORWARD             9 (to 286)
        >>  268 PUSH_EXC_INFO

958         270 POP_TOP

959         272 LOAD_CONST              10 ('❌ Not Connected')
            274 STORE_FAST              14 (network_status)
            276 POP_EXCEPT
            278 JUMP_FORWARD             3 (to 286)
        >>  280 COPY                     3
            282 POP_EXCEPT
            284 RERAISE                  1

962     >>  286 PUSH_NULL
            288 LOAD_FAST                0 (psutil)
            290 LOAD_ATTR               10 (percent)
            310 CACHE
            312 CACHE
            314 CACHE
            316 CACHE
            318 STORE_FAST              15 (cpu_percent)

963         320 PUSH_NULL
            322 LOAD_FAST                0 (psutil)
            324 LOAD_ATTR               11 (NULL|self + percent)
            344 CACHE
            346 CACHE
            348 STORE_FAST              16 (ram)

964         350 LOAD_FAST               16 (ram)
            352 LOAD_ATTR                5 (NULL|self + platform)
            372 CACHE
            374 CACHE
            376 LOAD_FAST               16 (ram)
            378 LOAD_ATTR               13 (NULL|self + power_plugged)
            398 CACHE
            400 CALL                     2
            408 CACHE
            410 STORE_FAST              18 (ram_total_gb)

966         412 LOAD_GLOBAL             25 (NULL + round)
            422 CACHE
            424 LOAD_FAST               16 (ram)
            426 LOAD_ATTR               14 (disk_usage)
            446 CACHE
            448 CALL                     2
            456 CACHE
            458 STORE_FAST              19 (ram_used_gb)

969         460 PUSH_NULL
            462 LOAD_FAST                2 (platform)
            464 LOAD_ATTR               15 (NULL|self + disk_usage)
            484 CACHE
            486 CACHE
            488 STORE_FAST              20 (system_name)

972         490 LOAD_CONST              13 ('🧠 System Info for: ')
            492 LOAD_FAST               20 (system_name)
            494 FORMAT_VALUE             0
            496 LOAD_CONST              14 ('\n🔋 Battery: ')

973         498 LOAD_FAST                5 (battery_percent)

972         500 FORMAT_VALUE             0
            502 LOAD_CONST              15 ('% (')

973         504 LOAD_FAST                6 (charging)

972         506 FORMAT_VALUE             0
            508 LOAD_CONST              16 (')\n💾 Storage: ')

974         510 LOAD_FAST               11 (free_gb)

972         512 FORMAT_VALUE             0
            514 LOAD_CONST              17 (' GB free of ')

974         516 LOAD_FAST               10 (total_gb)

972         518 FORMAT_VALUE             0
            520 LOAD_CONST              18 (' GB\n📶 Network: ')

975         522 LOAD_FAST               14 (network_status)

972         524 FORMAT_VALUE             0
            526 LOAD_CONST              19 ('\n🧠 CPU Usage: ')

976         528 LOAD_FAST               15 (cpu_percent)

972         530 FORMAT_VALUE             0
            532 LOAD_CONST              20 ('%\n📈 RAM Usage: ')

977         534 LOAD_FAST               17 (ram_percent)

972         536 FORMAT_VALUE             0
            538 LOAD_CONST              15 ('% (')

977         540 LOAD_FAST               19 (ram_used_gb)

972         542 FORMAT_VALUE             0
            544 LOAD_CONST              21 (' GB of ')

977         546 LOAD_FAST               18 (ram_total_gb)

972         548 FORMAT_VALUE             0
            550 LOAD_CONST              22 (' GB)')
            552 BUILD_STRING            21

971         554 RETURN_VALUE
        >>  556 PUSH_EXC_INFO

980         558 LOAD_GLOBAL             32 (Exception)
            568 CACHE
            570 CHECK_EXC_MATCH
            572 POP_JUMP_IF_FALSE       28 (to 630)
            574 STORE_FAST              21 (e)

981         576 LOAD_CONST              23 ('❌ सिस्टम जानकारी प्राप्त करने में त्रुटि: ')
            578 LOAD_GLOBAL             35 (NULL + str)
            588 CACHE
            590 LOAD_FAST               21 (e)
            592 UNPACK_SEQUENCE          1
            596 CALL                     1
            604 CACHE
            606 FORMAT_VALUE             0
            608 BUILD_STRING             2
            610 SWAP                     2
            612 POP_EXCEPT
            614 LOAD_CONST               2 (None)
            616 STORE_FAST              21 (e)
            618 DELETE_FAST             21 (e)
            620 RETURN_VALUE
        >>  622 LOAD_CONST               2 (None)
            624 STORE_FAST              21 (e)
            626 DELETE_FAST             21 (e)
            628 RERAISE                  1

980     >>  630 RERAISE                  0
        >>  632 COPY                     3
            634 POP_EXCEPT
            636 RERAISE                  1
ExceptionTable:
  40 to 188 -> 556 [0]
  192 to 264 -> 268 [0]
  266 to 266 -> 556 [0]
  268 to 274 -> 280 [1] lasti
  276 to 552 -> 556 [0]
  556 to 574 -> 632 [1] lasti
  576 to 608 -> 622 [1] lasti
  610 to 610 -> 632 [1] lasti
  622 to 630 -> 632 [1] lasti

Disassembly of <code object type_user_message_auto at 0x000001EFF69A8C00, file "tools.py", line 985>:
 985           0 RETURN_GENERATOR
               2 POP_TOP
               4 RESUME                   0

1001           6 LOAD_CONST               1 (0)
               8 LOAD_CONST               2 (None)
              10 IMPORT_NAME              0 (pyautogui)
              12 STORE_FAST               1 (pyautogui)

1002          14 LOAD_CONST               1 (0)
              16 LOAD_CONST               2 (None)
              18 IMPORT_NAME              1 (asyncio)
              20 STORE_FAST               2 (asyncio)

1004          22 LOAD_FAST                0 (message)
              24 STORE_SUBSCR
              28 CACHE
              30 CACHE
              32 CACHE
              34 CACHE
              36 CACHE
              38 CACHE
              40 CACHE
              42 CACHE
              44 CACHE
              46 UNPACK_SEQUENCE          0
              50 CALL                     0
              58 CACHE
              60 POP_JUMP_IF_TRUE         2 (to 66)

1005          62 LOAD_CONST               3 ('⚠️ Sir, message खाली है।')
              64 RETURN_VALUE

1007     >>   66 PUSH_NULL
              68 LOAD_FAST                2 (asyncio)
              70 LOAD_ATTR                3 (NULL|self + asyncio)
              90 CACHE
              92 LOAD_FAST                0 (message)
              94 LOAD_CONST               4 (0.1)
              96 KW_NAMES                 5 (('interval',))
              98 UNPACK_SEQUENCE          3
             102 CALL                     3
             110 CACHE
             112 GET_AWAITABLE            0
             114 LOAD_CONST               2 (None)
         >>  116 SEND                     3 (to 126)
             120 RESUME                   3
             122 JUMP_BACKWARD_NO_INTERRUPT     4 (to 116)
             124 POP_TOP

1008     >>  126 LOAD_CONST               6 ('✅ टाइप कर दिया गया: "')
             128 LOAD_FAST                0 (message)
             130 FORMAT_VALUE             0
             132 LOAD_CONST               7 ('"')
             134 BUILD_STRING             3
             136 RETURN_VALUE

Disassembly of <code object click_on_text at 0x000001EFF4A476D0, file "tools.py", line 1033>:
               0 MAKE_CELL               20 (SequenceMatcher)

1033           2 RETURN_GENERATOR
               4 POP_TOP
               6 RESUME                   0

1048           8 LOAD_CONST               1 (0)
              10 LOAD_CONST               2 (None)
              12 IMPORT_NAME              0 (pyautogui)
              14 STORE_FAST               1 (pyautogui)

1049          16 LOAD_CONST               1 (0)
              18 LOAD_CONST               2 (None)
              20 IMPORT_NAME              1 (pytesseract)
              22 STORE_FAST               2 (pytesseract)

1050          24 LOAD_CONST               1 (0)
              26 LOAD_CONST               2 (None)
              28 IMPORT_NAME              2 (cv2)
              30 STORE_FAST               3 (cv2)

1051          32 LOAD_CONST               1 (0)
              34 LOAD_CONST               2 (None)
              36 IMPORT_NAME              3 (numpy)
              38 STORE_FAST               4 (np)

1052          40 LOAD_CONST               1 (0)
              42 LOAD_CONST               2 (None)
              44 IMPORT_NAME              4 (asyncio)
              46 STORE_FAST               5 (asyncio)

1053          48 LOAD_CONST               1 (0)
              50 LOAD_CONST               3 (('SequenceMatcher',))
              52 IMPORT_NAME              5 (difflib)
              54 IMPORT_FROM              6 (SequenceMatcher)
              56 STORE_DEREF             20 (SequenceMatcher)
              58 POP_TOP

1055          60 LOAD_CONST               4 ('text1')
              62 LOAD_GLOBAL             14 (str)
              72 CACHE
              74 LOAD_CONST               5 ('text2')
              76 LOAD_GLOBAL             14 (str)
              86 CACHE
              88 LOAD_CONST               6 ('return')
              90 LOAD_GLOBAL             16 (float)
             100 CACHE
             102 BUILD_TUPLE              6
             104 LOAD_CLOSURE            20 (SequenceMatcher)
             106 BUILD_TUPLE              1
             108 LOAD_CONST               7 (<code object similarity at 0x000001EFF69FC1D0, file "tools.py", line 1055>)
             110 MAKE_FUNCTION           12 (annotations, closure)
             112 STORE_FAST               6 (similarity)

1058         114 NOP

1060         116 PUSH_NULL
             118 LOAD_FAST                5 (asyncio)
             120 LOAD_ATTR                9 (NULL|self + asyncio)
             140 CACHE
             142 UNPACK_SEQUENCE          1
             146 CALL                     1
             154 CACHE
             156 GET_AWAITABLE            0
             158 LOAD_CONST               2 (None)
         >>  160 SEND                     3 (to 170)
             164 RESUME                   3
             166 JUMP_BACKWARD_NO_INTERRUPT     4 (to 160)
             168 STORE_FAST               7 (screenshot)

1061     >>  170 PUSH_NULL
             172 LOAD_FAST                4 (np)
             174 LOAD_ATTR               11 (NULL|self + difflib)
             194 CACHE
             196 CACHE
             198 CACHE
             200 STORE_FAST               8 (screenshot_np)

1062         202 PUSH_NULL
             204 LOAD_FAST                3 (cv2)
             206 LOAD_ATTR               12 (SequenceMatcher)
             226 CACHE
             228 CACHE
             230 UNPACK_SEQUENCE          2
             234 CALL                     2
             242 CACHE
             244 STORE_FAST               9 (image)

1065         246 PUSH_NULL
             248 LOAD_FAST                3 (cv2)
             250 LOAD_ATTR               12 (SequenceMatcher)
             270 CACHE
             272 CACHE
             274 UNPACK_SEQUENCE          2
             278 CALL                     2
             286 CACHE
             288 STORE_FAST              10 (gray)

1066         290 PUSH_NULL
             292 LOAD_FAST                3 (cv2)
             294 LOAD_ATTR               15 (NULL|self + str)
             314 LOAD_ATTR               16 (float)
             334 CACHE
             336 CACHE
             338 CACHE
             340 STORE_FAST              10 (gray)

1069         342 PUSH_NULL
             344 LOAD_FAST                5 (asyncio)
             346 LOAD_ATTR                9 (NULL|self + asyncio)
             366 CACHE

1071         368 LOAD_FAST               10 (gray)

1072         370 LOAD_FAST                2 (pytesseract)
             372 LOAD_ATTR               18 (to_thread)

1069         392 KW_NAMES                10 (('output_type',))
             394 UNPACK_SEQUENCE          3
             398 CALL                     3
             406 CACHE
             408 GET_AWAITABLE            0
             410 LOAD_CONST               2 (None)
         >>  412 SEND                     3 (to 422)
             416 RESUME                   3
             418 JUMP_BACKWARD_NO_INTERRUPT     4 (to 412)
             420 STORE_FAST              11 (data)

1076     >>  422 LOAD_CONST               2 (None)
             424 STORE_FAST              12 (best_match)

1077         426 LOAD_CONST               1 (0)
             428 STORE_FAST              13 (best_score)

1079         430 LOAD_GLOBAL             41 (NULL + range)
             440 CACHE
             442 LOAD_GLOBAL             43 (NULL + len)
             452 CACHE
             454 LOAD_FAST               11 (data)
             456 LOAD_CONST              11 ('text')
             458 BINARY_SUBSCR
             462 CACHE
             464 CACHE
             466 CACHE
             468 UNPACK_SEQUENCE          1
             472 CALL                     1
             480 CACHE
             482 UNPACK_SEQUENCE          1
             486 CALL                     1
             494 CACHE
             496 GET_ITER
         >>  498 FOR_ITER               183 (to 868)

1080         502 LOAD_FAST               11 (data)
             504 LOAD_CONST              11 ('text')
             506 BINARY_SUBSCR
             510 CACHE
             512 CACHE
             514 CACHE
             516 LOAD_FAST               14 (i)
             518 BINARY_SUBSCR
             522 CACHE
             524 CACHE
             526 CACHE
             528 STORE_SUBSCR
             532 CACHE
             534 CACHE
             536 CACHE
             538 CACHE
             540 CACHE
             542 CACHE
             544 CACHE
             546 CACHE
             548 CACHE
             550 UNPACK_SEQUENCE          0
             554 CALL                     0
             562 CACHE
             564 STORE_FAST              15 (text)

1081         566 LOAD_FAST               15 (text)
             568 POP_JUMP_IF_TRUE         1 (to 572)

1082         570 JUMP_BACKWARD           37 (to 498)

1084     >>  572 PUSH_NULL
             574 LOAD_FAST                6 (similarity)
             576 LOAD_FAST                0 (target_text)
             578 LOAD_FAST               15 (text)
             580 UNPACK_SEQUENCE          2
             584 CALL                     2
             592 CACHE
             594 STORE_FAST              16 (score)

1085         596 LOAD_FAST               16 (score)
             598 LOAD_FAST               13 (best_score)
             600 COMPARE_OP               4 (<)
             604 CACHE
             606 POP_JUMP_IF_FALSE      128 (to 864)
             608 LOAD_FAST               16 (score)
             610 LOAD_CONST              12 (0.7)
             612 COMPARE_OP               4 (<)
             616 CACHE
             618 POP_JUMP_IF_FALSE      122 (to 864)

1086         620 LOAD_FAST               16 (score)
             622 STORE_FAST              13 (best_score)

1088         624 LOAD_FAST               15 (text)

1089         626 LOAD_GLOBAL             47 (NULL + int)
             636 CACHE
             638 LOAD_FAST               11 (data)
             640 LOAD_CONST              13 ('left')
             642 BINARY_SUBSCR
             646 CACHE
             648 CACHE
             650 CACHE
             652 LOAD_FAST               14 (i)
             654 BINARY_SUBSCR
             658 CACHE
             660 CACHE
             662 CACHE
             664 LOAD_CONST               8 (2)
             666 BINARY_OP               11 (/)
             670 UNPACK_SEQUENCE          1
             674 CALL                     1
             682 CACHE

1090         684 LOAD_GLOBAL             47 (NULL + int)
             694 CACHE
             696 LOAD_FAST               11 (data)
             698 LOAD_CONST              14 ('top')
             700 BINARY_SUBSCR
             704 CACHE
             706 CACHE
             708 CACHE
             710 LOAD_FAST               14 (i)
             712 BINARY_SUBSCR
             716 CACHE
             718 CACHE
             720 CACHE
             722 LOAD_CONST               8 (2)
             724 BINARY_OP               11 (/)
             728 UNPACK_SEQUENCE          1
             732 CALL                     1
             740 CACHE

1091         742 LOAD_GLOBAL             47 (NULL + int)
             752 CACHE
             754 LOAD_FAST               11 (data)
             756 LOAD_CONST              15 ('width')
             758 BINARY_SUBSCR
             762 CACHE
             764 CACHE
             766 CACHE
             768 LOAD_FAST               14 (i)
             770 BINARY_SUBSCR
             774 CACHE
             776 CACHE
             778 CACHE
             780 LOAD_CONST               8 (2)
             782 BINARY_OP               11 (/)
             786 UNPACK_SEQUENCE          1
             790 CALL                     1
             798 CACHE

1092         800 LOAD_GLOBAL             47 (NULL + int)
             810 CACHE
             812 LOAD_FAST               11 (data)
             814 LOAD_CONST              16 ('height')
             816 BINARY_SUBSCR
             820 CACHE
             822 CACHE
             824 CACHE
             826 LOAD_FAST               14 (i)
             828 BINARY_SUBSCR
             832 CACHE
             834 CACHE
             836 CACHE
             838 LOAD_CONST               8 (2)
             840 BINARY_OP               11 (/)
             844 UNPACK_SEQUENCE          1
             848 CALL                     1
             856 CACHE

1087         858 LOAD_CONST              17 (('text', 'x', 'y', 'w', 'h'))
             860 BUILD_CONST_KEY_MAP      5
             862 STORE_FAST              12 (best_match)
         >>  864 JUMP_BACKWARD          184 (to 498)

1095         866 LOAD_FAST               12 (best_match)
         >>  868 POP_JUMP_IF_TRUE         6 (to 882)

1096         870 LOAD_CONST              18 ("❌ '")
             872 LOAD_FAST                0 (target_text)
             874 FORMAT_VALUE             0
             876 LOAD_CONST              19 ("' नहीं मिला")
             878 BUILD_STRING             3
             880 RETURN_VALUE

1099     >>  882 LOAD_FAST               12 (best_match)
             884 LOAD_CONST              20 ('x')
             886 BINARY_SUBSCR
             890 CACHE
             892 CACHE
             894 CACHE
             896 LOAD_FAST               12 (best_match)
             898 LOAD_CONST              21 ('w')
             900 BINARY_SUBSCR
             904 CACHE
             906 CACHE
             908 CACHE
             910 LOAD_CONST               8 (2)
             912 BINARY_OP                2 (//)
             916 BINARY_OP                0 (+)
             920 STORE_FAST              17 (center_x)

1100         922 LOAD_FAST               12 (best_match)
             924 LOAD_CONST              22 ('y')
             926 BINARY_SUBSCR
             930 CACHE
             932 CACHE
             934 CACHE
             936 LOAD_FAST               12 (best_match)
             938 LOAD_CONST              23 ('h')
             940 BINARY_SUBSCR
             944 CACHE
             946 CACHE
             948 CACHE
             950 LOAD_CONST               8 (2)
             952 BINARY_OP                2 (//)
             956 BINARY_OP                0 (+)
             960 STORE_FAST              18 (center_y)

1102         962 PUSH_NULL
             964 LOAD_FAST                5 (asyncio)
             966 LOAD_ATTR                9 (NULL|self + asyncio)
             986 CACHE
             988 LOAD_FAST               17 (center_x)
             990 LOAD_FAST               18 (center_y)
             992 LOAD_CONST              24 (0.2)
             994 KW_NAMES                25 (('duration',))
             996 UNPACK_SEQUENCE          4
            1000 CALL                     4
            1008 CACHE
            1010 GET_AWAITABLE            0
            1012 LOAD_CONST               2 (None)
         >> 1014 SEND                     3 (to 1024)
            1018 RESUME                   3
            1020 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1014)
            1022 POP_TOP

1103     >> 1024 PUSH_NULL
            1026 LOAD_FAST                5 (asyncio)
            1028 LOAD_ATTR                9 (NULL|self + asyncio)
            1048 CACHE
            1050 UNPACK_SEQUENCE          1
            1054 CALL                     1
            1062 CACHE
            1064 GET_AWAITABLE            0
            1066 LOAD_CONST               2 (None)
         >> 1068 SEND                     3 (to 1078)
            1072 RESUME                   3
            1074 JUMP_BACKWARD_NO_INTERRUPT     4 (to 1068)
            1076 POP_TOP

1105     >> 1078 LOAD_CONST              26 ("✅ '")
            1080 LOAD_FAST                0 (target_text)
            1082 FORMAT_VALUE             0
            1084 LOAD_CONST              27 ("' पर क्लिक किया गया!")
            1086 BUILD_STRING             3
            1088 RETURN_VALUE
         >> 1090 PUSH_EXC_INFO

1107        1092 LOAD_GLOBAL             52 (Exception)
            1102 CACHE
            1104 CHECK_EXC_MATCH
            1106 POP_JUMP_IF_FALSE       28 (to 1164)
            1108 STORE_FAST              19 (e)

1108        1110 LOAD_CONST              28 ('🚫 Error: ')
            1112 LOAD_GLOBAL             15 (NULL + str)
            1122 CACHE
            1124 LOAD_FAST               19 (e)
            1126 UNPACK_SEQUENCE          1
            1130 CALL                     1
            1138 CACHE
            1140 FORMAT_VALUE             0
            1142 BUILD_STRING             2
            1144 SWAP                     2
            1146 POP_EXCEPT
            1148 LOAD_CONST               2 (None)
            1150 STORE_FAST              19 (e)
            1152 DELETE_FAST             19 (e)
            1154 RETURN_VALUE
         >> 1156 LOAD_CONST               2 (None)
            1158 STORE_FAST              19 (e)
            1160 DELETE_FAST             19 (e)
            1162 RERAISE                  1

1107     >> 1164 RERAISE                  0
         >> 1166 COPY                     3
            1168 POP_EXCEPT
            1170 RERAISE                  1
ExceptionTable:
  116 to 878 -> 1090 [0]
  882 to 1086 -> 1090 [0]
  1090 to 1108 -> 1166 [1] lasti
  1110 to 1142 -> 1156 [1] lasti
  1144 to 1144 -> 1166 [1] lasti
  1156 to 1164 -> 1166 [1] lasti

Disassembly of <code object similarity at 0x000001EFF69FC1D0, file "tools.py", line 1055>:
               0 COPY_FREE_VARS           1

1055           2 RESUME                   0

1056           4 PUSH_NULL
               6 LOAD_DEREF               2 (SequenceMatcher)
               8 LOAD_CONST               0 (None)
              10 LOAD_FAST                0 (text1)
              12 STORE_SUBSCR
              16 CACHE
              18 CACHE
              20 CACHE
              22 CACHE
              24 CACHE
              26 CACHE
              28 CACHE
              30 CACHE
              32 CACHE
              34 UNPACK_SEQUENCE          0
              38 CALL                     0
              46 CACHE
              48 STORE_SUBSCR
              52 CACHE
              54 CACHE
              56 CACHE
              58 CACHE
              60 CACHE
              62 CACHE
              64 CACHE
              66 CACHE
              68 CACHE
              70 UNPACK_SEQUENCE          0
              74 CALL                     0
              82 CACHE
              84 LOAD_FAST                1 (text2)
              86 STORE_SUBSCR
              90 CACHE
              92 CACHE
              94 CACHE
              96 CACHE
              98 CACHE
             100 CACHE
             102 CACHE
             104 CACHE
             106 CACHE
             108 UNPACK_SEQUENCE          0
             112 CALL                     0
             120 CACHE
             122 STORE_SUBSCR
             126 CACHE
             128 CACHE
             130 CACHE
             132 CACHE
             134 CACHE
             136 CACHE
             138 CACHE
             140 CACHE
             142 CACHE
             144 UNPACK_SEQUENCE          0
             148 CALL                     0
             156 CACHE
             158 UNPACK_SEQUENCE          3
             162 CALL                     3
             170 CACHE
             172 STORE_SUBSCR
             176 CACHE
             178 CACHE
             180 CACHE
             182 CACHE
             184 CACHE
             186 CACHE
             188 CACHE
             190 CACHE
             192 CACHE
             194 UNPACK_SEQUENCE          0
             198 CALL                     0
             206 CACHE
             208 RETURN_VALUE

Disassembly of <code object scan_system_for_viruses at 0x000001EFF4B63DF0, file "tools.py", line 1111>:
1111           0 RETURN_GENERATOR
               2 POP_TOP
               4 RESUME                   0

1125           6 LOAD_CONST               1 (0)
               8 LOAD_CONST               2 (None)
              10 IMPORT_NAME              0 (asyncio)
              12 STORE_FAST               0 (asyncio)

1126          14 LOAD_CONST               1 (0)
              16 LOAD_CONST               2 (None)
              18 IMPORT_NAME              1 (subprocess)
              20 STORE_FAST               1 (subprocess)

1128          22 NOP

1129          24 BUILD_LIST               0
              26 LOAD_CONST               3 (('C:\\Program Files\\Windows Defender\\MpCmdRun.exe', '-Scan', '-ScanType', '1'))
              28 LIST_EXTEND              1
              30 STORE_FAST               2 (cmd)

1135          32 BUILD_LIST               0
              34 LOAD_CONST               4 (('C:\\ProgramData\\Microsoft\\Windows Defender\\Platform\\4.18.23070.2003-0\\MpCmdRun.exe', '-Scan', '-ScanType', '1'))
              36 LIST_EXTEND              1
              38 STORE_FAST               3 (alt_cmd)

1140          40 NOP

1141          42 PUSH_NULL
              44 LOAD_FAST                0 (asyncio)
              46 LOAD_ATTR                2 (subprocess)
              66 CACHE
              68 CACHE

1144          70 LOAD_FAST                1 (subprocess)
              72 LOAD_ATTR                3 (NULL|self + subprocess)
         >>   92 SEND                     3 (to 102)
              96 RESUME                   3
              98 JUMP_BACKWARD_NO_INTERRUPT     4 (to 92)
             100 STORE_FAST               4 (proc)
         >>  102 JUMP_FORWARD            46 (to 196)
         >>  104 PUSH_EXC_INFO

1146         106 LOAD_GLOBAL              8 (FileNotFoundError)
             116 CACHE
             118 CHECK_EXC_MATCH
             120 POP_JUMP_IF_FALSE       33 (to 188)
             122 POP_TOP

1147         124 PUSH_NULL
             126 LOAD_FAST                0 (asyncio)
             128 LOAD_ATTR                2 (subprocess)
             148 CACHE
             150 CACHE

1150         152 LOAD_FAST                1 (subprocess)
             154 LOAD_ATTR                3 (NULL|self + subprocess)
         >>  174 SEND                     3 (to 184)
             178 RESUME                   3
             180 JUMP_BACKWARD_NO_INTERRUPT     4 (to 174)
             182 STORE_FAST               4 (proc)
         >>  184 POP_EXCEPT
             186 JUMP_FORWARD             4 (to 196)

1146     >>  188 RERAISE                  0
         >>  190 COPY                     3
             192 POP_EXCEPT
             194 RERAISE                  1

1153     >>  196 LOAD_FAST                4 (proc)
             198 STORE_SUBSCR
             202 CACHE
             204 CACHE
             206 CACHE
             208 CACHE
             210 CACHE
             212 CACHE
             214 CACHE
             216 CACHE
             218 CACHE
             220 UNPACK_SEQUENCE          0
             224 CALL                     0
             232 CACHE
             234 GET_AWAITABLE            0
             236 LOAD_CONST               2 (None)
         >>  238 SEND                     3 (to 248)
             242 RESUME                   3
             244 JUMP_BACKWARD_NO_INTERRUPT     4 (to 238)
             246 UNPACK_SEQUENCE          2
             250 STORE_FAST               5 (stdout)
             252 STORE_FAST               6 (stderr)

1155         254 LOAD_FAST                5 (stdout)
             256 STORE_SUBSCR
             260 CACHE
             262 CACHE
             264 CACHE
             266 CACHE
             268 CACHE
             270 CACHE
             272 CACHE
             274 CACHE
             276 CACHE
             278 UNPACK_SEQUENCE          0
             282 CALL                     0
             290 CACHE
             292 STORE_SUBSCR
             296 CACHE
             298 CACHE
             300 CACHE
             302 CACHE
             304 CACHE
             306 CACHE
             308 CACHE
             310 CACHE
             312 CACHE
             314 UNPACK_SEQUENCE          0
             318 CALL                     0
             326 CACHE
             328 STORE_FAST               7 (output)

1156         330 LOAD_CONST               6 ('Scan starting')
             332 LOAD_FAST                7 (output)
             334 CONTAINS_OP              0
             336 POP_JUMP_IF_TRUE         4 (to 346)
             338 LOAD_CONST               7 ('Scan completed')
             340 LOAD_FAST                7 (output)
             342 CONTAINS_OP              0
             344 POP_JUMP_IF_FALSE       13 (to 372)

1157     >>  346 LOAD_CONST               8 ('🛡️ सिस्टम स्कैन पूरा हुआ:\n\n')
             348 LOAD_FAST                7 (output)
             350 LOAD_CONST               9 (-500)
             352 LOAD_CONST               2 (None)
             354 BUILD_SLICE              2
             356 BINARY_SUBSCR
             360 CACHE
             362 CACHE
             364 CACHE
             366 FORMAT_VALUE             0
             368 BUILD_STRING             2
             370 RETURN_VALUE

1159     >>  372 LOAD_CONST              10 ('⚠️ स्कैन पूरा हुआ, लेकिन कोई जानकारी नहीं मिली:\n\n')
             374 LOAD_FAST                7 (output)
             376 LOAD_CONST               9 (-500)
             378 LOAD_CONST               2 (None)
             380 BUILD_SLICE              2
             382 BINARY_SUBSCR
             386 CACHE
             388 CACHE
             390 CACHE
             392 FORMAT_VALUE             0
             394 BUILD_STRING             2
             396 RETURN_VALUE
         >>  398 PUSH_EXC_INFO

1161         400 LOAD_GLOBAL             16 (Exception)
             410 CACHE
             412 CHECK_EXC_MATCH
             414 POP_JUMP_IF_FALSE       28 (to 472)
             416 STORE_FAST               8 (e)

1162         418 LOAD_CONST              11 ('❌ स्कैन में त्रुटि: ')
             420 LOAD_GLOBAL             19 (NULL + str)
             430 CACHE
             432 LOAD_FAST                8 (e)
             434 UNPACK_SEQUENCE          1
             438 CALL                     1
             446 CACHE
             448 FORMAT_VALUE             0
             450 BUILD_STRING             2
             452 SWAP                     2
             454 POP_EXCEPT
             456 LOAD_CONST               2 (None)
             458 STORE_FAST               8 (e)
             460 DELETE_FAST              8 (e)
             462 RETURN_VALUE
         >>  464 LOAD_CONST               2 (None)
             466 STORE_FAST               8 (e)
             468 DELETE_FAST              8 (e)
             470 RERAISE                  1

1161     >>  472 RERAISE                  0
         >>  474 COPY                     3
             476 POP_EXCEPT
             478 RERAISE                  1
ExceptionTable:
  24 to 38 -> 398 [0]
  42 to 100 -> 104 [0]
  102 to 102 -> 398 [0]
  104 to 182 -> 190 [1] lasti
  184 to 186 -> 398 [0]
  188 to 188 -> 190 [1] lasti
  190 to 368 -> 398 [0]
  372 to 394 -> 398 [0]
  398 to 416 -> 474 [1] lasti
  418 to 450 -> 464 [1] lasti
  452 to 452 -> 474 [1] lasti
  464 to 472 -> 474 [1] lasti

Disassembly of <code object enable_camera_analysis at 0x000001EFF3F23AB0, file "tools.py", line 1167>:
1167           0 RETURN_GENERATOR
               2 POP_TOP
               4 RESUME                   0

1179           6 NOP

1180           8 LOAD_GLOBAL              0 (assistant_instance)
              18 CACHE
              20 POP_JUMP_IF_NOT_NONE     2 (to 26)

1181          22 LOAD_CONST               2 ('Assistant instance not set')
              24 RETURN_VALUE

1182     >>   26 LOAD_GLOBAL              0 (assistant_instance)
              36 CACHE
              38 STORE_SUBSCR
              42 CACHE
              44 CACHE
              46 CACHE
              48 CACHE
              50 CACHE
              52 CACHE
              54 CACHE
              56 CACHE
              58 CACHE
              60 LOAD_FAST                0 (enable)
              62 UNPACK_SEQUENCE          1
              66 CALL                     1
              74 CACHE
              76 GET_AWAITABLE            0
              78 LOAD_CONST               1 (None)
         >>   80 SEND                     3 (to 90)
              84 RESUME                   3
              86 JUMP_BACKWARD_NO_INTERRUPT     4 (to 80)
              88 RETURN_VALUE
         >>   90 PUSH_EXC_INFO

1183          92 LOAD_GLOBAL              4 (Exception)
             102 CACHE
             104 CHECK_EXC_MATCH
             106 POP_JUMP_IF_FALSE       35 (to 178)
             108 STORE_FAST               1 (e)

1184         110 LOAD_CONST               3 ('Failed to ')
             112 LOAD_FAST                0 (enable)
             114 POP_JUMP_IF_FALSE        2 (to 120)
             116 LOAD_CONST               4 ('enable')
             118 JUMP_FORWARD             1 (to 122)
         >>  120 LOAD_CONST               5 ('disable')
         >>  122 FORMAT_VALUE             0
             124 LOAD_CONST               6 (' camera analysis: ')
             126 LOAD_GLOBAL              7 (NULL + str)
             136 CACHE
             138 LOAD_FAST                1 (e)
             140 UNPACK_SEQUENCE          1
             144 CALL                     1
             152 CACHE
             154 FORMAT_VALUE             0
             156 BUILD_STRING             4
             158 SWAP                     2
             160 POP_EXCEPT
             162 LOAD_CONST               1 (None)
             164 STORE_FAST               1 (e)
             166 DELETE_FAST              1 (e)
             168 RETURN_VALUE
         >>  170 LOAD_CONST               1 (None)
             172 STORE_FAST               1 (e)
             174 DELETE_FAST              1 (e)
             176 RERAISE                  1

1183     >>  178 RERAISE                  0
         >>  180 COPY                     3
             182 POP_EXCEPT
             184 RERAISE                  1
ExceptionTable:
  8 to 20 -> 90 [0]
  26 to 86 -> 90 [0]
  90 to 108 -> 180 [1] lasti
  110 to 156 -> 170 [1] lasti
  158 to 158 -> 180 [1] lasti
  170 to 178 -> 180 [1] lasti

Disassembly of <code object analyze_visual_scene at 0x000001EFF4BAEBF0, file "tools.py", line 1188>:
1188           0 RETURN_GENERATOR
               2 POP_TOP
               4 RESUME                   0

1204           6 NOP

1206           8 LOAD_GLOBAL              0 (assistant_instance)
              18 CACHE
              20 STORE_SUBSCR
              24 CACHE
              26 CACHE
              28 CACHE
              30 CACHE
              32 CACHE
              34 CACHE
              36 CACHE
              38 CACHE
              40 CACHE
              42 LOAD_CONST               1 (True)
              44 UNPACK_SEQUENCE          1
              48 CALL                     1
              56 CACHE
              58 GET_AWAITABLE            0
              60 LOAD_CONST               2 (None)
         >>   62 SEND                     3 (to 72)
              66 RESUME                   3
              68 JUMP_BACKWARD_NO_INTERRUPT     4 (to 62)
              70 POP_TOP

1207     >>   72 LOAD_GLOBAL              5 (NULL + asyncio)
              82 CACHE
              84 LOAD_ATTR                3 (NULL|self + enable_visual_analysis)
             104 CACHE
             106 CACHE
             108 CACHE
             110 GET_AWAITABLE            0
             112 LOAD_CONST               2 (None)
         >>  114 SEND                     3 (to 124)
             118 RESUME                   3
             120 JUMP_BACKWARD_NO_INTERRUPT     4 (to 114)
             122 POP_TOP

1208     >>  124 LOAD_GLOBAL              0 (assistant_instance)
             134 CACHE
             136 STORE_SUBSCR
             140 CACHE
             142 CACHE
             144 CACHE
             146 CACHE
             148 CACHE
             150 CACHE
             152 CACHE
             154 CACHE
             156 CACHE
             158 LOAD_FAST                0 (prompt)
             160 UNPACK_SEQUENCE          1
             164 CALL                     1
             172 CACHE
             174 GET_AWAITABLE            0
             176 LOAD_CONST               2 (None)
         >>  178 SEND                     3 (to 188)
             182 RESUME                   3
             184 JUMP_BACKWARD_NO_INTERRUPT     4 (to 178)
             186 STORE_FAST               1 (result)

1209     >>  188 LOAD_GLOBAL              0 (assistant_instance)
             198 CACHE
             200 STORE_SUBSCR
             204 CACHE
             206 CACHE
             208 CACHE
             210 CACHE
             212 CACHE
             214 CACHE
             216 CACHE
             218 CACHE
             220 CACHE
             222 LOAD_CONST               4 (False)
             224 UNPACK_SEQUENCE          1
             228 CALL                     1
             236 CACHE
             238 GET_AWAITABLE            0
             240 LOAD_CONST               2 (None)
         >>  242 SEND                     3 (to 252)
             246 RESUME                   3
             248 JUMP_BACKWARD_NO_INTERRUPT     4 (to 242)
             250 POP_TOP

1210     >>  252 LOAD_FAST                1 (result)
             254 RETURN_VALUE
         >>  256 PUSH_EXC_INFO

1212         258 LOAD_GLOBAL             10 (Exception)
             268 CACHE
             270 CHECK_EXC_MATCH
             272 POP_JUMP_IF_FALSE       28 (to 330)
             274 STORE_FAST               2 (e)

1213         276 LOAD_CONST               5 ('Visual analysis failed: ')
             278 LOAD_GLOBAL             13 (NULL + str)
             288 CACHE
             290 LOAD_FAST                2 (e)
             292 UNPACK_SEQUENCE          1
             296 CALL                     1
             304 CACHE
             306 FORMAT_VALUE             0
             308 BUILD_STRING             2
             310 SWAP                     2
             312 POP_EXCEPT
             314 LOAD_CONST               2 (None)
             316 STORE_FAST               2 (e)
             318 DELETE_FAST              2 (e)
             320 RETURN_VALUE
         >>  322 LOAD_CONST               2 (None)
             324 STORE_FAST               2 (e)
             326 DELETE_FAST              2 (e)
             328 RERAISE                  1

1212     >>  330 RERAISE                  0
         >>  332 COPY                     3
             334 POP_EXCEPT
             336 RERAISE                  1
ExceptionTable:
  8 to 252 -> 256 [0]
  256 to 274 -> 332 [1] lasti
  276 to 308 -> 322 [1] lasti
  310 to 310 -> 332 [1] lasti
  322 to 330 -> 332 [1] lasti

Disassembly of <code object _open_file_dialog at 0x000001EFF4C44050, file "tools.py", line 1258>:
1258           0 RETURN_GENERATOR
               2 POP_TOP
               4 RESUME                   0

1261           6 NOP

1262           8 LOAD_GLOBAL              1 (NULL + reset_analysis)
              18 CACHE
              20 UNPACK_SEQUENCE          0
              24 CALL                     0
              32 CACHE
              34 POP_TOP

1263          36 LOAD_GLOBAL              3 (NULL + tk)
              46 CACHE
              48 LOAD_ATTR                2 (tk)
              68 CACHE
              70 CACHE
              72 STORE_FAST               0 (root)

1264          74 LOAD_FAST                0 (root)
              76 STORE_SUBSCR
              80 CACHE
              82 CACHE
              84 CACHE
              86 CACHE
              88 CACHE
              90 CACHE
              92 CACHE
              94 CACHE
              96 CACHE
              98 UNPACK_SEQUENCE          0
             102 CALL                     0
             110 CACHE
             112 POP_TOP

1265         114 LOAD_FAST                0 (root)
             116 STORE_SUBSCR
             120 CACHE
             122 CACHE
             124 CACHE
             126 CACHE
             128 CACHE
             130 CACHE
             132 CACHE
             134 CACHE
             136 CACHE
             138 LOAD_CONST               1 ('-topmost')
             140 LOAD_CONST               2 (True)
             142 UNPACK_SEQUENCE          2
             146 CALL                     2
             154 CACHE
             156 POP_TOP

1266         158 LOAD_FAST                0 (root)
             160 STORE_SUBSCR
             164 CACHE
             166 CACHE
             168 CACHE
             170 CACHE
             172 CACHE
             174 CACHE
             176 CACHE
             178 CACHE
             180 CACHE
             182 UNPACK_SEQUENCE          0
             186 CALL                     0
             194 CACHE
             196 POP_TOP

1268         198 LOAD_GLOBAL             13 (NULL + filedialog)
             208 CACHE
             210 LOAD_ATTR                7 (NULL|self + withdraw)
             230 UNPACK_SEQUENCE          2
             234 CALL                     2
             242 CACHE
             244 STORE_FAST               1 (file_path)

1277         246 LOAD_FAST                0 (root)
             248 STORE_SUBSCR
             252 CACHE
             254 CACHE
             256 CACHE
             258 CACHE
             260 CACHE
             262 CACHE
             264 CACHE
             266 CACHE
             268 CACHE
             270 UNPACK_SEQUENCE          0
             274 CALL                     0
             282 CACHE
             284 POP_TOP

1278         286 LOAD_FAST                1 (file_path)
             288 POP_JUMP_IF_FALSE        2 (to 294)
             290 LOAD_FAST                1 (file_path)
             292 JUMP_FORWARD             1 (to 296)
         >>  294 LOAD_CONST               6 ('')
         >>  296 RETURN_VALUE
         >>  298 PUSH_EXC_INFO

1280         300 LOAD_GLOBAL             18 (Exception)
             310 CACHE
             312 CHECK_EXC_MATCH
             314 POP_JUMP_IF_FALSE       29 (to 374)
             316 STORE_FAST               2 (e)

1281         318 LOAD_GLOBAL             21 (NULL + print)
             328 CACHE
             330 LOAD_CONST               7 ('Error in file dialog: ')
             332 LOAD_FAST                2 (e)
             334 FORMAT_VALUE             0
             336 BUILD_STRING             2
             338 UNPACK_SEQUENCE          1
             342 CALL                     1
             350 CACHE
             352 POP_TOP

1282         354 POP_EXCEPT
             356 LOAD_CONST               8 (None)
             358 STORE_FAST               2 (e)
             360 DELETE_FAST              2 (e)
             362 LOAD_CONST               6 ('')
             364 RETURN_VALUE
         >>  366 LOAD_CONST               8 (None)
             368 STORE_FAST               2 (e)
             370 DELETE_FAST              2 (e)
             372 RERAISE                  1

1280     >>  374 RERAISE                  0
         >>  376 COPY                     3
             378 POP_EXCEPT
             380 RERAISE                  1
ExceptionTable:
  8 to 294 -> 298 [0]
  298 to 316 -> 376 [1] lasti
  318 to 352 -> 366 [1] lasti
  366 to 374 -> 376 [1] lasti

Disassembly of <code object _load_data_file at 0x000001EFF4B625F0, file "tools.py", line 1284>:
1284           0 RESUME                   0

1286           2 NOP

1287           4 LOAD_GLOBAL              0 (os)
              14 CACHE
              16 LOAD_ATTR                1 (NULL|self + os)
              36 CACHE
              38 CACHE
              40 CACHE
              42 CACHE
              44 CACHE
              46 CACHE
              48 LOAD_FAST                0 (file_path)
              50 UNPACK_SEQUENCE          1
              54 CALL                     1
              62 CACHE
              64 POP_JUMP_IF_TRUE        18 (to 102)

1288          66 LOAD_GLOBAL              7 (NULL + FileNotFoundError)
              76 CACHE
              78 LOAD_CONST               1 ('File not found: ')
              80 LOAD_FAST                0 (file_path)
              82 FORMAT_VALUE             0
              84 BUILD_STRING             2
              86 UNPACK_SEQUENCE          1
              90 CALL                     1
              98 CACHE
             100 RAISE_VARARGS            1

1290     >>  102 LOAD_GLOBAL              9 (NULL + Path)
             112 CACHE
             114 LOAD_FAST                0 (file_path)
             116 UNPACK_SEQUENCE          1
             120 CALL                     1
             128 CACHE
             130 LOAD_ATTR                5 (NULL|self + exists)
             150 CACHE
             152 CACHE
             154 CACHE
             156 CACHE
             158 CACHE
             160 CACHE
             162 UNPACK_SEQUENCE          0
             166 CALL                     0
             174 CACHE
             176 STORE_FAST               1 (file_ext)

1292         178 LOAD_FAST                1 (file_ext)
             180 LOAD_CONST               2 ('.csv')
             182 COMPARE_OP               2 (<)
             186 CACHE
             188 POP_JUMP_IF_FALSE       29 (to 248)

1294         190 LOAD_GLOBAL             15 (NULL + pd)
             200 CACHE
             202 LOAD_ATTR                8 (Path)

1300         222 BUILD_LIST               0
             224 LOAD_CONST               7 (('', 'NULL', 'null', 'NaN', 'nan', 'N/A', 'n/a'))
             226 LIST_EXTEND              1

1294         228 KW_NAMES                 8 (('encoding', 'low_memory', 'engine', 'skipinitialspace', 'na_values'))
             230 UNPACK_SEQUENCE          6
             234 CALL                     6
             242 CACHE
             244 STORE_FAST               2 (df)
             246 JUMP_FORWARD            74 (to 396)

1303     >>  248 LOAD_FAST                1 (file_ext)
             250 LOAD_CONST               9 (('.xlsx', '.xls'))
             252 CONTAINS_OP              0
             254 POP_JUMP_IF_FALSE       52 (to 360)

1305         256 LOAD_FAST                1 (file_ext)
             258 LOAD_CONST              10 ('.xlsx')
             260 COMPARE_OP               2 (<)
             264 CACHE
             266 POP_JUMP_IF_FALSE       23 (to 314)

1306         268 LOAD_GLOBAL             15 (NULL + pd)
             278 CACHE
             280 LOAD_ATTR                9 (NULL|self + Path)
             300 CALL                     2
             308 CACHE
             310 STORE_FAST               2 (df)
             312 JUMP_FORWARD            41 (to 396)

1308     >>  314 LOAD_GLOBAL             15 (NULL + pd)
             324 CACHE
             326 LOAD_ATTR                9 (NULL|self + Path)
             346 CALL                     2
             354 CACHE
             356 STORE_FAST               2 (df)
             358 JUMP_FORWARD            18 (to 396)

1310     >>  360 LOAD_GLOBAL             21 (NULL + ValueError)
             370 CACHE
             372 LOAD_CONST              14 ('Unsupported file format: ')
             374 LOAD_FAST                1 (file_ext)
             376 FORMAT_VALUE             0
             378 BUILD_STRING             2
             380 UNPACK_SEQUENCE          1
             384 CALL                     1
             392 CACHE
             394 RAISE_VARARGS            1

1313     >>  396 LOAD_FAST                2 (df)
             398 LOAD_ATTR               11 (NULL|self + suffix)
             418 CACHE
             420 CACHE
             422 CACHE
             424 CACHE
             426 CACHE
             428 CACHE
             430 LOAD_GLOBAL             26 (str)
             440 CACHE
             442 UNPACK_SEQUENCE          1
             446 CALL                     1
             454 CACHE
             456 LOAD_ATTR               13 (NULL|self + lower)
             476 CACHE
             478 CACHE
             480 CACHE
             482 CACHE
             484 CACHE
             486 CACHE
             488 UNPACK_SEQUENCE          0
             492 CALL                     0
             500 CACHE
             502 LOAD_FAST                2 (df)
             504 STORE_ATTR              11 (columns)

1316         514 LOAD_FAST                2 (df)
             516 STORE_SUBSCR
             520 CACHE
             522 CACHE
             524 CACHE
             526 CACHE
             528 CACHE
             530 CACHE
             532 CACHE
             534 CACHE
             536 CACHE
             538 LOAD_CONST              15 ('all')
             540 KW_NAMES                16 (('how',))
             542 UNPACK_SEQUENCE          1
             546 CALL                     1
             554 CACHE
             556 STORE_SUBSCR
             560 CACHE
             562 CACHE
             564 CACHE
             566 CACHE
             568 CACHE
             570 CACHE
             572 CACHE
             574 CACHE
             576 CACHE
             578 LOAD_CONST               6 (True)
             580 KW_NAMES                17 (('drop',))
             582 UNPACK_SEQUENCE          1
             586 CALL                     1
             594 CACHE
             596 STORE_FAST               2 (df)

1317         598 LOAD_FAST                2 (df)
             600 LOAD_ATTR               11 (NULL|self + suffix)
             620 STORE_SUBSCR
             624 CACHE
             626 CACHE
             628 CACHE
             630 CACHE
             632 CACHE
             634 CACHE
             636 CACHE
             638 CACHE
             640 CACHE
             642 LOAD_CONST              18 ('^Unnamed')
             644 LOAD_CONST               4 (False)
             646 KW_NAMES                19 (('na',))
             648 UNPACK_SEQUENCE          2
             652 CALL                     2
             660 CACHE
             662 STORE_FAST               3 (unnamed_cols)

1318         664 LOAD_FAST                2 (df)
             666 LOAD_ATTR               18 (read_excel)
             686 BUILD_TUPLE              2
             688 BINARY_SUBSCR
             692 CACHE
             694 CACHE
             696 CACHE
             698 STORE_FAST               2 (df)

1320         700 LOAD_FAST                2 (df)
             702 RETURN_VALUE
         >>  704 PUSH_EXC_INFO

1322         706 LOAD_GLOBAL             38 (Exception)
             716 CACHE
             718 CHECK_EXC_MATCH
             720 POP_JUMP_IF_FALSE       29 (to 780)
             722 STORE_FAST               4 (e)

1323         724 LOAD_GLOBAL             41 (NULL + print)
             734 CACHE
             736 LOAD_CONST              21 ('Error loading file: ')
             738 LOAD_FAST                4 (e)
             740 FORMAT_VALUE             0
             742 BUILD_STRING             2
             744 UNPACK_SEQUENCE          1
             748 CALL                     1
             756 CACHE
             758 POP_TOP

1324         760 POP_EXCEPT
             762 LOAD_CONST              20 (None)
             764 STORE_FAST               4 (e)
             766 DELETE_FAST              4 (e)
             768 LOAD_CONST              20 (None)
             770 RETURN_VALUE
         >>  772 LOAD_CONST              20 (None)
             774 STORE_FAST               4 (e)
             776 DELETE_FAST              4 (e)
             778 RERAISE                  1

1322     >>  780 RERAISE                  0
         >>  782 COPY                     3
             784 POP_EXCEPT
             786 RERAISE                  1
ExceptionTable:
  4 to 700 -> 704 [0]
  704 to 722 -> 782 [1] lasti
  724 to 758 -> 772 [1] lasti
  772 to 780 -> 782 [1] lasti

Disassembly of <code object _detect_column_types_fast at 0x000001EFF4BB3650, file "tools.py", line 1326>:
1326           0 RESUME                   0

1328           2 BUILD_MAP                0
               4 STORE_FAST               1 (type_info)

1331           6 LOAD_FAST                0 (df)
               8 LOAD_ATTR                0 (columns)
              28 LOAD_FAST                2 (col)
              30 BINARY_SUBSCR
              34 CACHE
              36 CACHE
              38 CACHE
              40 STORE_FAST               3 (col_data)

1334          42 LOAD_FAST                3 (col_data)
              44 STORE_SUBSCR
              48 CACHE
              50 CACHE
              52 CACHE
              54 CACHE
              56 CACHE
              58 CACHE
              60 CACHE
              62 CACHE
              64 CACHE
              66 UNPACK_SEQUENCE          0
              70 CALL                     0
              78 CACHE
              80 STORE_SUBSCR
              84 CACHE
              86 CACHE
              88 CACHE
              90 CACHE
              92 CACHE
              94 CACHE
              96 CACHE
              98 CACHE
             100 CACHE
             102 UNPACK_SEQUENCE          0
             106 CALL                     0
             114 CACHE
             116 POP_JUMP_IF_FALSE        6 (to 130)

1335         118 LOAD_CONST               1 ('empty')
             120 LOAD_FAST                1 (type_info)
             122 LOAD_FAST                2 (col)
             124 STORE_SUBSCR

1336         128 JUMP_BACKWARD           55 (to 20)

1339     >>  130 LOAD_FAST                3 (col_data)
             132 LOAD_ATTR                3 (NULL|self + isna)
             152 CACHE
             154 CACHE
             156 LOAD_ATTR                5 (NULL|self + all)
             176 STORE_SUBSCR
             180 CACHE
             182 CACHE
             184 CACHE
             186 CACHE
             188 CACHE
             190 CACHE
             192 CACHE
             194 CACHE
             196 CACHE
             198 LOAD_FAST                4 (dtype)
             200 UNPACK_SEQUENCE          1
             204 CALL                     1
             212 CACHE
             214 POP_JUMP_IF_FALSE        6 (to 228)

1342         216 LOAD_CONST               2 ('numeric')
             218 LOAD_FAST                1 (type_info)
             220 LOAD_FAST                2 (col)
             222 STORE_SUBSCR
             226 JUMP_BACKWARD          104 (to 20)

1343     >>  228 LOAD_GLOBAL              8 (pd)
             238 CACHE
             240 LOAD_ATTR                5 (NULL|self + all)
             260 STORE_SUBSCR
             264 CACHE
             266 CACHE
             268 CACHE
             270 CACHE
             272 CACHE
             274 CACHE
             276 CACHE
             278 CACHE
             280 CACHE
             282 LOAD_FAST                4 (dtype)
             284 UNPACK_SEQUENCE          1
             288 CALL                     1
             296 CACHE
             298 POP_JUMP_IF_FALSE        6 (to 312)

1344         300 LOAD_CONST               3 ('datetime')
             302 LOAD_FAST                1 (type_info)
             304 LOAD_FAST                2 (col)
             306 STORE_SUBSCR
             310 JUMP_BACKWARD          146 (to 20)

1347     >>  312 LOAD_GLOBAL             19 (NULL + min)
             322 CACHE
             324 LOAD_CONST               4 (100)
             326 LOAD_GLOBAL             21 (NULL + len)
             336 CACHE
             338 LOAD_FAST                3 (col_data)
             340 STORE_SUBSCR
             344 CACHE
             346 CACHE
             348 CACHE
             350 CACHE
             352 CACHE
             354 CACHE
             356 CACHE
             358 CACHE
             360 CACHE
             362 UNPACK_SEQUENCE          0
             366 CALL                     0
             374 CACHE
             376 UNPACK_SEQUENCE          1
             380 CALL                     1
             388 CACHE
             390 UNPACK_SEQUENCE          2
             394 CALL                     2
             402 CACHE
             404 STORE_FAST               5 (sample_size)

1348         406 LOAD_FAST                5 (sample_size)
             408 LOAD_CONST               5 (0)
             410 COMPARE_OP               4 (<)
             414 CACHE
             416 EXTENDED_ARG             1
             418 POP_JUMP_IF_FALSE      267 (to 954)

1349         420 LOAD_FAST                3 (col_data)
             422 STORE_SUBSCR
             426 CACHE
             428 CACHE
             430 CACHE
             432 CACHE
             434 CACHE
             436 CACHE
             438 CACHE
             440 CACHE
             442 CACHE
             444 UNPACK_SEQUENCE          0
             448 CALL                     0
             456 CACHE
             458 LOAD_ATTR               12 (types)
             478 CACHE
             480 CACHE
             482 CACHE
             484 STORE_FAST               6 (sample)

1350         486 LOAD_FAST                6 (sample)
             488 STORE_SUBSCR
             492 CACHE
             494 CACHE
             496 CACHE
             498 CACHE
             500 CACHE
             502 CACHE
             504 CACHE
             506 CACHE
             508 CACHE
             510 LOAD_GLOBAL             28 (str)
             520 CACHE
             522 UNPACK_SEQUENCE          1
             526 CALL                     1
             534 CACHE
             536 LOAD_ATTR               14 (is_numeric_dtype)
             556 CACHE
             558 CACHE
             560 CACHE
             562 CACHE
             564 CACHE
             566 CACHE
             568 UNPACK_SEQUENCE          0
             572 CALL                     0
             580 CACHE
             582 STORE_FAST               7 (sample_str)

1352         584 LOAD_FAST                7 (sample_str)
             586 LOAD_ATTR               14 (is_numeric_dtype)
             606 CACHE
             608 CACHE
             610 CACHE
             612 CACHE
             614 CACHE
             616 CACHE
             618 LOAD_CONST               7 ('date|2020|2021|2022|2023|2024|2025')
             620 LOAD_CONST               8 (False)
             622 KW_NAMES                 9 (('na',))
             624 UNPACK_SEQUENCE          2
             628 CALL                     2
             636 CACHE
             638 STORE_SUBSCR
             642 CACHE
             644 CACHE
             646 CACHE
             648 CACHE
             650 CACHE
             652 CACHE
             654 CACHE
             656 CACHE
             658 CACHE
             660 UNPACK_SEQUENCE          0
             664 CALL                     0
             672 CACHE
             674 POP_JUMP_IF_FALSE        7 (to 690)

1353         676 LOAD_CONST              10 ('potential_date')
             678 LOAD_FAST                1 (type_info)
             680 LOAD_FAST                2 (col)
             682 STORE_SUBSCR
             686 EXTENDED_ARG             1
             688 JUMP_BACKWARD          335 (to 20)

1354     >>  690 LOAD_FAST                2 (col)
             692 STORE_SUBSCR
             696 CACHE
             698 CACHE
             700 CACHE
             702 CACHE
             704 CACHE
             706 CACHE
             708 CACHE
             710 CACHE
             712 CACHE
             714 UNPACK_SEQUENCE          0
             718 CALL                     0
             726 CACHE
             728 STORE_SUBSCR
             732 CACHE
             734 CACHE
             736 CACHE
             738 CACHE
             740 CACHE
             742 CACHE
             744 CACHE
             746 CACHE
             748 CACHE
             750 LOAD_CONST              11 ('_id')
             752 UNPACK_SEQUENCE          1
             756 CALL                     1
             764 CACHE
             766 POP_JUMP_IF_TRUE        22 (to 812)
             768 LOAD_CONST              12 ('id')
             770 LOAD_FAST                2 (col)
             772 STORE_SUBSCR
             776 CACHE
             778 CACHE
             780 CACHE
             782 CACHE
             784 CACHE
             786 CACHE
             788 CACHE
             790 CACHE
             792 CACHE
             794 UNPACK_SEQUENCE          0
             798 CALL                     0
             806 CACHE
             808 CONTAINS_OP              0
             810 POP_JUMP_IF_FALSE        7 (to 826)

1355     >>  812 LOAD_CONST              13 ('identifier')
             814 LOAD_FAST                1 (type_info)
             816 LOAD_FAST                2 (col)
             818 STORE_SUBSCR
             822 EXTENDED_ARG             1
             824 JUMP_BACKWARD          403 (to 20)

1357     >>  826 LOAD_GLOBAL             21 (NULL + len)
             836 CACHE
             838 LOAD_FAST                6 (sample)
             840 STORE_SUBSCR
             844 CACHE
             846 CACHE
             848 CACHE
             850 CACHE
             852 CACHE
             854 CACHE
             856 CACHE
             858 CACHE
             860 CACHE
             862 UNPACK_SEQUENCE          0
             866 CALL                     0
             874 CACHE
             876 UNPACK_SEQUENCE          1
             880 CALL                     1
             888 CACHE
             890 LOAD_GLOBAL             21 (NULL + len)
             900 CACHE
             902 LOAD_FAST                6 (sample)
             904 UNPACK_SEQUENCE          1
             908 CALL                     1
             916 CACHE
             918 BINARY_OP               11 (/)
             922 STORE_FAST               8 (unique_ratio)

1358         924 LOAD_FAST                8 (unique_ratio)
             926 LOAD_CONST              14 (0.5)
             928 COMPARE_OP               0 (<)
             932 CACHE
             934 POP_JUMP_IF_FALSE        2 (to 940)
             936 LOAD_CONST              15 ('categorical')
             938 JUMP_FORWARD             1 (to 942)
         >>  940 LOAD_CONST              16 ('text')
         >>  942 LOAD_FAST                1 (type_info)
             944 LOAD_FAST                2 (col)
             946 STORE_SUBSCR
             950 EXTENDED_ARG             1
             952 JUMP_BACKWARD          467 (to 20)

1360     >>  954 LOAD_CONST              16 ('text')
             956 LOAD_FAST                1 (type_info)
             958 LOAD_FAST                2 (col)
             960 STORE_SUBSCR
             964 EXTENDED_ARG             1
             966 JUMP_BACKWARD          474 (to 20)

1362         968 LOAD_FAST                1 (type_info)
             970 RETURN_VALUE

Disassembly of <code object _detect_business_context_fast at 0x000001EFF4B62CA0, file "tools.py", line 1364>:
               0 MAKE_CELL               12 (all_text)
               2 MAKE_CELL               13 (col)

1364           4 RESUME                   0

1367           6 LOAD_CONST               1 ('unknown')

1368           8 BUILD_LIST               0

1369          10 BUILD_LIST               0

1370          12 BUILD_LIST               0

1371          14 BUILD_LIST               0

1372          16 BUILD_LIST               0

1366          18 LOAD_CONST               2 (('domain', 'key_metrics', 'time_columns', 'id_columns', 'categorical_columns', 'numeric_columns'))
              20 BUILD_CONST_KEY_MAP      6
              22 STORE_FAST               1 (context)

1376          24 LOAD_GLOBAL              1 (NULL + pd)
              34 CACHE
              36 LOAD_ATTR                1 (NULL|self + pd)
              56 CACHE
              58 UNPACK_SEQUENCE          1
              62 CALL                     1
              70 CACHE
              72 LOAD_ATTR                3 (NULL|self + Series)
              92 CACHE
              94 CACHE
              96 CACHE
              98 CACHE
             100 CACHE
             102 CACHE
             104 UNPACK_SEQUENCE          0
             108 CALL                     0
             116 CACHE
             118 STORE_FAST               2 (columns_lower)

1380         120 BUILD_LIST               0
             122 LOAD_CONST               3 (('sales', 'revenue', 'profit', 'customer', 'order', 'amount', 'price'))
             124 LIST_EXTEND              1

1381         126 BUILD_LIST               0
             128 LOAD_CONST               4 (('employee', 'salary', 'department', 'performance', 'rating'))
             130 LIST_EXTEND              1

1382         132 BUILD_LIST               0
             134 LOAD_CONST               5 (('product', 'stock', 'quantity', 'warehouse', 'inventory'))
             136 LIST_EXTEND              1

1383         138 BUILD_LIST               0
             140 LOAD_CONST               6 (('campaign', 'clicks', 'impressions', 'conversion', 'marketing'))
             142 LIST_EXTEND              1

1384         144 BUILD_LIST               0
             146 LOAD_CONST               7 (('expense', 'cost', 'budget', 'account', 'balance', 'financial'))
             148 LIST_EXTEND              1

1379         150 LOAD_CONST               8 (('sales', 'hr', 'inventory', 'marketing', 'financial'))
             152 BUILD_CONST_KEY_MAP      5
             154 STORE_FAST               3 (domain_keywords)

1387         156 LOAD_CONST               9 (' ')
             158 STORE_SUBSCR
             162 CACHE
             164 CACHE
             166 CACHE
             168 CACHE
             170 CACHE
             172 CACHE
             174 CACHE
             176 CACHE
             178 CACHE
             180 LOAD_FAST                2 (columns_lower)
             182 UNPACK_SEQUENCE          1
             186 CALL                     1
             194 CACHE
             196 STORE_DEREF             12 (all_text)

1388         198 LOAD_CONST              10 (0)
             200 STORE_FAST               4 (max_score)

1389         202 LOAD_FAST                3 (domain_keywords)
             204 STORE_SUBSCR
             208 CACHE
             210 CACHE
             212 CACHE
             214 CACHE
             216 CACHE
             218 CACHE
             220 CACHE
             222 CACHE
             224 CACHE
             226 UNPACK_SEQUENCE          0
             230 CALL                     0
             238 CACHE
             240 GET_ITER
         >>  242 FOR_ITER                45 (to 336)
             246 CACHE
             248 STORE_FAST               5 (domain)
             250 STORE_FAST               6 (keywords)

1390         252 LOAD_GLOBAL             15 (NULL + sum)
             262 CACHE
             264 LOAD_CLOSURE            12 (all_text)
             266 BUILD_TUPLE              1
             268 LOAD_CONST              11 (<code object <genexpr> at 0x000001EFF69BC3F0, file "tools.py", line 1390>)
             270 MAKE_FUNCTION            8 (closure)
             272 LOAD_FAST                6 (keywords)
             274 GET_ITER
             276 UNPACK_SEQUENCE          0
             280 CALL                     0
             288 CACHE
             290 UNPACK_SEQUENCE          1
             294 CALL                     1
             302 CACHE
             304 STORE_FAST               7 (score)

1391         306 LOAD_FAST                7 (score)
             308 LOAD_FAST                4 (max_score)
             310 COMPARE_OP               4 (<)
             314 CACHE
             316 POP_JUMP_IF_FALSE        7 (to 332)

1392         318 LOAD_FAST                7 (score)
             320 STORE_FAST               4 (max_score)

1393         322 LOAD_FAST                5 (domain)
             324 LOAD_FAST                1 (context)
             326 LOAD_CONST              12 ('domain')
             328 STORE_SUBSCR
         >>  332 JUMP_BACKWARD           46 (to 242)

1396         334 LOAD_FAST                2 (columns_lower)
         >>  336 LOAD_ATTR                3 (NULL|self + Series)
             356 CACHE
             358 CACHE
             360 CACHE
             362 CACHE
             364 CACHE
             366 CACHE
             368 LOAD_CONST              13 ('date|time|month|year')
             370 LOAD_CONST              14 (False)
             372 KW_NAMES                15 (('na',))
             374 UNPACK_SEQUENCE          2
             378 CALL                     2
             386 CACHE
             388 STORE_FAST               8 (time_mask)

1397         390 LOAD_FAST                2 (columns_lower)
             392 LOAD_ATTR                3 (NULL|self + Series)
             412 CACHE
             414 CACHE
             416 CACHE
             418 CACHE
             420 CACHE
             422 CACHE
             424 LOAD_CONST              16 ('id|_id$')
             426 LOAD_CONST              14 (False)
             428 KW_NAMES                15 (('na',))
             430 UNPACK_SEQUENCE          2
             434 CALL                     2
             442 CACHE
             444 STORE_FAST               9 (id_mask)

1399         446 LOAD_FAST                0 (df)
             448 LOAD_ATTR                2 (Series)
             468 CACHE
             470 STORE_SUBSCR
             474 CACHE
             476 CACHE
             478 CACHE
             480 CACHE
             482 CACHE
             484 CACHE
             486 CACHE
             488 CACHE
             490 CACHE
             492 UNPACK_SEQUENCE          0
             496 CALL                     0
             504 CACHE
             506 LOAD_FAST                1 (context)
             508 LOAD_CONST              17 ('time_columns')
             510 STORE_SUBSCR

1400         514 LOAD_FAST                0 (df)
             516 LOAD_ATTR                2 (Series)
             536 CACHE
             538 STORE_SUBSCR
             542 CACHE
             544 CACHE
             546 CACHE
             548 CACHE
             550 CACHE
             552 CACHE
             554 CACHE
             556 CACHE
             558 CACHE
             560 UNPACK_SEQUENCE          0
             564 CALL                     0
             572 CACHE
             574 LOAD_FAST                1 (context)
             576 LOAD_CONST              18 ('id_columns')
             578 STORE_SUBSCR

1403         582 LOAD_FAST                0 (df)
             584 STORE_SUBSCR
             588 CACHE
             590 CACHE
             592 CACHE
             594 CACHE
             596 CACHE
             598 CACHE
             600 CACHE
             602 CACHE
             604 CACHE
             606 LOAD_GLOBAL             22 (np)
             616 CACHE
             618 LOAD_ATTR               12 (items)
             638 CACHE
             640 CACHE
             642 CACHE
             644 CACHE
             646 LOAD_ATTR                2 (Series)
             666 CACHE
             668 CACHE
             670 CACHE
             672 CACHE
             674 CACHE
             676 CACHE
             678 UNPACK_SEQUENCE          0
             682 CALL                     0
             690 CACHE
             692 STORE_FAST              10 (numeric_cols)

1404         694 LOAD_FAST               10 (numeric_cols)
             696 LOAD_FAST                1 (context)
             698 LOAD_CONST              20 ('numeric_columns')
             700 STORE_SUBSCR

1407         704 BUILD_LIST               0
             706 LOAD_CONST              21 (('amount', 'price', 'cost', 'revenue', 'profit', 'sales', 'quantity', 'total', 'value', 'score'))
             708 LIST_EXTEND              1
             710 STORE_FAST              11 (metric_keywords)

1408         712 LOAD_FAST               10 (numeric_cols)
             714 GET_ITER
         >>  716 FOR_ITER                56 (to 832)

1409         720 LOAD_GLOBAL             27 (NULL + any)
             730 CACHE
             732 LOAD_CLOSURE            13 (col)
             734 BUILD_TUPLE              1
             736 LOAD_CONST              22 (<code object <genexpr> at 0x000001EFF69DCF10, file "tools.py", line 1409>)
             738 MAKE_FUNCTION            8 (closure)
             740 LOAD_FAST               11 (metric_keywords)
             742 GET_ITER
             744 UNPACK_SEQUENCE          0
             748 CALL                     0
             756 CACHE
             758 UNPACK_SEQUENCE          1
             762 CALL                     1
             770 CACHE
             772 POP_JUMP_IF_FALSE       27 (to 828)

1410         774 LOAD_FAST                1 (context)
             776 LOAD_CONST              23 ('key_metrics')
             778 BINARY_SUBSCR
             782 CACHE
             784 CACHE
             786 CACHE
             788 STORE_SUBSCR
             792 CACHE
             794 CACHE
             796 CACHE
             798 CACHE
             800 CACHE
             802 CACHE
             804 CACHE
             806 CACHE
             808 CACHE
             810 LOAD_DEREF              13 (col)
             812 UNPACK_SEQUENCE          1
             816 CALL                     1
             824 CACHE
             826 POP_TOP
         >>  828 JUMP_BACKWARD           57 (to 716)

1413         830 LOAD_FAST                0 (df)
         >>  832 LOAD_ATTR                2 (Series)
             852 CONTAINS_OP              1
             854 POP_JUMP_IF_FALSE       93 (to 1042)
             856 LOAD_DEREF              13 (col)
             858 LOAD_FAST                1 (context)
             860 LOAD_CONST              17 ('time_columns')
             862 BINARY_SUBSCR
             866 CACHE
             868 CACHE
             870 CACHE
             872 CONTAINS_OP              1
             874 POP_JUMP_IF_FALSE       83 (to 1042)
             876 LOAD_DEREF              13 (col)
             878 LOAD_FAST                1 (context)
             880 LOAD_CONST              18 ('id_columns')
             882 BINARY_SUBSCR
             886 CACHE
             888 CACHE
             890 CACHE
             892 CONTAINS_OP              1
             894 POP_JUMP_IF_FALSE       73 (to 1042)

1415         896 LOAD_FAST                0 (df)
             898 LOAD_DEREF              13 (col)
             900 BINARY_SUBSCR
             904 CACHE
             906 CACHE
             908 CACHE
             910 STORE_SUBSCR
             914 CACHE
             916 CACHE
             918 CACHE
             920 CACHE
             922 CACHE
             924 CACHE
             926 CACHE
             928 CACHE
             930 CACHE
             932 UNPACK_SEQUENCE          0
             936 CALL                     0
             944 CACHE
             946 LOAD_GLOBAL             33 (NULL + len)
             956 CACHE
             958 LOAD_FAST                0 (df)
             960 UNPACK_SEQUENCE          1
             964 CALL                     1
             972 CACHE
             974 LOAD_CONST              24 (0.5)
             976 BINARY_OP                5 (*)
             980 COMPARE_OP               0 (<)
             984 CACHE
             986 POP_JUMP_IF_FALSE       27 (to 1042)

1416         988 LOAD_FAST                1 (context)
             990 LOAD_CONST              25 ('categorical_columns')
             992 BINARY_SUBSCR
             996 CACHE
             998 CACHE
            1000 CACHE
            1002 STORE_SUBSCR
            1006 CACHE
            1008 CACHE
            1010 CACHE
            1012 CACHE
            1014 CACHE
            1016 CACHE
            1018 CACHE
            1020 CACHE
            1022 CACHE
            1024 LOAD_DEREF              13 (col)
            1026 UNPACK_SEQUENCE          1
            1030 CALL                     1
            1038 CACHE
            1040 POP_TOP
         >> 1042 JUMP_BACKWARD          100 (to 844)

1418        1044 LOAD_FAST                1 (context)
            1046 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001EFF69BC3F0, file "tools.py", line 1390>:
               0 COPY_FREE_VARS           1

1390           2 RETURN_GENERATOR
               4 POP_TOP
               6 RESUME                   0
               8 LOAD_FAST                0 (.0)
         >>   10 FOR_ITER                10 (to 34)
              14 LOAD_FAST                1 (keyword)
              16 LOAD_DEREF               2 (all_text)
              18 CONTAINS_OP              0
