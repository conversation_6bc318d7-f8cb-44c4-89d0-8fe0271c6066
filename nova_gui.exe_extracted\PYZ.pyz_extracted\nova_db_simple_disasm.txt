Simple disassembly of nova_gui.exe_extracted\PYZ.pyz_extracted\nova_db.pyc
============================================================

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 (0)
              4 LOAD_CONST               1 (None)
              6 IMPORT_NAME              0 (sqlite3)
              8 STORE_NAME               0 (sqlite3)

  2          10 LOAD_CONST               0 (0)
             12 LOAD_CONST               1 (None)
             14 IMPORT_NAME              1 (json)
             16 STORE_NAME               1 (json)

  3          18 LOAD_CONST               0 (0)
             20 LOAD_CONST               2 (('List', 'Dict', 'Any'))
             22 IMPORT_NAME              2 (typing)
             24 IMPORT_FROM              3 (List)
             26 STORE_NAME               3 (List)
             28 IMPORT_FROM              4 (Dict)
             30 STORE_NAME               4 (Dict)
             32 IMPORT_FROM              5 (Any)
             34 STORE_NAME               5 (Any)
             36 POP_TOP

  5          38 LOAD_CONST               3 ('chat_history.db')
             40 STORE_NAME               6 (DB_PATH)

  7          42 LOAD_CONST               4 (<code object init_db at 0x0000021F4E95FC50, file "nova_db.py", line 7>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               7 (init_db)

 22          48 LOAD_CONST              16 ((None, None))
             50 LOAD_CONST               5 ('role')
             52 LOAD_NAME                8 (str)
             54 LOAD_CONST               6 ('content')
             56 LOAD_NAME                8 (str)
             58 LOAD_CONST               7 ('message_id')
             60 LOAD_NAME                8 (str)
             62 LOAD_CONST               8 ('timestamp')
             64 LOAD_NAME                9 (float)
             66 LOAD_CONST               9 ('return')
             68 LOAD_CONST               1 (None)
             70 BUILD_TUPLE             10
             72 LOAD_CONST              10 (<code object store_message at 0x0000021F4CAB4950, file "nova_db.py", line 22>)
             74 MAKE_FUNCTION            5 (defaults, annotations)
             76 STORE_NAME              10 (store_message)

 42          78 LOAD_CONST              17 ((100,))
             80 LOAD_CONST              12 ('limit')
             82 LOAD_NAME               11 (int)
             84 LOAD_CONST               9 ('return')
             86 LOAD_NAME                3 (List)
             88 LOAD_NAME                4 (Dict)
             90 LOAD_NAME                8 (str)
             92 LOAD_NAME                5 (Any)
             94 BUILD_TUPLE              2
             96 BINARY_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 BINARY_SUBSCR
            110 CACHE
            112 CACHE
            114 CACHE
            116 BUILD_TUPLE              4
            118 LOAD_CONST              13 (<code object get_chat_history at 0x0000021F4CB4C4F0, file "nova_db.py", line 42>)
            120 MAKE_FUNCTION            5 (defaults, annotations)
            122 STORE_NAME              12 (get_chat_history)

 66         124 LOAD_CONST              18 ((4,))
            126 LOAD_CONST              15 (<code object get_last_n_messages at 0x0000021F4E9A8D50, file "nova_db.py", line 66>)
            128 MAKE_FUNCTION            1 (defaults)
            130 STORE_NAME              13 (get_last_n_messages)
            132 LOAD_CONST               1 (None)
            134 RETURN_VALUE

Disassembly of <code object init_db at 0x0000021F4E95FC50, file "nova_db.py", line 7>:
  7           0 RESUME                   0

  9           2 LOAD_GLOBAL              1 (NULL + sqlite3)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sqlite3)
             34 CACHE
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE
             50 STORE_FAST               0 (conn)

 10          52 LOAD_FAST                0 (conn)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 STORE_FAST               1 (cursor)

 11          92 LOAD_FAST                1 (cursor)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_CONST               1 ('\n        CREATE TABLE IF NOT EXISTS messages (\n            id TEXT PRIMARY KEY,\n            role TEXT NOT NULL,\n            content TEXT NOT NULL,\n            timestamp REAL NOT NULL\n        )\n    ')
            118 UNPACK_SEQUENCE          1
            122 CALL                     1
            130 CACHE
            132 POP_TOP

 19         134 LOAD_FAST                0 (conn)
            136 STORE_SUBSCR
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 UNPACK_SEQUENCE          0
            162 CALL                     0
            170 CACHE
            172 POP_TOP

 20         174 LOAD_FAST                0 (conn)
            176 STORE_SUBSCR
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 UNPACK_SEQUENCE          0
            202 CALL                     0
            210 CACHE
            212 POP_TOP
            214 LOAD_CONST               2 (None)
            216 RETURN_VALUE

Disassembly of <code object store_message at 0x0000021F4CAB4950, file "nova_db.py", line 22>:
 22           0 RESUME                   0

 24           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               2 (None)
              6 IMPORT_NAME              0 (time)
              8 STORE_FAST               4 (time)

 25          10 LOAD_CONST               1 (0)
             12 LOAD_CONST               2 (None)
             14 IMPORT_NAME              1 (uuid)
             16 STORE_FAST               5 (uuid)

 27          18 LOAD_FAST                2 (message_id)
             20 POP_JUMP_IF_TRUE        33 (to 88)

 28          22 LOAD_GLOBAL              5 (NULL + str)
             32 CACHE
             34 LOAD_FAST                5 (uuid)
             36 STORE_SUBSCR
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 UNPACK_SEQUENCE          0
             62 CALL                     0
             70 CACHE
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 STORE_FAST               2 (message_id)

 30     >>   88 LOAD_FAST                3 (timestamp)
             90 POP_JUMP_IF_TRUE        20 (to 132)

 31          92 LOAD_FAST                4 (time)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 UNPACK_SEQUENCE          0
            120 CALL                     0
            128 CACHE
            130 STORE_FAST               3 (timestamp)

 33     >>  132 LOAD_GLOBAL              9 (NULL + sqlite3)
            142 CACHE
            144 LOAD_ATTR                5 (NULL|self + str)
            164 CACHE
            166 UNPACK_SEQUENCE          1
            170 CALL                     1
            178 CACHE
            180 STORE_FAST               6 (conn)

 34         182 LOAD_FAST                6 (conn)
            184 STORE_SUBSCR
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 UNPACK_SEQUENCE          0
            210 CALL                     0
            218 CACHE
            220 STORE_FAST               7 (cursor)

 35         222 LOAD_FAST                7 (cursor)
            224 STORE_SUBSCR
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 LOAD_CONST               3 ('\n        INSERT OR REPLACE INTO messages (id, role, content, timestamp)\n        VALUES (?, ?, ?, ?)\n    ')

 38         248 LOAD_FAST                2 (message_id)
            250 LOAD_FAST                0 (role)
            252 LOAD_FAST                1 (content)
            254 LOAD_FAST                3 (timestamp)
            256 BUILD_TUPLE              4

 35         258 UNPACK_SEQUENCE          2
            262 CALL                     2
            270 CACHE
            272 POP_TOP

 39         274 LOAD_FAST                6 (conn)
            276 STORE_SUBSCR
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 UNPACK_SEQUENCE          0
            302 CALL                     0
            310 CACHE
            312 POP_TOP

 40         314 LOAD_FAST                6 (conn)
            316 STORE_SUBSCR
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 UNPACK_SEQUENCE          0
            342 CALL                     0
            350 CACHE
            352 POP_TOP
            354 LOAD_CONST               2 (None)
            356 RETURN_VALUE

Disassembly of <code object get_chat_history at 0x0000021F4CB4C4F0, file "nova_db.py", line 42>:
 42           0 RESUME                   0

 44           2 LOAD_GLOBAL              1 (NULL + sqlite3)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sqlite3)
             34 CACHE
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE
             50 STORE_FAST               1 (conn)

 45          52 LOAD_FAST                1 (conn)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 STORE_FAST               2 (cursor)

 46          92 LOAD_FAST                2 (cursor)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_CONST               1 ('\n        SELECT id, role, content, timestamp\n        FROM messages\n        ORDER BY timestamp ASC\n        LIMIT ?\n    ')

 51         118 LOAD_FAST                0 (limit)
            120 BUILD_TUPLE              1

 46         122 UNPACK_SEQUENCE          2
            126 CALL                     2
            134 CACHE
            136 POP_TOP

 53         138 BUILD_LIST               0
            140 STORE_FAST               3 (messages)

 54         142 LOAD_FAST                2 (cursor)
            144 STORE_SUBSCR
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 UNPACK_SEQUENCE          0
            170 CALL                     0
            178 CACHE
            180 GET_ITER
        >>  182 FOR_ITER                52 (to 290)

 55         186 LOAD_FAST                3 (messages)
            188 STORE_SUBSCR
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE

 56         210 LOAD_FAST                4 (row)
            212 LOAD_CONST               2 (0)
            214 BINARY_SUBSCR
            218 CACHE
            220 CACHE
            222 CACHE

 57         224 LOAD_FAST                4 (row)
            226 LOAD_CONST               3 (1)
            228 BINARY_SUBSCR
            232 CACHE
            234 CACHE
            236 CACHE

 58         238 LOAD_FAST                4 (row)
            240 LOAD_CONST               4 (2)
            242 BINARY_SUBSCR
            246 CACHE
            248 CACHE
            250 CACHE

 59         252 LOAD_FAST                4 (row)
            254 LOAD_CONST               5 (3)
            256 BINARY_SUBSCR
            260 CACHE
            262 CACHE
            264 CACHE

 55         266 LOAD_CONST               6 (('id', 'role', 'content', 'timestamp'))
            268 BUILD_CONST_KEY_MAP      4
            270 UNPACK_SEQUENCE          1
            274 CALL                     1
            282 CACHE
            284 POP_TOP
            286 JUMP_BACKWARD           53 (to 182)

 62         288 LOAD_FAST                1 (conn)
        >>  290 STORE_SUBSCR
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 UNPACK_SEQUENCE          0
            316 CALL                     0
            324 CACHE
            326 POP_TOP

 64         328 LOAD_FAST                3 (messages)
            330 RETURN_VALUE

Disassembly of <code object get_last_n_messages at 0x0000021F4E9A8D50, file "nova_db.py", line 66>:
 66           0 RESUME                   0

 67           2 LOAD_GLOBAL              1 (NULL + sqlite3)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sqlite3)
             34 CACHE
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE
             50 STORE_FAST               1 (conn)

 68          52 LOAD_FAST                1 (conn)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 STORE_FAST               2 (cursor)

 69          92 LOAD_FAST                2 (cursor)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE

 70         116 LOAD_CONST               1 ('SELECT role, content, timestamp FROM messages ORDER BY id DESC LIMIT ?')

 71         118 LOAD_FAST                0 (n)
            120 BUILD_TUPLE              1

 69         122 UNPACK_SEQUENCE          2
            126 CALL                     2
            134 CACHE
            136 POP_TOP

 73         138 LOAD_FAST                2 (cursor)
            140 STORE_SUBSCR
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 UNPACK_SEQUENCE          0
            166 CALL                     0
            174 CACHE
            176 STORE_FAST               3 (messages)

 74         178 LOAD_FAST                1 (conn)
            180 STORE_SUBSCR
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 UNPACK_SEQUENCE          0
            206 CALL                     0
            214 CACHE
            216 POP_TOP

 75         218 LOAD_GLOBAL             15 (NULL + list)
            228 CACHE
            230 LOAD_GLOBAL             17 (NULL + reversed)
            240 CACHE
            242 LOAD_FAST                3 (messages)
            244 UNPACK_SEQUENCE          1
            248 CALL                     1
            256 CACHE
            258 UNPACK_SEQUENCE          1
            262 CALL                     1
            270 CACHE
            272 RETURN_VALUE
