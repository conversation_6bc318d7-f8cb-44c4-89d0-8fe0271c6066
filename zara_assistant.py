#!/usr/bin/env python3
"""
<PERSON><PERSON> Voice Assistant
Advanced AI Voice Assistant with LiveKit Integration

Creator: Ratnam Sanjay
Version: 2.0.0
Based on Nova architecture with enhancements
"""

import asyncio
import livekit.rtc as rtc
import livekit.agents.utils.images as images
from livekit.rtc import VideoBufferType
from typing import Any, List, Optional
from dotenv import load_dotenv
from datetime import datetime
import livekit.agents as agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
import livekit.plugins.noise_cancellation as noise_cancellation
import livekit.plugins.google as google
from livekit.agents.llm.chat_context import ChatContext
import numpy as np
import cv2
from collections import deque
import json
import sqlite3
import uuid
import time
import logging
import logging.handlers
import os

# Import Nova tools and prompts
from prompts import AGENT_INSTRUCTION, SESSION_INSTRUCTION, AGENT_INSTRUCTION_FOR_TOOLS
import tools
from tools import (
    get_weather, search_web, play_media, get_time_info,
    system_power_action, manage_window, desktop_control,
    send_whatsapp_message, write_in_notepad, open_app,
    press_key, get_system_info, type_user_message_auto,
    scan_system_for_viruses, analyze_visual_scene
)

# Load environment variables
load_dotenv()

# Configure logging with rotation
log_handler = logging.handlers.RotatingFileHandler(
    'zara_assistant.log', maxBytes=10*1024*1024, backupCount=5
)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[log_handler, logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Database configuration
DB_PATH = "zara_chat_history.db"

class ZaraDatabase:
    """Database manager for Zara conversations"""
    
    @staticmethod
    def init_db():
        """Initialize the database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                id TEXT PRIMARY KEY,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                timestamp REAL NOT NULL
            )
        """)
        conn.commit()
        conn.close()
    
    @staticmethod
    def store_message(role: str, content: str, message_id: str = None, timestamp: float = None):
        """Store a message in the database"""
        if timestamp is None:
            timestamp = time.time()
        if message_id is None:
            message_id = str(uuid.uuid4())
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT OR REPLACE INTO messages (id, role, content, timestamp)
            VALUES (?, ?, ?, ?)
        """, (message_id, role, content, timestamp))
        conn.commit()
        conn.close()
    
    @staticmethod
    def get_last_n_messages(n: int = 4):
        """Get last n messages from database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT role, content, timestamp FROM messages ORDER BY id DESC LIMIT ?", (n,))
        messages = cursor.fetchall()
        conn.close()
        return list(reversed(messages))

class ZaraAssistant(Agent):
    """
    Zara Voice Assistant - Advanced AI Assistant with LiveKit
    
    Features:
    - Real-time voice interaction via LiveKit
    - Visual analysis with camera feed
    - Multilingual support (Hindi/English)
    - 30+ automation tools
    - Conversation logging
    - System control capabilities
    
    Creator: Ratnam Sanjay
    """
    
    def __init__(self):
        """Initialize Zara Assistant"""
        # Initialize with instructions from prompts
        super().__init__(instructions=AGENT_INSTRUCTION)

        # Assistant identity
        self.name = "Zara"
        self.creator = "Ratnam Sanjay"
        self.version = "2.0.0"
        
        # Initialize database
        ZaraDatabase.init_db()
        
        # Visual analysis components
        self._visual_analysis_enabled = False
        self._frame_buffer = deque(maxlen=20)
        self._min_frame_interval = 0.5
        self._last_frame_time = 0
        
        # Chat logging
        self._chat_log_path = "zara_conversations.txt"
        
        # Initialize tools
        self._tools = {}
        self._initialize_tools()
        
        logger.info(f"🤖 {self.name} v{self.version} initialized by {self.creator}")
        logger.info(f"🛠️ Tools loaded: {len(self._tools)}")
        logger.info(f"💾 Database: {DB_PATH}")
    
    def _initialize_tools(self):
        """Initialize all available tools"""
        tool_functions = [
            get_weather, search_web, play_media, get_time_info,
            system_power_action, manage_window, desktop_control,
            send_whatsapp_message, write_in_notepad, open_app,
            press_key, get_system_info, type_user_message_auto,
            scan_system_for_viruses, analyze_visual_scene
        ]
        
        for tool_func in tool_functions:
            if hasattr(tool_func, '__name__'):
                self._tools[tool_func.__name__] = tool_func
                logger.info(f"✅ Tool loaded: {tool_func.__name__}")
        
        logger.info(f"📋 Total tools initialized: {len(self._tools)}")
    
    async def enable_visual_analysis(self, enable: bool = True):
        """Toggle visual analysis with proper frame refresh"""
        self._visual_analysis_enabled = enable
        if enable:
            self._frame_buffer.clear()
            logger.info("कैमरा विश्लेषण सक्रिय किया गया")
        else:
            logger.info("कैमरा विश्लेषण निष्क्रिय किया गया")
    
    async def process_visual_frame(self, frame: rtc.VideoFrame):
        """Process incoming video frames with throttling"""
        if not self._visual_analysis_enabled:
            return
        
        current_time = time.time()
        if current_time - self._last_frame_time < self._min_frame_interval:
            return
        
        try:
            # Convert frame to numpy array
            frame_data = images.frame_to_image(frame)
            if frame_data is not None:
                self._frame_buffer.append({
                    'frame': frame_data,
                    'timestamp': current_time
                })
                self._last_frame_time = current_time
        except Exception as e:
            logger.error(f"Frame processing error: {e}")
    
    async def analyze_current_scene(self, prompt: str = "Describe what you see") -> str:
        """Analyze current visual scene"""
        if not self._visual_analysis_enabled:
            return "कृपया पहले कैमरा सक्रिय करें"
        
        if not self._frame_buffer:
            return "कोई ताजा फ्रेम उपलब्ध नहीं"
        
        try:
            # Get the most recent frame
            latest_frame = self._frame_buffer[-1]['frame']
            
            # Use the visual analysis tool
            result = await analyze_visual_scene(prompt)
            return result
        except Exception as e:
            logger.error(f"Visual analysis error: {e}")
            return f"विश्लेषण में त्रुटि हुई: {str(e)}"
    
    def _log_conversation(self, sender: str, message: str):
        """Log conversation to file with proper encoding and error handling."""
        try:
            # Store in database
            ZaraDatabase.store_message(sender, message)
            
            # Log to text file
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            with open(self._chat_log_path, 'a', encoding='utf-8') as f:
                f.write(f'[{timestamp}] {sender}: {message}\n')
                
        except IOError as e:
            logger.error(f"⚠️ Failed to log conversation: {str(e)}")
    
    async def on_tool_call_start(self, tool_call):
        """Handle tool call initiation."""
        logger.info(f"🛠️ Tool call started: {tool_call.name}")
        self._log_conversation("System", f"Tool called: {tool_call.name}")

    async def on_tool_call_end(self, tool_call, result):
        """Handle tool call completion."""
        logger.info(f"✅ Tool call completed: {tool_call.name}")
        self._log_conversation("System", f"Tool result: {str(result)[:100]}...")

    async def on_user_turn_completed(self, turn_ctx, new_message):
        """Handle post-processing after user turn completion."""
        try:
            # Extract user message
            user_message = "[no user input]"
            assistant_message = "[no assistant reply]"

            if hasattr(new_message, 'text_content'):
                user_message = new_message.text_content

            # Log the conversation
            print(f"\n🗣️ USER: {user_message}")
            print(f"🤖 ASSISTANT: {assistant_message}")

            self._log_conversation("User", user_message)
            self._log_conversation("Assistant", assistant_message)

            # Update tool usage metadata
            if hasattr(self, '_last_tool_used') and self._last_tool_used:
                tool = self._tools.get(self._last_tool_used)
                if tool:
                    metadata = getattr(tool, 'metadata', {})
                    metadata['last_used'] = datetime.now()
                    metadata['usage_count'] = metadata.get('usage_count', 0) + 1

            await super().on_user_turn_completed(turn_ctx, new_message)

        except Exception as e:
            logger.error(f"Error in user turn completion: {e}")

    def _check_reminders(self):
        """Check and announce any reminders."""
        try:
            # This would integrate with the reminder system from tools
            # For now, just a placeholder
            pass
        except Exception as e:
            logger.error(f"Error checking reminders: {e}")

    async def generate_startup_message(self, session: AgentSession) -> str:
        """
        Dynamically constructs startup instructions by combining the base SESSION_INSTRUCTION
        with a summary of the last conversation, then generates a context-aware greeting.
        """
        try:
            # Get last messages from database
            last_messages = ZaraDatabase.get_last_n_messages(n=4)

            if last_messages:
                logger.info("✅ Found recent messages. Adding conversational context to startup instructions.")

                # Build conversation summary
                summary_lines = []
                for msg in last_messages[1:]:  # Skip first message
                    content = msg[1]  # content is at index 1
                    summary_lines.append(f"- {msg[0]}: {content}")

                last_convo_summary = '\n'.join(summary_lines)

                final_instructions = SESSION_INSTRUCTION + f"""

🔰 विशेष संदर्भ:
यह हमारी पिछली बातचीत का अंतिम अंश है:
{last_convo_summary}

--------------------------"""
            else:
                logger.info("✅ No message history found. Using standard startup instructions.")
                final_instructions = SESSION_INSTRUCTION

            # Generate contextual greeting
            return "नमस्कार सर, मैं ज़ारा आपकी सेवा में प्रस्तुत हूँ। आज का कार्य प्रारंभ करें?"

        except Exception as e:
            logger.error(f"⚠️ Failed to generate startup message: {str(e)}")
            return "नमस्कार सर, मैं ज़ारा आपकी सेवा में प्रस्तुत हूँ।"

async def entrypoint(ctx: agents.JobContext):
    """Main entrypoint for Zara Assistant"""
    logger.info("🚀 Starting Zara Voice Assistant...")

    # Create assistant instance
    assistant = ZaraAssistant()

    # Connect to room
    await ctx.connect(auto_subscribe=agents.AutoSubscribe.AUDIO_ONLY)

    # Start agent session with the correct API
    session = AgentSession(
        ctx=ctx,
        agent=assistant
    )

    # Generate and send startup message
    startup_msg = await assistant.generate_startup_message(session)
    logger.info(f"Startup message: {startup_msg}")

    # Log session start
    assistant._log_conversation("System", "Session started")

    logger.info("✅ Agent session started successfully and is now live.")

    # Start the session
    await session.start()

# Simple CLI test function
async def test_zara_cli():
    """Simple CLI test for Zara Assistant without LiveKit"""
    print("🤖 Zara Voice Assistant - CLI Test Mode")
    print("Creator: Ratnam Sanjay")
    print("-" * 50)

    # Create assistant instance
    assistant = ZaraAssistant()

    # Generate startup message
    startup_msg = await assistant.generate_startup_message(None)
    print(f"\n{startup_msg}")

    print("\nCommands: 'quit' to exit, 'tools' to list tools, 'history' to show conversation history")
    print("-" * 80)

    while True:
        try:
            user_input = input("\n👤 You: ").strip()

            if user_input.lower() in ['quit', 'exit', 'bye']:
                print(f"\n🤖 Zara: अलविदा! आपका दिन शुभ हो!")
                break

            if user_input.lower() == 'tools':
                print(f"\n🛠️ Available Tools ({len(assistant._tools)}):")
                for i, tool_name in enumerate(assistant._tools.keys(), 1):
                    print(f"   {i:2d}. {tool_name}")
                continue

            if user_input.lower() == 'history':
                messages = ZaraDatabase.get_last_n_messages(10)
                print(f"\n📜 Recent Conversation History:")
                for msg in messages:
                    timestamp = datetime.fromtimestamp(msg[2]).strftime('%H:%M:%S')
                    print(f"   [{timestamp}] {msg[0]}: {msg[1][:50]}...")
                continue

            if not user_input:
                continue

            # Log user input
            assistant._log_conversation("User", user_input)

            # Simple response (without full LLM integration)
            response = f"मैंने समझा कि आपने कहा: '{user_input}'. मैं अभी भी सीख रही हूँ।"

            # Log assistant response
            assistant._log_conversation("Assistant", response)

            print(f"\n🤖 Zara: {response}")

        except KeyboardInterrupt:
            print(f"\n\n🤖 Zara: अलविदा!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "cli":
        # Run CLI test mode
        asyncio.run(test_zara_cli())
    else:
        # Run LiveKit mode
        try:
            from livekit.agents import cli, WorkerOptions
            cli.run_app(
                WorkerOptions(
                    entrypoint_fnc=entrypoint,
                )
            )
        except ImportError:
            print("⚠️ LiveKit not available. Running CLI test mode...")
            asyncio.run(test_zara_cli())
