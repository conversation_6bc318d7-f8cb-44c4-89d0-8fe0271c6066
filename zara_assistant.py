import asyncio
from livekit import rtc
from livekit.agents.utils import images
import asyncio
from livekit.rtc import VideoBufferType
from typing import Any, List, Optional
from dotenv import load_dotenv
from datetime import datetime
from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.plugins import noise_cancellation
from livekit.plugins import google
from livekit.agents.llm.chat_context import ChatContext
import numpy as np
import cv2
from collections import deque
import json

# Import prompts and tools
from prompts import (
    AGENT_INSTRUCTION,
    SESSION_INSTRUCTION,
    AGENT_INSTRUCTION_FOR_TOOLS
)

from tools import (
    get_weather,
    search_web,
    play_media,
    get_time_info,
    system_power_action,
    manage_window,
    desktop_control,
    send_whatsapp_message,
    write_in_notepad,
    open_app,
    press_key,
    get_system_info,
    type_user_message_auto,
    scan_system_for_viruses,
    analyze_visual_scene
)

try:
    from livekit.plugins.google.beta.realtime.custom_plugins import EffectPlugin
except ImportError:
    pass

import time
import logging
from logging.handlers import RotatingFileHandler  # For log rotation

load_dotenv()

class ZaraAssistant(Agent):
    def __init__(self) -> None:
        # Initialize tools with validation
        import tools
        tools.assistant_instance = self
        self._tools = self._initialize_tools([
            get_weather,
            search_web,
            play_media,
            get_time_info,
            system_power_action,
            manage_window,
            desktop_control,
            send_whatsapp_message,
            write_in_notepad,
            open_app,
            press_key,
            get_system_info,
            type_user_message_auto,
            scan_system_for_viruses,
            analyze_visual_scene
        ])

        super().__init__(
            instructions=self._build_instructions(),
            llm=google.beta.realtime.RealtimeModel(
                voice="Charon",
                temperature=0.5,
                top_p=0.9,
            ),
            tools=self._tools,
        )
        
        # State tracking
        self._last_tool_used: Optional[str] = None
        self._last_tool_success: bool = False
        self._chat_log_path = "zara_chat_log.txt"
        try:
            EffectPlugin.register_effect_callback(self._trigger_gui_effect)
        except:
            pass  # EffectPlugin might not be available
        self._min_frame_interval: float = 0.5  # Process one frame every 0.5s (2 FPS max)
        self._max_buffer_size: int = 20        # Store a max of 20 frames (~10 seconds of context)
        self._max_frames_to_send: int = 3      # Send a maximum of 3 frames to the API
        
        # Visual Analysis Runtime State
        self._frame_buffer = deque(maxlen=self._max_buffer_size) # Use deque for efficiency
        self._last_frame_time: float = 0
        self._visual_analysis_enabled: bool = False
        self._is_analyzing: bool = False
        self._analysis_lock = asyncio.Lock()

    async def enable_visual_analysis(self, enable: bool):
        """Toggle visual analysis with proper frame refresh"""
        self._visual_analysis_enabled = enable
        if enable:
            self._frame_buffer.clear()
            self._last_frame_time = 0
            return "कैमरा विश्लेषण सक्रिय किया गया"
        else:
            self._frame_buffer.clear()
            if hasattr(self.llm, "session") and self.llm.session():
                self.llm.session().clear_video()
            return "कैमरा विश्लेषण निष्क्रिय किया गया"  # Camera deactivated in Hindi

    async def process_visual_frame(self, frame: rtc.VideoFrame):
        """
        Process incoming frames with throttling and efficient buffering.
        This function is called frequently by the video stream.
        """
        if not self._visual_analysis_enabled:
            return
            
        current_time = time.time()
        # Enforce a minimum interval between processed frames to reduce load
        if current_time - self._last_frame_time < self._min_frame_interval:
            return
            
        try:
            if frame.type != VideoBufferType.RGBA:
                frame = frame.convert(VideoBufferType.RGBA)
                
            # Add timestamp directly to the frame object for later retrieval
            frame.timestamp = current_time
            self._frame_buffer.append(frame) # deque handles maxlen automatically
            self._last_frame_time = current_time
            
        except Exception as e:
            # Use the agent's logger if available, otherwise print
            logger = getattr(self, 'logger', logging)
            logger.error(f"Frame processing error: {str(e)}")
            self._frame_buffer.clear()

    async def analyze_current_scene(self, prompt: str) -> str:
        """
        Analyzes a limited, representative sample of frames to avoid API quota issues.
        """
        if not self._visual_analysis_enabled:
            return "कृपया पहले कैमरा सक्रिय करें"
            
        if not self._frame_buffer:
            return "कोई ताजा फ्रेम उपलब्ध नहीं"

        async with self._analysis_lock:
            try:
                self._is_analyzing = True
                
                # --- KEY CHANGE: Smart Frame Selection ---
                # Instead of sending all recent frames, we select a small,
                # representative sample from the buffer.
                frames_to_send = []
                buffer_len = len(self._frame_buffer)
                
                if buffer_len > 0:
                    # Create a set of indices to pick: first, middle, and last.
                    # Using a set automatically handles duplicates if buffer is small.
                    indices_to_pick = {0, buffer_len // 2, buffer_len - 1}
                    
                    # Select the frames from the buffer using the chosen indices
                    selected_frames = [self._frame_buffer[i] for i in sorted(list(indices_to_pick))]
                    
                    # Ensure we don't exceed the configured max number of frames
                    frames_to_send = selected_frames[:self._max_frames_to_send]

                if not frames_to_send:
                    return "विश्लेषण के लिए कोई फ्रेम नहीं चुना गया"
                    
                session = self.llm.session()
                if not session:
                    return "सत्र उपलब्ध नहीं"
                
                # Clear any previous video context and push the new, limited set
                session.clear_video()
                for frame in frames_to_send:
                    session.push_video(frame)
                
                enhanced_prompt = (
                    f"{prompt}\n"
                    f"संदर्भ: {len(frames_to_send)} प्रतिनिधि फ्रेम्स का विश्लेषण करें।\n"
                    "कृपया हिंदी में विस्तृत विश्लेषण दें"
                )
                
                response = await session.generate_reply(
                    instructions=enhanced_prompt,
                    timeout=20.0
                )
                return response.text_content
                
            except asyncio.TimeoutError:
                return "विश्लेषण समय समाप्त - कृपया पुनः प्रयास करें"
            except Exception as e:
                logger = getattr(self, 'logger', logging)
                logger.error(f"Analysis error: {str(e)}")
                return "विश्लेषण में त्रुटि हुई"
            finally:
                self._is_analyzing = False

    def _trigger_gui_effect(self):
        # This will be called by the plugin system
        pass  # Implementation in zara_gui.py

    def _initialize_tools(self, tools: List[Any]) -> List[Any]:
        """Validate and initialize tools with proper error handling."""
        validated_tools = []
        for tool in tools:
            try:
                if not callable(tool):
                    raise ValueError(f"Tool {getattr(tool, '__name__', str(tool))} is not callable")
                if not tool.__doc__:
                    print(f"⚠️ Warning: Tool {tool.__name__} missing docstring")
                
                # Add tool description metadata
                tool.metadata = {
                    'description': tool.__doc__.strip() if tool.__doc__ else f"Tool: {tool.__name__}",
                    'last_used': None,
                    'usage_count': 0
                }
                validated_tools.append(tool)
                print(f"✅ Initialized tool: {tool.__name__}")
            except Exception as e:
                print(f"⚠️ Failed to initialize tool {getattr(tool, '__name__', str(tool))}: {str(e)}")
        
        print(f"📋 Total tools initialized: {len(validated_tools)}")
        return validated_tools

    def _build_instructions(self) -> str:
        """Construct the complete instruction set for the agent."""
        tool_descriptions = []
        for tool in self._tools:
            tool_name = tool.__name__
            tool_doc = tool.__doc__ if tool.__doc__ else "No description available"
            tool_descriptions.append(f"- {tool_name}: {tool_doc.strip()}")
        
        available_tools = "\n".join(tool_descriptions)
        
        return "\n".join([
            AGENT_INSTRUCTION,
            SESSION_INSTRUCTION,
            AGENT_INSTRUCTION_FOR_TOOLS,
            "You are a multilingual assistant capable of understanding and responding in multiple languages.",
            f"\nAvailable tools:\n{available_tools}",
            "\nWhen a user request requires tool usage, actively use the appropriate tool and provide feedback about the action taken."
        ])

    async def on_tool_call_start(self, tool_call):
        """Handle tool call start event."""
        print(f"🔧 Starting tool call: {tool_call.function_info.name}")
        self._last_tool_used = tool_call.function_info.name
        return await super().on_tool_call_start(tool_call)

    async def on_tool_call_end(self, tool_call, result):
        """Handle tool call completion."""
        success = result and not isinstance(result, Exception)
        self._last_tool_success = success

        print(f"🔧 Tool call completed: {tool_call.function_info.name} - {'✅ Success' if success else '❌ Failed'}")
        if not success and result:
            print(f"   Error: {result}")

        return await super().on_tool_call_end(tool_call, result)

    async def on_user_turn_completed(self, turn_ctx, new_message):
        """Handle post-processing after user turn completion."""
        user_message = turn_ctx.user_message.text_content if turn_ctx.user_message else "[no user input]"
        assistant_message = new_message.text_content if new_message else "[no assistant reply]"

        # Log conversation
        print(f"\n🗣️ USER: {user_message}")
        print(f"🤖 ASSISTANT: {assistant_message}")
        await self._log_conversation("User", user_message)
        await self._log_conversation("Assistant", assistant_message)

        # Update tool usage tracking
        if self._last_tool_used:
            for tool in self._tools:
                if tool.__name__ == self._last_tool_used:
                    tool.metadata['last_used'] = datetime.now()
                    tool.metadata['usage_count'] += 1
                    break

        return await super().on_user_turn_completed(turn_ctx, new_message)

    async def _log_conversation(self, sender: str, message: str) -> None:
        """Log conversation to file with proper encoding and error handling."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        try:
            with open(self._chat_log_path, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {sender}: {message}\n")
        except IOError as e:
            print(f"⚠️ Failed to log conversation: {str(e)}")

    async def _check_reminders(self) -> None:
        """Check and announce any reminders."""
        try:
            # Placeholder for reminder functionality
            pass
        except Exception as e:
            print(f"⚠️ Failed to check reminders: {str(e)}")

    async def generate_startup_message(self, session: AgentSession):
        """
        Dynamically constructs startup instructions by combining the base SESSION_INSTRUCTION
        with a summary of the last conversation, then generates a context-aware greeting.
        """
        try:
            # Start with the base instructions you've already written.
            final_instructions = SESSION_INSTRUCTION

            # Use the final, combined instructions to generate the spoken reply.
            await session.generate_reply(instructions=final_instructions)

        except Exception as e:
            print(f"⚠️ Failed to generate startup message: {e}")
            # Fallback to a simple greeting if there's an error.
            await session.generate_reply(instructions="नमस्कार सर, मैं ज़ारा आपकी सेवा में प्रस्तुत हूँ।")

async def entrypoint(ctx: agents.JobContext):
    """Main entry point for the agent with retry logic."""
    max_retries = 3
    retry_delay = 5

    for attempt in range(1, max_retries + 1):
        try:
            # Create agent first
            agent = ZaraAssistant()

            # Create session with the agent
            session = AgentSession()

            # Start session with proper configuration
            try:
                await asyncio.wait_for(
                    session.start(
                        room=ctx.room,
                        agent=agent,
                        room_input_options=RoomInputOptions(
                            video_enabled=True,
                            noise_cancellation=noise_cancellation.BVC(),
                        ),
                    ),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                raise Exception("Connection timeout")

            # Connect to the room
            await ctx.connect()

            # Generate startup message
            await agent.generate_startup_message(session)

            # Check for reminders
            await agent._check_reminders()

            print("✅ Agent session started successfully and is now live.")
            break

        except Exception as e:
            print(f"❌ Entrypoint failed on attempt {attempt}: {e}")
            if attempt < max_retries:
                await asyncio.sleep(retry_delay)
            else:
                raise  # Re-raise if all retries failed

if __name__ == "__main__":
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))
