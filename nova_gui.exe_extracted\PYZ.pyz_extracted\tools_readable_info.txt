Readable Information from nova_gui.exe_extracted\PYZ.pyz_extracted\tools.pyc
============================================================

HEADER ANALYSIS:
Magic bytes: a70d0d0a
File size: 178486 bytes

CODE STRUCTURE:
Code Object Analysis:
  Name: <module>
  Filename: tools.py
  First line: 1
  Arg count: 0
  Local count: 0
  Stack size: 33
  Flags: 0
  Names: ('logging', 'livekit.agents', 'function_tool', 'requests', 'langchain_community.tools', 'DuckDuckGoSearchRun', 'subprocess', 'ctypes', 'pygetwindow', 'gw', 'platform', 'time', 'os', 'webbrowser', 'typing', 'Optional', 'Literal', 'datetime', 'psutil', 'smtplib', 'email.mime.multipart', 'MIMEMultipart', 'email.mime.text', 'MIMEText', 'pyautogui', 're', 'dotenv', 'load_dotenv', 'json', 'wikipedia', 'List', 'Dict', 'asyncio', 'aiohttp', 'assistant_instance', 'basicConfig', 'INFO', 'getLogger', '__name__', 'logger', 'YOUTUBE_API_KEY', 'GMAIL_USER', 'GMAIL_PASSWORD', 'PAUSE', 'FAILSAFE', 'str', 'bool', 'validate_email', 'get_weather', 'system_power_action', 'manage_window', 'get_time_info', 'search_web', 'play_media', 'int', 'desktop_control', 'send_email', 'list_active_windows', 'manage_window_state', 'say_reminder', 'date', 'sqlite3', 'DB_PATH', 'TABLE_NAME', 'get_today_reminder_message_from_db', 'extract_date_from_text', 'Tuple', 'send_whatsapp_message', 'write_in_notepad', 'open_app', 'press_key', 'get_system_info', 'type_user_message_auto', 'numpy', 'np', 'cv2', 'mss', 'pytesseract', 'win32gui', 'PIL', 'Image', 'functools', 'wraps', 'tesseract_cmd', 'environ', 'sct', 'monitors', 'monitor', 'click_on_text', 'scan_system_for_viruses', 'enable_camera_analysis', 'analyze_visual_scene', 'pandas', 'pd', 'matplotlib.pyplot', 'pyplot', 'plt', 'seaborn', 'sns', 'warnings', 'Any', 'pathlib', 'Path', 'scipy', 'stats', 'sklearn.preprocessing', 'StandardScaler', 'sklearn.cluster', 'KMeans', 'sklearn.decomposition', 'PCA', 'tkinter', 'tk', 'filedialog', 'concurrent.futures', 'ThreadPoolExecutor', 'ProcessPoolExecutor', 'io', 'base64', 'multiprocessing', 'mp', 'partial', 'filterwarnings', 'set_option', 'GLOBAL_DF', 'ANALYSIS_CACHE', '_open_file_dialog', 'DataFrame', '_load_data_file', '_detect_column_types_fast', '_detect_business_context_fast', '_analyze_data_quality_fast', '_find_key_insights_fast', '_generate_business_insights_fast', '_create_visualizations_fast', '_generate_html_report_fast', 'reset_analysis', 'load_and_analyze_excel', 'get_analysis_report', 'get_analysis_status', 'create_visualizations_chart', 'get_top_insights', 'get_data_summary', 'export_results', 'full_analysis_with_report', 'messagebox', 'simpledialog', 'sys', 'matplotlib.patches', 'patches', 'mpatches', 'matplotlib', 'use', '_create_file_dialog', '_create_input_dialog', '_load_data_smart', '_analyze_data', '_create_advanced_styling', '_add_statistics', '_create_graph_advanced', 'tuple', 'create_advanced_graph', 'create_quick_advanced_graph', 'nmap', 'socket', 'mac_vendor_lookup', 'MacLookup', 'Listbox', 'Scrollbar', 'Frame', 'Label', 'Button', 'SINGLE', 'END', 'ttk', 'threading', 'concurrent', 'ipaddress', 'queue', '_mac_cache', '_cache_file', 'Queue', '_ui_queue', '_load_mac_cache', '_save_mac_cache', '_get_mac_vendor_fast', '_discover_network_ultra_fast', '_scan_devices_parallel', '_interactive_scan_workflow', 'advanced_network_scan', 'run', 'result', 'print')
  Var names: ()
  Constants: [0, None, ('function_tool',), ('DuckDuckGoSearchRun',), ('Optional', 'Literal'), ('datetime',), ('MIMEMultipart',), ('MIMEText',), ('load_dotenv',), ('List', 'Dict'), '%(asctime)s - %(name)s - %(levelname)s - %(message...', ('level', 'format'), 'AIzaSyBW5YkF21ntit_zIMGE5TpTKlBpBJl3mfM', '<EMAIL>', 'zisn kydb qysb yifr', 0.1, True, 'email', 'return', '<code object validate_email at 0x000001EFF690E630,...', 'city', '<code object get_weather at 0x000001EFF4BF1930, fi...', 'action', ('shutdown', 'restart', 'lock'), '<code object system_power_action at 0x000001EFF4BF...', ('close', 'minimize', 'maximize'), '<code object manage_window at 0x000001EFF4AF8A40, ...', '<code object get_time_info at 0x000001EFF3F22D30, ...', 'query', '<code object search_web at 0x000001EFF4BF2C60, fil...', 'song', 'media_name', 'media_type', ('song', 'video'), '<code object play_media at 0x000001EFF4BF3860, fil...', 3, ('show', 'scroll'), 'direction', ('up', 'down'), 'amount', '<code object desktop_control at 0x000001EFF4B71650...', 'to_email', 'subject', 'message', 'cc_email', '<code object send_email at 0x000001EFF41E24D0, fil...', '<code object list_active_windows at 0x000001EFF4B6...', ('maximize', 'minimize', 'restore'), 'window_title', '<code object manage_window_state at 0x000001EFF4BB...', 'msg', '<code object say_reminder at 0x000001EFF690E730, f...', ('datetime', 'date'), 'nova_memory/chat_history.db', 'chat_messages', '<code object get_today_reminder_message_from_db at...', 'text', '<code object extract_date_from_text at 0x000001EFF...', ('Tuple',), 'contact', '<code object send_whatsapp_message at 0x000001EFF4...', 'letter', 'title', 'content', 'document_type', '<code object write_in_notepad at 0x000001EFF4BF7B4...', 'app_name', '<code object open_app at 0x000001EFF4BA7C20, file ...', 'key', '<code object press_key at 0x000001EFF4A49950, file...', '<code object get_system_info at 0x000001EFF4BFA0C0...', '<code object type_user_message_auto at 0x000001EFF...', ('mss',), ('Image',), ('wraps',), 'C:\\Program Files\\Tesseract-OCR\\tesseract.exe', '2', 'TF_CPP_MIN_LOG_LEVEL', 1, 'target_text', '<code object click_on_text at 0x000001EFF4A47C50, ...', '<code object scan_system_for_viruses at 0x000001EF...', 'enable', '<code object enable_camera_analysis at 0x000001EFF...', 'prompt', '<code object analyze_visual_scene at 0x000001EFF4B...', ('Dict', 'List', 'Any', 'Optional'), ('Path',), ('stats',), ('StandardScaler',), ('KMeans',), ('PCA',), ('filedialog',), ('ThreadPoolExecutor', 'ProcessPoolExecutor'), ('partial',), 'ignore', 'compute.use_bottleneck', 'compute.use_numexpr', "('data', 'insights', 'metadata', 'analysis_results...", '<code object _open_file_dialog at 0x000001EFF4A49B...', 'file_path', '<code object _load_data_file at 0x000001EFF4BF8900...', 'df', '<code object _detect_column_types_fast at 0x000001...', '<code object _detect_business_context_fast at 0x00...', '<code object _analyze_data_quality_fast at 0x00000...', 'business_context', '<code object _find_key_insights_fast at 0x000001EF...', 'insights', '<code object _generate_business_insights_fast at 0...', '<code object _create_visualizations_fast at 0x0000...', 'quality', '<code object _generate_html_report_fast at 0x00000...', '<code object reset_analysis at 0x000001EFF69C2090,...', '<code object load_and_analyze_excel at 0x000001EFF...', '<code object get_analysis_report at 0x000001EFF4BB...', '<code object get_analysis_status at 0x000001EFF416...', '<code object create_visualizations_chart at 0x0000...', '<code object get_top_insights at 0x000001EFF69FC85...', '<code object get_data_summary at 0x000001EFF4B5842...', 'json', 'format_type', '<code object export_results at 0x000001EFF4B586E0,...', '<code object full_analysis_with_report at 0x000001...', ('filedialog', 'messagebox', 'simpledialog'), 'Agg', '<code object _create_file_dialog at 0x000001EFF4B9...', 'string', '<code object _create_input_dialog at 0x000001EFF41...', '<code object _load_data_smart at 0x000001EFF4B58CB...', '<code object _analyze_data at 0x000001EFF4BFF790, ...', '<code object _create_advanced_styling at 0x000001E...', '<code object _add_statistics at 0x000001EFF4B59A20...', '<code object _create_graph_advanced at 0x000001EFF...', 'line', 'full', 'default', 'auto', 'png', 300, (12, 8), 'graph_type', 'x_column', 'y_column', 'data_range', 'data_limit', 'save_name', 'style', 'color_scheme', 'interactive_mode', 'show_stats', 'export_format', 'dpi', 'figsize', '<code object create_advanced_graph at 0x000001EFF4...', False, '<code object create_quick_advanced_graph at 0x0000...', ('MacLookup',), "('Listbox', 'Scrollbar', 'Frame', 'Label', 'Button...", ('List', 'Literal'), 'mac_vendor_cache.json', '<code object _load_mac_cache at 0x000001EFF3F3FBB0...', '<code object _save_mac_cache at 0x000001EFF3F23930...', 'mac_address', '<code object _get_mac_vendor_fast at 0x000001EFF69...', '<code object _discover_network_ultra_fast at 0x000...', 'target_network', '<code object _scan_devices_parallel at 0x000001EFF...', '<code object _interactive_scan_workflow at 0x00000...', '<code object advanced_network_scan at 0x000001EFF6...', '__main__', ('song',), (None, 3), (None,), ('letter',), ('json',), (None, 'string'), "(None, 'line', None, None, 'full', None, None, Non...", "('line', None, None, 'full', None, None, None, 'de..."]
  Nested code object 19:
    Code Object Analysis:
      Name: validate_email
      Filename: tools.py
      First line: 47
      Arg count: 1
      Local count: 2
      Stack size: 4
      Flags: 3
      Names: ('re', 'match')
      Var names: ('email', 'pattern')
      Constants: ['ईमेल एड्रेस को वैलिडेट करें', '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$', None]
  Nested code object 21:
    Code Object Analysis:
      Name: get_weather
      Filename: tools.py
      First line: 52
      Arg count: 1
      Local count: 11
      Stack size: 10
      Flags: 131
      Names: ('print', 'aiohttp', 'ClientSession', 'get', 'ClientTimeout', 'json', 'isinstance', 'list', 'Exception', 'logger', 'error')
      Var names: ('city', 'session', 'response', 'geo_data', 'location', 'weather_url', 'weather_data', 'current', 'location_name', 'result', 'e')
      Constants: ['\n\n    Fetches current weather conditions for a spe...', '🌤️ Getting weather for: ', None, 'https://geocoding-api.open-meteo.com/v1/search?nam...', 5, ('total',), ('timeout',), 'results', 'https://nominatim.openstreetmap.org/search?q=', '&format=json', 'क्षमा करें, मैं स्थान नहीं ढूंढ पाया: ', '.', 0, 'https://api.open-meteo.com/v1/forecast?latitude=', 'lat', 'latitude', '&longitude=', 'lon', 'longitude', '&current_weather=true', 'current_weather', 'display_name', 'name', ' का वर्तमान तापमान है ', 'temperature', '°C और पवन की गति है ', 'windspeed', ' km/h।', '✅ Weather result: ', 'मौसम की जानकारी प्राप्त करने में असमर्थ: ', 'मौसम त्रुटि: ', 'मौसम सेवा अस्थायी रूप से अनुपलब्ध है। कृपया बाद मे...']
  Nested code object 24:
    Code Object Analysis:
      Name: system_power_action
      Filename: tools.py
      First line: 120
      Arg count: 1
      Local count: 3
      Stack size: 7
      Flags: 131
      Names: ('print', 'platform', 'system', 'asyncio', 'create_task', 'to_thread', 'os', 'ctypes', 'windll', 'user32', 'LockWorkStation', 'subprocess', 'run', 'Exception', 'logger', 'error', 'str')
      Var names: ('action', 'system', 'e')
      Constants: ['\n    Controls system power state across Windows/Li...', '🔧 Power action: ', 'shutdown', 'Windows', 'shutdown /s /t 1', None, 'Linux', 'shutdown now', 'Darwin', 'sudo shutdown -h now', 'सिस्टम शटडाउन किया जा रहा है।', 'restart', 'shutdown /r /t 1', 'reboot', 'sudo shutdown -r now', 'सिस्टम रीस्टार्ट किया जा रहा है।', 'lock', 'loginctl', 'lock-session', True, ('check',), '/System/Library/CoreServices/Menu Extras/User.menu...', '-suspend', '🔒 स्क्रीन लॉक की गई है।', 'पावर एक्शन विफल: ', ' करने में समस्या आई: ']
  Nested code object 26:
    Code Object Analysis:
      Name: manage_window
      Filename: tools.py
      First line: 178
      Arg count: 1
      Local count: 4
      Stack size: 7
      Flags: 131
      Names: ('print', 'asyncio', 'create_task', 'to_thread', 'gw', 'getActiveWindow', 'title', 'strip', 'close', 'minimize', 'maximize', 'Exception', 'logger', 'error', 'str')
      Var names: ('action', 'active_win', 'title', 'e')
      Constants: ['\n    Manages the currently active application wind...', '🪟 Window action: ', None, '❌ कोई सक्रिय विंडो नहीं मिली।', 'अज्ञात विंडो', 'close', "✅ '", "' विंडो बंद कर दी गई।", 'minimize', "' विंडो छोटी की गई।", 'maximize', "' विंडो बड़ी की गई।", 'विंडो प्रबंधन विफल: ', '❌ विंडो ', ' करने में समस्या आई: ']
  Nested code object 27:
    Code Object Analysis:
      Name: get_time_info
      Filename: tools.py
      First line: 222
      Arg count: 0
      Local count: 2
      Stack size: 8
      Flags: 131
      Names: ('datetime', 'now', 'strftime')
      Var names: ('now', 'result')
      Constants: ['\n    Provides current datetime information in Hind...', 'आज की तारीख है ', '%d-%m-%Y', '। अभी का समय है ', '%I:%M %p', '। सप्ताह का दिन है ', '%A', '।']
  Nested code object 29:
    Code Object Analysis:
      Name: search_web
      Filename: tools.py
      First line: 246
      Arg count: 1
      Local count: 10
      Stack size: 8
      Flags: 131
      Names: ('print', 'asyncio', 'create_task', 'to_thread', 'wikipedia', 'summary', 'Exception', 'aiohttp', 'ClientSession', 'get', 'ClientTimeout', 'json', 'DuckDuckGoSearchRun', 'run', 'logger', 'error')
      Var names: ('query', 'summary', 'e', 'session', 'url', 'params', 'response', 'data', 'search_tool', 'results')
      Constants: ['\n    Performs multi-source web search with fallbac...', '🔍 Searching web for: ', 2, ('sentences',), None, '✅ Wikipedia result found', '📚 विकिपीडिया:\n', '⚠️ Wikipedia failed: ', 'https://api.duckduckgo.com/', 'json', '1', ('q', 'format', 'no_redirect', 'no_html'), 5, ('total',), ('params', 'timeout'), 'AbstractText', '✅ DuckDuckGo API result found', '🦆 DuckDuckGo:\n', 'RelatedTopics', '✅ DuckDuckGo related topics found', '🔍 संबंधित:\n', 0, 'Text', '⚠️ DuckDuckGo API failed: ', '✅ DuckDuckGo search tool result found', '🔎 परिणाम:\n', '⚠️ DuckDuckGo search tool failed: ', '❌ क्षमा करें, अभी कोई उपयोगी जानकारी नहीं मिली।', 'खोज त्रुटि: ', '❌ वेब खोज में त्रुटि: ']
  Nested code object 34:
    Code Object Analysis:
      Name: play_media
      Filename: tools.py
      First line: 308
      Arg count: 2
      Local count: 7
      Stack size: 8
      Flags: 131
      Names: ('print', 'YOUTUBE_API_KEY', 'asyncio', 'create_task', 'to_thread', 'webbrowser', 'open', 'aiohttp', 'ClientSession', 'get', 'ClientTimeout', 'json', 'Exception', 'logger', 'error', 'str')
      Var names: ('media_name', 'media_type', 'session', 'response', 'data', 'video', 'e')
      Constants: ['\n    Plays media content from YouTube.\n    \n    Ar...', '🎵 Playing media: ', ' (type: ', ')', 'https://www.youtube.com/results?search_query=', None, "YouTube पर '", "' खोल रहा हूँ...", 'https://www.googleapis.com/youtube/v3/search?part=...', '&type=video&key=', 10, ('total',), ('timeout',), 'items', 0, 'https://www.youtube.com/watch?v=', 'id', 'videoId', '🎵 अब बज रहा है: ', 'snippet', 'title', 'मीडिया त्रुटि: ', '❌ मीडिया चलाने में समस्या आई: ']
  Nested code object 40:
    Code Object Analysis:
      Name: desktop_control
      Filename: tools.py
      First line: 350
      Arg count: 3
      Local count: 8
      Stack size: 10
      Flags: 131
      Names: ('print', 'asyncio', 'create_task', 'to_thread', 'pyautogui', 'position', 'hotkey', 'moveTo', 'x', 'y', 'Exception', 'click', 'sleep', 'press', 'str', 'size', 'scroll')
      Var names: ('action', 'direction', 'amount', 'original_pos', 'e', 'screen_width', 'screen_height', 'scroll_amount')
      Constants: ['\n    Controls desktop UI elements.\n    \n    Args:\n...', '🖥️ Desktop control: ', None, 'show', 'win', 'd', 0.1, ('duration',), '🖥️ डेस्कटॉप दिखाया जा रहा है।', 'right', ('button',), 0.5, 'm', '❌ डेस्कटॉप दिखाने में विफल: ', 'scroll', 'up', 3, 2, '✅ सफलतापूर्वक ', ' की ओर ', ' यूनिट स्क्रॉल किया।', '❌ स्क्रॉल करने में विफल: ', '❌ डेस्कटॉप कंट्रोल में त्रुटि: ']
  Nested code object 45:
    Code Object Analysis:
      Name: send_email
      Filename: tools.py
      First line: 408
      Arg count: 4
      Local count: 7
      Stack size: 5
      Flags: 131
      Names: ('print', 'validate_email', 'GMAIL_USER', 'GMAIL_PASSWORD', 'asyncio', 'create_task', 'to_thread', 'join', 'Exception', 'logger', 'error', 'str')
      Var names: ('to_email', 'subject', 'message', 'cc_email', 'send_email_sync', 'recipients', 'e')
      Constants: ['\n    Sends emails via authenticated Gmail SMTP.\n  ...', '📧 Sending email to: ', '❌ अमान्य प्राप्तकर्ता ईमेल: ', '❌ अमान्य CC ईमेल: ', '❌ ईमेल credentials नहीं मिले। कृपया .env फाइल चेक ...', '<code object send_email_sync at 0x000001EFF4AB4100...', None, '✅ ईमेल सफलतापूर्वक भेजा गया: ', ', ', 'ईमेल त्रुटि: ', '❌ ईमेल भेजने में त्रुटि: ']
      Nested code object 5:
        Code Object Analysis:
          Name: send_email_sync
          Filename: tools.py
          First line: 438
          Arg count: 0
          Local count: 3
          Stack size: 7
          Flags: 19
          Names: ('MIMEMultipart', 'GMAIL_USER', 'attach', 'MIMEText', 'smtplib', 'SMTP', 'starttls', 'login', 'GMAIL_PASSWORD', 'sendmail', 'as_string')
          Var names: ('msg', 'server', 'recipients')
          Constants: [None, 'From', 'To', 'Subject', 'Cc', 'plain', 'smtp.gmail.com', 587]
  Nested code object 46:
    Code Object Analysis:
      Name: list_active_windows
      Filename: tools.py
      First line: 462
      Arg count: 0
      Local count: 6
      Stack size: 8
      Flags: 131
      Names: ('print', 'asyncio', 'create_task', 'to_thread', 'gw', 'getAllWindows', 'title', 'append', 'strip', 'Exception', 'join', 'logger', 'error', 'str')
      Var names: ('windows', 'result', 'is_minimized', 'is_maximized', 'status', 'e')
      Constants: ['\n    Lists all visible application windows.\n    \n ...', '🪟 Listing active windows', None, '<code object <lambda> at 0x000001EFF68C6170, file ...', '<code object <lambda> at 0x000001EFF68C5A70, file ...', 'Minimized', 'Maximized', 'Active', '• ', ' (', ')', '📋 खुली हुई विंडोज:\n', '\n', '❌ कोई विंडो नहीं मिली', 'विंडो सूची त्रुटि: ', '❌ विंडो डिटेक्शन विफल: ']
      Nested code object 3:
        Code Object Analysis:
          Name: <lambda>
          Filename: tools.py
          First line: 484
          Arg count: 0
          Local count: 0
          Stack size: 1
          Flags: 19
          Names: ('isMinimized',)
          Var names: ()
          Constants: [None]
      Nested code object 4:
        Code Object Analysis:
          Name: <lambda>
          Filename: tools.py
          First line: 485
          Arg count: 0
          Local count: 0
          Stack size: 1
          Flags: 19
          Names: ('isMaximized',)
          Var names: ()
          Constants: [None]
  Nested code object 49:
    Code Object Analysis:
      Name: manage_window_state
      Filename: tools.py
      First line: 500
      Arg count: 2
      Local count: 7
      Stack size: 7
      Flags: 131
      Names: ('print', 'asyncio', 'create_task', 'to_thread', 'gw', 'getAllWindows', 'title', 'lower', 'append', 'getActiveWindow', 'maximize', 'minimize', 'restore', 'Exception', 'str')
      Var names: ('action', 'window_title', 'all_windows', 'candidates', 'win', 'target_window', 'e')
      Constants: ['विशिष्ट या सक्रिय विंडो की स्थिति प्रबंधित करें (ब...', '🪟 Managing window state: ', ' for ', 'active window', None, "❌ '", "' नाम की कोई विंडो नहीं मिली", 0, '❌ कोई सक्रिय विंडो नहीं मिली', 'maximize', 'minimize', 'restore', "✅ विंडो '", "' को ", ' किया गया', '❌ विंडो ', ' करने में विफल: ', '❌ त्रुटि: ']
  Nested code object 51:
    Code Object Analysis:
      Name: say_reminder
      Filename: tools.py
      First line: 539
      Arg count: 1
      Local count: 1
      Stack size: 4
      Flags: 131
      Names: ('print',)
      Var names: ('msg',)
      Constants: ['\n    Creates audible/visual reminders.\n    \n    Ar...', '🔔 Reminder: ', '🔔 याद दिलाना: ']
  Nested code object 55:
    Code Object Analysis:
      Name: get_today_reminder_message_from_db
      Filename: tools.py
      First line: 563
      Arg count: 0
      Local count: 12
      Stack size: 6
      Flags: 131
      Names: ('datetime', 'now', 'date', 'print', 'asyncio', 'create_task', 'to_thread', 'json', 'loads', 'lower', 'extract_date_from_text', 'append', 'Exception', 'join')
      Var names: ('today', 'db_operation', 'rows', 'reminders', 'role', 'content_json', 'content_items', 'item', 'item_lower', 'date', 'e', 'combined')
      Constants: ["Get today's reminders from the database", '🔍 Checking reminders for ', '<code object db_operation at 0x000001EFF3F7E8B0, f...', None, 'user', 'remind', 'remember', 'याद दिला', '⚠️ Error parsing content: ', '\n', '<code object <genexpr> at 0x000001EFF68C6CD0, file...', '🧠 सर, आज आपको याद है न — ', '❌ Error while checking reminders: ']
      Nested code object 2:
        Code Object Analysis:
          Name: db_operation
          Filename: tools.py
          First line: 569
          Arg count: 0
          Local count: 3
          Stack size: 5
          Flags: 19
          Names: ('sqlite3', 'connect', 'DB_PATH', 'cursor', 'execute', 'TABLE_NAME', 'fetchall', 'close')
          Var names: ('conn', 'cursor', 'rows')
          Constants: [None, 'SELECT role, content FROM ', ' ORDER BY created_at ASC']
      Nested code object 10:
        Code Object Analysis:
          Name: <genexpr>
          Filename: tools.py
          First line: 599
          Arg count: 1
          Local count: 2
          Stack size: 3
          Flags: 51
          Names: ()
          Var names: ('.0', 'r')
          Constants: ['🔔 ', None]
  Nested code object 57:
    Code Object Analysis:
      Name: extract_date_from_text
      Filename: tools.py
      First line: 608
      Arg count: 1
      Local count: 4
      Stack size: 4
      Flags: 3
      Names: ('datetime', 'now', 'date', 're', 'search', 'strptime', 'group', 'timedelta')
      Var names: ('text', 'today', 'date_match', 'timedelta')
      Constants: ['Extract date from text', '\\d{4}-\\d{2}-\\d{2}', '%Y-%m-%d', None, 'आज', 'कल', 0, ('timedelta',), 1, ('days',)]
  Nested code object 60:
    Code Object Analysis:
      Name: send_whatsapp_message
      Filename: tools.py
      First line: 636
      Arg count: 2
      Local count: 7
      Stack size: 8
      Flags: 131
      Names: ('pyautogui', 'asyncio', 'os', 'print', 'to_thread', 'position', 'press', 'sleep', 'typewrite', 'hotkey', 'moveTo', 'x', 'y', 'Exception', 'str')
      Var names: ('contact', 'message', 'pyautogui', 'asyncio', 'os', 'original_pos', 'e')
      Constants: ['\n    Sends WhatsApp messages via desktop automatio...', 0, None, '📨 WhatsApp भेजने की प्रक्रिया शुरू: ', ' -> ', 'win', 1, 'whatsapp', 0.1, ('interval',), 'enter', 3, 'ctrl', 'f', 2, 1.5, 'down', 0.5, 0.06, "✅ '", '\' को संदेश भेजा गया: "', '"\n🧠 क्या कुछ और भेजना है sir? जवाब दें — Nova उस स...', ('duration',), '❌ संदेश भेजने में त्रुटि: ']
  Nested code object 65:
    Code Object Analysis:
      Name: write_in_notepad
      Filename: tools.py
      First line: 710
      Arg count: 3
      Local count: 16
      Stack size: 9
      Flags: 131
      Names: ('pyautogui', 'asyncio', 'datetime', 'print', 'to_thread', 'position', 'press', 'sleep', 'typewrite', 'hotkey', 'now', 'strftime', 'lower', 'split', 'enumerate', 'strip', 'replace', 'title', 'moveTo', 'x', 'y', 'Exception', 'str')
      Var names: ('title', 'content', 'document_type', 'pyautogui', 'asyncio', 'datetime', 'original_pos', 'current_date', 'paragraphs', 'i', 'paragraph', 'clean_paragraph', 'safe_title', 'filename', 'e', 'error_msg')
      Constants: ['\n    Creates formatted documents in Notepad.\n    \n...', 0, None, '📝 Starting Notepad writing process: ', ' - ', '🔧 Opening Notepad...', 'win', 1, 'notepad', 0.1, ('interval',), 'enter', 3, '📄 Creating new file...', 'ctrl', 'n', 'a', 0.5, 'delete', '✍️ Writing document content...', '%d/%m/%Y', 'Date: ', 0.05, 'Subject: ', ('letter', 'application'), 'Dear Sir/Madam,', '\n\n', 0.03, 'Thank you for your time and consideration.', 'Yours sincerely,', '[Your Name]', '💾 Saving document...', 's', 2, ' ', '_', '/', '\\', '.txt', '✅ Document created successfully!', "✅ '", "' ", ' successfully created in Notepad\n📄 File saved as: ', '\n🎯 Document type: ', '\n📝 Content written with proper formatting\n🔄 New fi...', ('duration',), '❌ Error writing to Notepad: ']
  Nested code object 67:
    Code Object Analysis:
      Name: open_app
      Filename: tools.py
      First line: 839
      Arg count: 1
      Local count: 5
      Stack size: 8
      Flags: 131
      Names: ('pyautogui', 'asyncio', 'print', 'to_thread', 'position', 'press', 'sleep', 'typewrite', 'moveTo', 'x', 'y', 'Exception', 'str')
      Var names: ('app_name', 'pyautogui', 'asyncio', 'original_pos', 'e')
      Constants: ['\n    Launches applications via Start Menu search.\n...', 0, None, '🚀 ऐप खोलने का प्रयास: ', 'win', 1, 0.1, ('interval',), 'enter', "✅ '", "' खोल दिया गया है।", ('duration',), '❌ ऐप खोलने में त्रुटि: ']
  Nested code object 69:
    Code Object Analysis:
      Name: press_key
      Filename: tools.py
      First line: 883
      Arg count: 1
      Local count: 5
      Stack size: 5
      Flags: 131
      Names: ('pyautogui', 'asyncio', 'strip', 'lower', 'split', 'to_thread', 'hotkey', 'press', 'Exception', 'str')
      Var names: ('key', 'pyautogui', 'asyncio', 'keys', 'e')
      Constants: ['\n    Simulates keyboard key presses.\n    \n    Args...', 0, None, '+', '<code object <listcomp> at 0x000001EFF695DF30, fil...', "✅ '", "' दबा दिया गया है।", '❌ कुंजी दबाने में त्रुटि: ']
      Nested code object 4:
        Code Object Analysis:
          Name: <listcomp>
          Filename: tools.py
          First line: 906
          Arg count: 1
          Local count: 2
          Stack size: 4
          Flags: 19
          Names: ('strip',)
          Var names: ('.0', 'k')
          Constants: []
  Nested code object 70:
    Code Object Analysis:
      Name: get_system_info
      Filename: tools.py
      First line: 918
      Arg count: 0
      Local count: 22
      Stack size: 21
      Flags: 131
      Names: ('psutil', 'socket', 'platform', 'shutil', 'sensors_battery', 'percent', 'power_plugged', 'disk_usage', 'gethostname', 'gethostbyname', 'cpu_percent', 'virtual_memory', 'round', 'total', 'used', 'node', 'Exception', 'str')
      Var names: ('psutil', 'socket', 'platform', 'shutil', 'battery', 'battery_percent', 'charging', 'total', 'used', 'free', 'total_gb', 'free_gb', 'hostname', 'ip_address', 'network_status', 'cpu_percent', 'ram', 'ram_percent', 'ram_total_gb', 'ram_used_gb', 'system_name', 'e')
      Constants: ['\n    Provides comprehensive system diagnostics.\n  ...', 0, None, '⚡ Charging', '🔋 On Battery', 'N/A', '/', 1073741824, 'Connected (IP: ', ')', '❌ Not Connected', 1, ('interval',), '🧠 System Info for: ', '\n🔋 Battery: ', '% (', ')\n💾 Storage: ', ' GB free of ', ' GB\n📶 Network: ', '\n🧠 CPU Usage: ', '%\n📈 RAM Usage: ', ' GB of ', ' GB)', '❌ सिस्टम जानकारी प्राप्त करने में त्रुटि: ']
  Nested code object 71:
    Code Object Analysis:
      Name: type_user_message_auto
      Filename: tools.py
      First line: 985
      Arg count: 1
      Local count: 3
      Stack size: 5
      Flags: 131
      Names: ('pyautogui', 'asyncio', 'strip', 'to_thread', 'typewrite')
      Var names: ('message', 'pyautogui', 'asyncio')
      Constants: ['\n    Types content into active window.\n    \n    Ar...', 0, None, '⚠️ Sir, message खाली है।', 0.1, ('interval',), '✅ टाइप कर दिया गया: "', '"']
  Nested code object 80:
    Code Object Analysis:
      Name: click_on_text
      Filename: tools.py
      First line: 1033
      Arg count: 1
      Local count: 20
      Stack size: 9
      Flags: 131
      Names: ('pyautogui', 'pytesseract', 'cv2', 'numpy', 'asyncio', 'difflib', 'SequenceMatcher', 'str', 'float', 'to_thread', 'screenshot', 'array', 'cvtColor', 'COLOR_RGB2BGR', 'COLOR_BGR2GRAY', 'resize', 'INTER_CUBIC', 'image_to_data', 'Output', 'DICT', 'range', 'len', 'strip', 'int', 'moveTo', 'click', 'Exception')
      Var names: ('target_text', 'pyautogui', 'pytesseract', 'cv2', 'np', 'asyncio', 'similarity', 'screenshot', 'screenshot_np', 'image', 'gray', 'data', 'best_match', 'best_score', 'i', 'text', 'score', 'center_x', 'center_y', 'e')
      Constants: ['\n    Clicks on screen text using OCR.\n    \n    Arg...', 0, None, ('SequenceMatcher',), 'text1', 'text2', 'return', '<code object similarity at 0x000001EFF69FC1D0, fil...', 2, ('fx', 'fy', 'interpolation'), ('output_type',), 'text', 0.7, 'left', 'top', 'width', 'height', ('text', 'x', 'y', 'w', 'h'), "❌ '", "' नहीं मिला", 'x', 'w', 'y', 'h', 0.2, ('duration',), "✅ '", "' पर क्लिक किया गया!", '🚫 Error: ']
      Nested code object 7:
        Code Object Analysis:
          Name: similarity
          Filename: tools.py
          First line: 1055
          Arg count: 2
          Local count: 2
          Stack size: 6
          Flags: 19
          Names: ('lower', 'strip', 'ratio')
          Var names: ('text1', 'text2')
          Constants: [None]
  Nested code object 81:
    Code Object Analysis:
      Name: scan_system_for_viruses
      Filename: tools.py
      First line: 1111
      Arg count: 0
      Local count: 9
      Stack size: 7
      Flags: 131
      Names: ('asyncio', 'subprocess', 'create_subprocess_exec', 'PIPE', 'FileNotFoundError', 'communicate', 'decode', 'strip', 'Exception', 'str')
      Var names: ('asyncio', 'subprocess', 'cmd', 'alt_cmd', 'proc', 'stdout', 'stderr', 'output', 'e')
      Constants: ['\n    Performs quick virus scan using Windows Defen...', 0, None, "('C:\\\\Program Files\\\\Windows Defender\\\\MpCmdRun.ex...", "('C:\\\\ProgramData\\\\Microsoft\\\\Windows Defender\\\\Pl...", ('stdout', 'stderr'), 'Scan starting', 'Scan completed', '🛡️ सिस्टम स्कैन पूरा हुआ:\n\n', -500, '⚠️ स्कैन पूरा हुआ, लेकिन कोई जानकारी नहीं मिली:\n\n', '❌ स्कैन में त्रुटि: ']
  Nested code object 83:
    Code Object Analysis:
      Name: enable_camera_analysis
      Filename: tools.py
      First line: 1167
      Arg count: 1
      Local count: 2
      Stack size: 7
      Flags: 131
      Names: ('assistant_instance', 'enable_visual_analysis', 'Exception', 'str')
      Var names: ('enable', 'e')
      Constants: ['\n    Enable or disable camera feed analysis. When ...', None, 'Assistant instance not set', 'Failed to ', 'enable', 'disable', ' camera analysis: ']
  Nested code object 85:
    Code Object Analysis:
      Name: analyze_visual_scene
      Filename: tools.py
      First line: 1188
      Arg count: 1
      Local count: 3
      Stack size: 5
      Flags: 131
      Names: ('assistant_instance', 'enable_visual_analysis', 'asyncio', 'sleep', 'analyze_current_scene', 'Exception', 'str')
      Var names: ('prompt', 'result', 'e')
      Constants: ['\n    Analyzes camera feed based on prompt.\n    \n  ...', True, None, 0.5, False, 'Visual analysis failed: ']
  Nested code object 99:
    Code Object Analysis:
      Name: _open_file_dialog
      Filename: tools.py
      First line: 1258
      Arg count: 0
      Local count: 3
      Stack size: 5
      Flags: 131
      Names: ('reset_analysis', 'tk', 'Tk', 'withdraw', 'attributes', 'focus_force', 'filedialog', 'askopenfilename', 'destroy', 'Exception', 'print')
      Var names: ('root', 'file_path', 'e')
      Constants: ['Open file dialog to select Excel/CSV file', '-topmost', True, 'Select Data File', "(('Excel files', '*.xlsx *.xls'), ('CSV files', '*...", ('title', 'filetypes'), '', 'Error in file dialog: ', None]
  Nested code object 101:
    Code Object Analysis:
      Name: _load_data_file
      Filename: tools.py
      First line: 1284
      Arg count: 1
      Local count: 5
      Stack size: 9
      Flags: 3
      Names: ('os', 'path', 'exists', 'FileNotFoundError', 'Path', 'suffix', 'lower', 'pd', 'read_csv', 'read_excel', 'ValueError', 'columns', 'astype', 'str', 'strip', 'dropna', 'reset_index', 'contains', 'loc', 'Exception', 'print')
      Var names: ('file_path', 'file_ext', 'df', 'unnamed_cols', 'e')
      Constants: ['Ultra-fast data loading with optimizations', 'File not found: ', '.csv', 'utf-8', False, 'c', True, ('', 'NULL', 'null', 'NaN', 'nan', 'N/A', 'n/a'), "('encoding', 'low_memory', 'engine', 'skipinitials...", ('.xlsx', '.xls'), '.xlsx', 'openpyxl', ('engine',), 'xlrd', 'Unsupported file format: ', 'all', ('how',), ('drop',), '^Unnamed', ('na',), None, 'Error loading file: ']
  Nested code object 103:
    Code Object Analysis:
      Name: _detect_column_types_fast
      Filename: tools.py
      First line: 1326
      Arg count: 1
      Local count: 9
      Stack size: 8
      Flags: 3
      Names: ('columns', 'isna', 'all', 'dtype', 'pd', 'api', 'types', 'is_numeric_dtype', 'is_datetime64_any_dtype', 'min', 'len', 'dropna', 'iloc', 'astype', 'str', 'lower', 'contains', 'any', 'endswith', 'unique')
      Var names: ('df', 'type_info', 'col', 'col_data', 'dtype', 'sample_size', 'sample', 'sample_str', 'unique_ratio')
      Constants: ['Ultra-fast column type detection', 'empty', 'numeric', 'datetime', 100, 0, None, 'date|2020|2021|2022|2023|2024|2025', False, ('na',), 'potential_date', '_id', 'id', 'identifier', 0.5, 'categorical', 'text']
  Nested code object 104:
    Code Object Analysis:
      Name: _detect_business_context_fast
      Filename: tools.py
      First line: 1364
      Arg count: 1
      Local count: 12
      Stack size: 7
      Flags: 3
      Names: ('pd', 'Series', 'columns', 'str', 'lower', 'join', 'items', 'sum', 'contains', 'tolist', 'select_dtypes', 'np', 'number', 'any', 'append', 'nunique', 'len')
      Var names: ('df', 'context', 'columns_lower', 'domain_keywords', 'max_score', 'domain', 'keywords', 'score', 'time_mask', 'id_mask', 'numeric_cols', 'metric_keywords')
      Constants: ['Ultra-fast business context detection', 'unknown', "('domain', 'key_metrics', 'time_columns', 'id_colu...", "('sales', 'revenue', 'profit', 'customer', 'order'...", "('employee', 'salary', 'department', 'performance'...", "('product', 'stock', 'quantity', 'warehouse', 'inv...", "('campaign', 'clicks', 'impressions', 'conversion'...", "('expense', 'cost', 'budget', 'account', 'balance'...", "('sales', 'hr', 'inventory', 'marketing', 'financi...", ' ', 0, '<code object <genexpr> at 0x000001EFF69BC3F0, file...', 'domain', 'date|time|month|year', False, ('na',), 'id|_id$', 'time_columns', 'id_columns', ('include',), 'numeric_columns', "('amount', 'price', 'cost', 'revenue', 'profit', '...", '<code object <genexpr> at 0x000001EFF69DCE00, file...', 'key_metrics', 0.5, 'categorical_columns']
      Nested code object 11:
        Code Object Analysis:
          Name: <genexpr>
          Filename: tools.py
          First line: 1390
          Arg count: 1
          Local count: 2
          Stack size: 3
          Flags: 51
          Names: ()
          Var names: ('.0', 'keyword')
          Constants: [1, None]
      Nested code object 22:
        Code Object Analysis:
          Name: <genexpr>
          Filename: tools.py
          First line: 1409
          Arg count: 1
          Local count: 2
          Stack size: 4
          Flags: 51
          Names: ('lower',)
          Var names: ('.0', 'keyword')
          Constants: [None]
  Nested code object 105:
    Code Object Analysis:
      Name: _analyze_data_quality_fast
      Filename: tools.py
      First line: 1420
      Arg count: 1
      Local count: 5
      Stack size: 6
      Flags: 3
      Names: ('len', 'columns', 'isnull', 'sum', 'duplicated', 'max')
      Var names: ('df', 'total_cells', 'missing_count', 'quality', 'quality_score')
      Constants: ['Ultra-fast data quality analysis', 0, 100, "('total_rows', 'total_columns', 'missing_percentag...", 'missing_percentage', 'duplicate_rows', 'quality_score']
  Nested code object 107:
    Code Object Analysis:
      Name: _find_key_insights_fast
      Filename: tools.py
      First line: 1439
      Arg count: 2
      Local count: 13
      Stack size: 9
      Flags: 3
      Names: ('columns', 'dropna', 'len', 'float', 'max', 'min', 'mean', 'median', 'std', 'corr', 'np', 'triu', 'ones_like', 'bool', 'where', 'range', 'iloc', 'pd', 'isna', 'abs', 'append', '_generate_business_insights_fast')
      Var names: ('df', 'business_context', 'insights', 'key_metrics', 'metric', 'col_data', 'numeric_cols', 'corr_matrix', 'mask', 'corr_values', 'i', 'j', 'corr_value')
      Constants: ['Ultra-fast insight extraction', "('top_performers', 'correlations', 'distributions'...", 'key_metrics', None, 5, 0, "('max_value', 'min_value', 'mean', 'median', 'std'...", 'top_performers', 'numeric_columns', 8, 1, ('dtype',), ('k',), 0.7, 'correlations', 'strong positive', 'strong negative', ('column1', 'column2', 'correlation', 'strength'), 'business_insights']
  Nested code object 109:
    Code Object Analysis:
      Name: _generate_business_insights_fast
      Filename: tools.py
      First line: 1488
      Arg count: 3
      Local count: 12
      Stack size: 12
      Flags: 3
      Names: ('list', 'items', 'append', 'isnull', 'sum', 'len', 'columns')
      Var names: ('df', 'business_context', 'insights', 'business_insights', 'top_metrics', 'metric', 'data', 'range_pct', 'corr', 'missing_pct', 'domain', 'domain_insights')
      Constants: ['Ultra-fast business insight generation', 'top_performers', None, 3, 'max_value', 0, 'min_value', 'mean', 100, 'The ', ' shows high variability with a ', '.1f', '% range from mean, indicating potential opportunit...', 'correlations', 2, 'Strong ', 'strength', ' correlation (', 'correlation', '.3f', ') between ', 'column1', ' and ', 'column2', ' suggests potential business relationship.', 10, 'Data quality requires attention: ', '% missing values detected.', 'domain', 'Sales analysis reveals performance patterns across...', 'HR data analysis shows employee performance and or...', 'Inventory analysis indicates stock levels and prod...', 'Marketing metrics show campaign effectiveness and ...', 'Financial analysis reveals cost structures and bud...', "('sales', 'hr', 'inventory', 'marketing', 'financi..."]
  Nested code object 110:
    Code Object Analysis:
      Name: _create_visualizations_fast
      Filename: tools.py
      First line: 1529
      Arg count: 2
      Local count: 14
      Stack size: 8
      Flags: 3
      Names: ('plt', 'switch_backend', 'style', 'use', 'subplots', 'suptitle', 'enumerate', 'hist', 'set_title', 'grid', 'range', 'len', 'set_visible', 'tight_layout', 'savefig', 'close', 'figure', 'corr', 'sns', 'heatmap', 'title')
      Var names: ('df', 'business_context', 'visualizations', 'key_metrics', 'fig', 'axes', 'i', 'metric', 'row', 'col', 'metrics_path', 'numeric_cols', 'corr_matrix', 'corr_path')
      Constants: ['Ultra-fast visualization creation', 'Agg', 'fast', 'key_metrics', None, 4, 2, (10, 6), ('figsize',), 'Key Metrics Overview', 12, 'bold', ('fontsize', 'fontweight'), 15, 0.7, 'skyblue', ('bins', 'ax', 'alpha', 'color'), ' Distribution', 10, ('fontsize',), True, 0.3, ('alpha',), False, 'key_metrics_overview.png', 150, 'tight', ('dpi', 'bbox_inches'), 'numeric_columns', 8, 1, (8, 6), 'coolwarm', 0, '.2f', ('annot', 'cmap', 'center', 'square', 'fmt'), 'Correlation Matrix', 'correlation_heatmap.png', 'correlations']
  Nested code object 112:
    Code Object Analysis:
      Name: _generate_html_report_fast
      Filename: tools.py
      First line: 1576
      Arg count: 4
      Local count: 31
      Stack size: 24
      Flags: 3
      Names: ('datetime', 'now', 'strftime', 'get', 'title', 'len', 'columns', 'list', 'items', 'abs', 'Exception', 'open', 'write', 'print', 'str')
      Var names: ('df', 'insights', 'quality', 'business_context', 'timestamp', 'domain', 'key_metrics_count', 'quality_score', 'missing_percentage', 'duplicate_rows', 'outliers', 'inconsistencies', 'top_performers', 'business_insights', 'correlations', 'html_content', 'metric', 'data', 'max_val', 'min_val', 'mean_val', 'median_val', 'insight', 'corr', 'corr_value', 'strength_class', 'strength_text', 'e', 'report_path', 'f', 'simple_html')
      Constants: ['Ultra-fast HTML report generation with enhanced da...', '%B %d, %Y at %I:%M %p', 'domain', 'unknown domain', 'key_metrics', 'quality_score', 0, 'missing_percentage', 'duplicate_rows', 'outliers', 'inconsistencies', 'top_performers', 'business_insights', 'correlations', '<!DOCTYPE html>\n<html lang="en"><head><meta charse...', '\n                </div>\n                <div class...', '\n                </div>\n                <div class...', ',', ' records × ', ' attributes\n                </div>\n            </d...', '</div>\n                <div class="metric-label">T...', '</div>\n                <div class="metric-label">D...', '.1f', '%</div>\n                <div class="metric-label">...', '</div>\n                <div class="metric-label">K...', '<table class="stats-table"><tr><th>Metric</th><th>...', None, 5, 'max_value', 'min_value', 'mean', 'median', '<tr><td><strong>', '</strong></td><td>', ',.2f', '</td><td>', '</td></tr>', '</table>', '<h3 class="section-title" style="margin-top: 40px;...', '<div class="insight-grid">', '\n                <div class="insight-card">\n      ...', '</div>\n                </div>', '</div>', '<h3 class="section-title" style="margin-top: 40px;...', 3, 'correlation', 0.7, 'strong', 0.3, 'moderate', 'weak', 'Strong', 'Moderate', 'Weak', '\n                    <div class="correlation-item"...', 'column1', 'Variable 1', '</strong> ↔ \n                            <strong>', 'column2', 'Variable 2', '</strong>\n                        </div>\n         ...', '">', '</span>\n                            <div style="ma...', '.3f', '</div>\n                        </div>\n            ...', '</div>\n    \n    <div class="section">\n        <h2 ...', '%</div>\n                <div class="metric-label">...', '</div>\n                <div class="metric-label">D...', '</div>\n                <div class="metric-label">P...', '</div>\n                <div class="metric-label">D...', '</strong> dataset reveals valuable insights from <...', '</strong> records across <strong>', '</strong> attributes. Key findings include:</p>\n  ...', '</strong> key performance metrics with detailed st...', '</strong> actionable business insights identified<...', '</strong> significant data relationships discovere...', '%</strong> with recommendations for improvement</l...', 'professional_data_report_dark.html', 'w', 'utf-8', ('encoding',), 'Error generating HTML report: ', '<html><body><h1>Report Generation Error</h1><p>', '</p></body></html>', 'error_report.html']
  Nested code object 113:
    Code Object Analysis:
      Name: reset_analysis
      Filename: tools.py
      First line: 2110
      Arg count: 0
      Local count: 0
      Stack size: 7
      Flags: 3
      Names: ('GLOBAL_DF', 'ANALYSIS_CACHE')
      Var names: ()
      Constants: ['Reset all analysis data', None, "('data', 'insights', 'metadata', 'analysis_results...", '✅ Analysis data reset successfully.']
  Nested code object 114:
    Code Object Analysis:
      Name: load_and_analyze_excel
      Filename: tools.py
      First line: 2127
      Arg count: 0
      Local count: 11
      Stack size: 13
      Flags: 131
      Names: ('asyncio', 'get_event_loop', 'ThreadPoolExecutor', 'run_in_executor', '_open_file_dialog', '_load_data_file', 'empty', 'GLOBAL_DF', 'ANALYSIS_CACHE', '_detect_business_context_fast', '_analyze_data_quality_fast', 'gather', '_find_key_insights_fast', 'update', 'datetime', 'now', 'isoformat', 'len', 'columns', 'Exception', 'str')
      Var names: ('loop', 'executor', 'file_path', 'df', 'business_task', 'quality_task', 'business_context', 'quality_results', 'insights_task', 'insights', 'e')
      Constants: ['\n    Loads an Excel file, analyzes its contents us...', 1, ('max_workers',), None, 'No file selected for analysis.', 'The selected file could not be loaded or is empty.', 'data', 4, "('analysis_timestamp', 'file_path', 'total_rows', ...", "('business_context', 'analysis_results', 'insights...", 'Analysis completed.\nDataset: ', ',', ' rows × ', ' columns\nDetected Domain: ', 'domain', '\nData Quality Score: ', 'quality_score', '.1f', '%\nGenerated Insights: ', 'business_insights', 'Error during analysis: ']
  Nested code object 115:
    Code Object Analysis:
      Name: get_analysis_report
      Filename: tools.py
      First line: 2189
      Arg count: 0
      Local count: 8
      Stack size: 9
      Flags: 131
      Names: ('ANALYSIS_CACHE', 'asyncio', 'get_event_loop', 'ThreadPoolExecutor', 'run_in_executor', '_generate_html_report_fast', 'webbrowser', 'open', 'os', 'path', 'abspath', 'Exception', 'str')
      Var names: ('df', 'insights', 'quality', 'business_context', 'loop', 'executor', 'report_path', 'e')
      Constants: ['\n    Generates and opens a detailed HTML report su...', 'data', None, 'No data available. Please run load_and_analyze_exc...', 'insights', 'analysis_results', 'business_context', 1, ('max_workers',), 'file://', 'Report generated and opened successfully at: ', 'Report generation failed: ']
  Nested code object 116:
    Code Object Analysis:
      Name: get_analysis_status
      Filename: tools.py
      First line: 2219
      Arg count: 0
      Local count: 4
      Stack size: 18
      Flags: 131
      Names: ('ANALYSIS_CACHE', 'len', 'columns', 'title')
      Var names: ('df', 'business_context', 'quality', 'insights')
      Constants: ['\n    Returns the current status of the analysis, i...', 'data', None, 'No data loaded. Please perform analysis first.', 'business_context', 'analysis_results', 'insights', 'Analysis Status\n', '----------------------------------------', '\nRows × Columns: ', ',', ' × ', '\nBusiness Domain: ', 'domain', '\nQuality Score: ', 'quality_score', '.1f', '%\nKey Metrics: ', 'key_metrics', '\nInsights Generated: ', 'business_insights', '\nLast Run: ', 'metadata', 'analysis_timestamp', 19]
  Nested code object 117:
    Code Object Analysis:
      Name: create_visualizations_chart
      Filename: tools.py
      First line: 2247
      Arg count: 0
      Local count: 9
      Stack size: 7
      Flags: 131
      Names: ('ANALYSIS_CACHE', 'asyncio', 'get_event_loop', 'ThreadPoolExecutor', 'run_in_executor', '_create_visualizations_fast', 'items', 'Exception', 'str')
      Var names: ('df', 'business_context', 'loop', 'executor', 'visualizations', 'result', 'chart', 'path', 'e')
      Constants: ['\n    Generates and saves visual charts based on th...', 'data', None, 'No dataset available for visualization. Please ana...', 'business_context', 1, ('max_workers',), 'visualizations', 'Generated Visualizations:\n', '- ', ': ', '\n', 'No visualizations were generated for this dataset.', 'Visualization error: ']
  Nested code object 118:
    Code Object Analysis:
      Name: get_top_insights
      Filename: tools.py
      First line: 2282
      Arg count: 0
      Local count: 4
      Stack size: 6
      Flags: 131
      Names: ('ANALYSIS_CACHE', 'get', 'enumerate', 'strip')
      Var names: ('insights', 'result', 'i', 'insight')
      Constants: ['\n    Retrieves top business insights derived from ...', 'insights', 'business_insights', 'No business insights available. Please run analysi...', 'Top Business Insights\n----------------------------...', 1, '. ', '\n\n']
  Nested code object 119:
    Code Object Analysis:
      Name: get_data_summary
      Filename: tools.py
      First line: 2301
      Arg count: 0
      Local count: 3
      Stack size: 22
      Flags: 131
      Names: ('ANALYSIS_CACHE', 'len', 'columns', 'title')
      Var names: ('df', 'context', 'quality')
      Constants: ['\n    Summarizes the dataset including structure, q...', 'data', None, 'Data summary not available. Please run analysis fi...', 'business_context', 'analysis_results', 'Dataset Summary\n', '----------------------------------------', '\nDimensions: ', ',', ' rows × ', ' columns\nDomain: ', 'domain', '\nData Quality: ', 'quality_score', '.1f', '%\nMissing Values: ', 'missing_percentage', '%\nNumeric Columns: ', 'numeric_columns', '\nCategorical Columns: ', 'categorical_columns', '\nKey Metrics Identified: ', 'key_metrics', '\nLast Analyzed: ', 'metadata', 'analysis_timestamp', 19]
  Nested code object 122:
    Code Object Analysis:
      Name: export_results
      Filename: tools.py
      First line: 2329
      Arg count: 1
      Local count: 4
      Stack size: 9
      Flags: 131
      Names: ('ANALYSIS_CACHE', 'datetime', 'now', 'strftime', 'lower', 'open', 'json', 'dump', 'isoformat', 'str', 'get_analysis_report')
      Var names: ('format_type', 'timestamp', 'filename', 'f')
      Constants: ['\n    Exports the analysis results in the specified...', 'insights', 'No results available to export. Please run the ana...', '%Y%m%d_%H%M%S', 'json', 'analysis_results_', '.json', 'w', 'utf-8', ('encoding',), 'metadata', 'business_context', 'analysis_results', "('metadata', 'business_context', 'analysis_results...", 2, ('indent', 'default'), None, 'Analysis results exported to ', 'html', "Unsupported export format. Use either 'json' or 'h..."]
  Nested code object 123:
    Code Object Analysis:
      Name: full_analysis_with_report
      Filename: tools.py
      First line: 2366
      Arg count: 0
      Local count: 3
      Stack size: 5
      Flags: 131
      Names: ('load_and_analyze_excel', 'create_visualizations_chart', 'get_analysis_report', 'get_data_summary')
      Var names: ('result', 'report_result', 'summary')
      Constants: ['\n    Asks user to select an Excel file, performs f...', None, 'Error', 'No file', '\n\n']
  Nested code object 126:
    Code Object Analysis:
      Name: _create_file_dialog
      Filename: tools.py
      First line: 2406
      Arg count: 0
      Local count: 3
      Stack size: 7
      Flags: 3
      Names: ('tk', 'Tk', 'withdraw', 'attributes', 'title', 'filedialog', 'askopenfilename', 'os', 'path', 'expanduser', 'destroy', 'Exception', 'print')
      Var names: ('root', 'file_path', 'e')
      Constants: ['Advanced file dialog with multiple format support', '-topmost', True, 'Advanced Graph Creator', '🔍 Select Your Data File', "(('CSV files', '*.csv'), ('Excel files', '*.xlsx')...", '~/Downloads', ('title', 'filetypes', 'initialdir'), '❌ File dialog error: ', None]
  Nested code object 128:
    Code Object Analysis:
      Name: _create_input_dialog
      Filename: tools.py
      First line: 2433
      Arg count: 3
      Local count: 8
      Stack size: 5
      Flags: 3
      Names: ('tk', 'Tk', 'withdraw', 'attributes', 'join', 'simpledialog', 'askstring', 'askinteger', 'destroy', 'Exception', 'print')
      Var names: ('prompt', 'options', 'dialog_type', 'root', 'options_str', 'full_prompt', 'result', 'e')
      Constants: ['Advanced input dialog with validation', '-topmost', True, '\n', '<code object <listcomp> at 0x000001EFF69C2950, fil...', '\n\n📋 Available options:\n', '\n\n✏️ Enter your choice:', 'string', '📊 Graph Creator Input', 'integer', '❌ Input dialog error: ', None]
      Nested code object 4:
        Code Object Analysis:
          Name: <listcomp>
          Filename: tools.py
          First line: 2441
          Arg count: 1
          Local count: 2
          Stack size: 4
          Flags: 19
          Names: ()
          Var names: ('.0', 'opt')
          Constants: ['• ']
  Nested code object 129:
    Code Object Analysis:
      Name: _load_data_smart
      Filename: tools.py
      First line: 2457
      Arg count: 1
      Local count: 8
      Stack size: 9
      Flags: 3
      Names: ('os', 'path', 'splitext', 'lower', 'pd', 'read_csv', 'len', 'columns', 'print', 'read_excel', 'ValueError', 'Exception', 'str')
      Var names: ('file_path', 'file_ext', 'encodings', 'separators', 'encoding', 'sep', 'df', 'e')
      Constants: ['Smart data loading with multiple encoding support', 1, '.csv', '.txt', ('utf-8', 'latin1', 'cp1252', 'iso-8859-1'), (',', ';', '\t', '|'), ('encoding', 'sep'), 0, '✅ Data loaded: ', " encoding, '", "' separator", 'python', 'utf-8', None, ('engine', 'encoding', 'sep'), ('.xlsx', '.xls'), '.xlsx', 'openpyxl', 'xlrd', ('engine',), 'Unsupported file format: ', 'Data loading failed: ']
  Nested code object 130:
    Code Object Analysis:
      Name: _analyze_data
      Filename: tools.py
      First line: 2491
      Arg count: 1
      Local count: 6
      Stack size: 6
      Flags: 3
      Names: ('columns', 'dtype', 'pd', 'api', 'types', 'is_numeric_dtype', 'append', 'is_datetime64_any_dtype', 'is_bool_dtype', 'is_object_dtype', 'to_numeric', 'count')
      Var names: ('df', 'analysis', 'col', 'dtype', 'numeric_count', 'total_count')
      Constants: ['Advanced data analysis and column classification', "('numeric_columns', 'categorical_columns', 'dateti...", 'numeric_columns', 'datetime_columns', 'boolean_columns', 'coerce', ('errors',), 0.8, 'mixed_columns', 'categorical_columns']
  Nested code object 131:
    Code Object Analysis:
      Name: _create_advanced_styling
      Filename: tools.py
      First line: 2524
      Arg count: 2
      Local count: 6
      Stack size: 8
      Flags: 3
      Names: ('plt', 'cm', 'tab10', 'Set2', 'Set1', 'Dark2', 'tab20', 'plasma', 'get', 'style', 'use', 'viridis', 'coolwarm', 'Pastel1')
      Var names: ('style', 'color_scheme', 'style_configs', 'config', 'color_maps', 'colors')
      Constants: ['Advanced styling configuration', 'default', ('style', 'colors'), 'seaborn-v0_8-darkgrid', 'ggplot', 'bmh', 'classic', 'dark_background', "('default', 'seaborn', 'ggplot', 'bmh', 'classic',...", 'style', "('auto', 'viridis', 'plasma', 'coolwarm', 'tab10',...", 'auto']
  Nested code object 132:
    Code Object Analysis:
      Name: _add_statistics
      Filename: tools.py
      First line: 2556
      Arg count: 6
      Local count: 10
      Stack size: 9
      Flags: 3
      Names: ('columns', 'dropna', 'len', 'append', 'mean', 'median', 'std', 'join', 'dict', 'text', 'transAxes')
      Var names: ('ax', 'df', 'x_column', 'y_column', 'graph_type', 'show_stats', 'stats_text', 'data', 'textstr', 'props')
      Constants: ['Add statistical information to graph', ('pie', 'heatmap'), None, 0, 'Mean: ', '.2f', 'Median: ', 'Std: ', 'Count: ', '\n', 'round', 'wheat', 0.8, ('boxstyle', 'facecolor', 'alpha'), 0.02, 0.98, 9, 'top', "('transform', 'fontsize', 'verticalalignment', 'bb..."]
  Nested code object 133:
    Code Object Analysis:
      Name: _create_graph_advanced
      Filename: tools.py
      First line: 2577
      Arg count: 6
      Local count: 39
      Stack size: 13
      Flags: 3
      Names: ('dtype', 'len', 'range', 'plot', 'fill_between', 'max', 'set_xticks', 'set_xticklabels', 'iloc', 'select_dtypes', 'np', 'number', 'columns', 'nlargest', 'head', 'bar', 'linspace', 'enumerate', 'get_height', 'text', 'get_x', 'get_width', 'scatter', 'polyfit', 'poly1d', 'dropna', 'hist', 'min', 'set_facecolor', 'stats', 'norm', 'fit', 'pdf', 'legend', 'value_counts', 'pie', 'values', 'index', 'set_color', 'set_fontweight', 'ValueError', 'unique', 'boxplot', 'zip', 'set_alpha', 'empty', 'corr', 'triu', 'ones_like', 'bool', 'sns', 'heatmap')
      Var names: ('df', 'graph_type', 'x_column', 'y_column', 'colors', 'ax', 'x_vals', 'line', 'step', 'df_plot', 'bars', 'i', 'bar', 'height', 'scatter', 'z', 'p', 'data', 'n', 'bins', 'patches', 'patch', 'mu', 'sigma', 'x', 'y', 'value_counts', 'colors_pie', 'wedges', 'texts', 'autotexts', 'autotext', 'categories', 'data_to_plot', 'bp', 'color', 'numeric_df', 'correlation_matrix', 'mask')
      Constants: ['Advanced graph creation with all types', 'line', 'object', 100, 'o', 2.5, 5, 0.8, 0.1, "('marker', 'linewidth', 'markersize', 'alpha', 'co...", ('alpha', 'color'), 1, 15, None, 45, 'right', ('rotation', 'ha'), 6, 'bar', 50, ('include',), 0, 'black', 0.5, ('color', 'alpha', 'edgecolor', 'linewidth'), 2.0, '.1f', 'center', 'bottom', 8, ('ha', 'va', 'fontsize'), 'scatter', 0.7, 80, "('alpha', 's', 'c', 'cmap', 'edgecolors', 'linewid...", 2, 'r--', ('alpha', 'linewidth'), 'histogram', 10, 20, 0.3, ('bins', 'alpha', 'color', 'edgecolor'), 'r-', 'Normal (μ=', '.2f', ', σ=', ')', ('linewidth', 'alpha', 'label'), 'pie', '%1.1f%%', 90, 0.05, "('labels', 'autopct', 'startangle', 'colors', 'exp...", 'white', 'bold', 'Pie chart requires categorical column', 'box', '<code object <listcomp> at 0x000001EFF69F83C0, fil...', True, ('labels', 'patch_artist'), 'boxes', ('patch_artist',), 'heatmap', 'Heatmap requires numeric columns', ('dtype',), 'RdYlBu_r', 'shrink', "('mask', 'annot', 'cmap', 'center', 'ax', 'square'...", 'area', 0.4, 3, ('linewidth', 'color')]
      Nested code object 58:
        Code Object Analysis:
          Name: <listcomp>
          Filename: tools.py
          First line: 2670
          Arg count: 1
          Local count: 2
          Stack size: 5
          Flags: 19
          Names: ('dropna',)
          Var names: ('.0', 'cat')
          Constants: []
  Nested code object 154:
    Code Object Analysis:
      Name: create_advanced_graph
      Filename: tools.py
      First line: 2709
      Arg count: 15
      Local count: 31
      Stack size: 9
      Flags: 3
      Names: ('print', '_create_file_dialog', 'plt', 'close', 'os', 'path', 'basename', 'exists', '_load_data_smart', 'empty', 'shape', '_analyze_data', 'len', 'head', 'tail', 'sample', 'min', '_create_input_dialog', 'columns', 'list', '_create_advanced_styling', 'clf', 'subplots', '_create_graph_advanced', '_add_statistics', 'title', 'set_title', 'set_xlabel', 'set_ylabel', 'grid', 'set_axisbelow', 'tight_layout', 'datetime', 'now', 'strftime', 'splitext', 'endswith', 'join', 'expanduser', 'savefig', 'show', 'sys', 'platform', 'startswith', 'startfile', 'system', 'upper', 'strip', 'Exception', 'str')
      Var names: ('file_path', 'graph_type', 'x_column', 'y_column', 'data_range', 'data_limit', 'title', 'save_name', 'style', 'color_scheme', 'interactive_mode', 'show_stats', 'export_format', 'dpi', 'figsize', 'df', 'analysis', 'original_size', 'suitable_columns', 'choice', 'colors', 'fig', 'ax', 'timestamp', 'filename', 'downloads_path', 'full_save_path', 'save_params', 'report', 'e', 'error_msg')
      Constants: ['\n    🚀 ADVANCED GRAPH CREATOR - Complete Solution ...', '🚀 Advanced Graph Creator Starting...', '📁 Opening advanced file dialog...', 'all', '❌ No file selected - Process cancelled', '✅ File selected: ', '❌ File not found: ', '📊 Loading data with smart detection...', '❌ File is empty or contains no valid data', '✅ Data loaded successfully: ', 0, ' rows × ', 1, ' columns', '🔍 Data Analysis:', '   • Numeric columns: ', 'numeric_columns', '   • Categorical columns: ', 'categorical_columns', '   • DateTime columns: ', 'datetime_columns', 'head', '📊 Using first ', ' rows', 'tail', '📊 Using last ', 'custom', 42, ('n', 'random_state'), '📊 Using random sample of ', '🎯 Column selection process...', ('histogram', 'box'), 'mixed_columns', '❌ ', ' requires numeric columns', 'Select column for ', ':', 'Select X-axis column:', ('histogram', 'pie', 'box', 'heatmap'), ' requires numeric Y-axis column', 'Select Y-axis column:', "❌ Column '", "' not found in data", '✅ Columns selected - X: ', ', Y: ', '🎨 Creating ', ' graph with advanced styling...', 'white', ('figsize', 'facecolor'), ' Analysis: ', ' vs ', '', 16, 'bold', 20, ('fontsize', 'fontweight', 'pad'), ('pie', 'heatmap'), 12, ('fontsize', 'fontweight'), True, 0.3, '--', 0.5, ('alpha', 'linestyle', 'linewidth'), '%Y%m%d_%H%M%S', '_', '.', '~', 'Downloads', 'tight', 'none', ('dpi', 'bbox_inches', 'facecolor', 'edgecolor'), 'pdf', 'format', 'svg', '💾 Graph saved: ', 'win', 'darwin', 'open "', '"', 'xdg-open "', '\n🎉 GRAPH CREATION SUCCESSFUL!\n\n📊 GRAPH DETAILS:\n  ...', '\n   • Style: ', '\n   • Color Scheme: ', '\n   • Size: ', '×', '\n   • Statistics: ', 'Enabled', 'Disabled', '\n\n📁 DATA INFORMATION:\n   • File: ', '\n   • Total Rows: ', ',', '\n   • Used Rows: ', '\n   • Columns: ', '\n\n📈 AXIS CONFIGURATION:\n   • X-axis: ', '\n   • Y-axis: ', 'N/A', '\n\n💾 EXPORT DETAILS:\n   • Format: ', '\n   • Resolution: ', ' DPI\n   • Filename: ', '\n   • Location: ', '\n\n🔍 DATA ANALYSIS:\n   • Numeric Columns: ', '\n   • Categorical Columns: ', '\n   • DateTime Columns: ', '\n        ', '❌ Graph creation failed: ', None]
  Nested code object 156:
    Code Object Analysis:
      Name: create_quick_advanced_graph
      Filename: tools.py
      First line: 2985
      Arg count: 13
      Local count: 16
      Stack size: 16
      Flags: 131
      Names: ('print', 'create_advanced_graph', 'Exception', 'str')
      Var names: ('graph_type', 'x_column', 'y_column', 'data_range', 'data_limit', 'title', 'save_name', 'style', 'color_scheme', 'interactive_mode', 'show_stats', 'export_format', 'dpi', 'result', 'e', 'error_msg')
      Constants: ['\n    ⚡ QUICK ADVANCED GRAPH CREATOR - Fast One-Cli...', '⚡ Starting Quick Advanced Graph Creation...', None, "('file_path', 'graph_type', 'x_column', 'y_column'...", '❌ Quick graph creation failed: ']
  Nested code object 161:
    Code Object Analysis:
      Name: _load_mac_cache
      Filename: tools.py
      First line: 3103
      Arg count: 0
      Local count: 1
      Stack size: 6
      Flags: 3
      Names: ('os', 'path', 'exists', '_cache_file', 'open', 'json', 'load', '_mac_cache', 'Exception')
      Var names: ('f',)
      Constants: ['Load MAC vendor cache from file for instant lookup...', 'r', None]
  Nested code object 162:
    Code Object Analysis:
      Name: _save_mac_cache
      Filename: tools.py
      First line: 3113
      Arg count: 0
      Local count: 1
      Stack size: 6
      Flags: 3
      Names: ('open', '_cache_file', 'json', 'dump', '_mac_cache', 'Exception')
      Var names: ('f',)
      Constants: ['Save MAC vendor cache to file', 'w', None]
  Nested code object 164:
    Code Object Analysis:
      Name: _get_mac_vendor_fast
      Filename: tools.py
      First line: 3121
      Arg count: 1
      Local count: 2
      Stack size: 4
      Flags: 3
      Names: ('_mac_cache', 'MacLookup', 'lookup', 'Exception')
      Var names: ('mac_address', 'vendor')
      Constants: ['Ultra-fast MAC vendor lookup with caching', 'N/A', 'Unknown']
  Nested code object 165:
    Code Object Analysis:
      Name: _discover_network_ultra_fast
      Filename: tools.py
      First line: 3138
      Arg count: 0
      Local count: 7
      Stack size: 7
      Flags: 3
      Names: ('psutil', 'net_if_addrs', 'net_if_stats', 'isup', 'family', 'socket', 'AF_INET', 'address', 'startswith', 'ipaddress', 'IPv4Network', 'netmask', 'str', 'Exception', 'items')
      Var names: ('all_interfaces', 'all_stats', 'priority_interfaces', 'iface_name', 'addr', 'net', 'addrs')
      Constants: ['Lightning-fast network discovery', "('Wi-Fi', 'Ethernet', 'wlan0', 'eth0', 'en0', 'ens...", '127.', '/', False, ('strict',), None]
  Nested code object 167:
    Code Object Analysis:
      Name: _scan_devices_parallel
      Filename: tools.py
      First line: 3169
      Arg count: 2
      Local count: 14
      Stack size: 6
      Flags: 3
      Names: ('nmap', 'PortScanner', 'scan', 'all_hosts', 'len', 'concurrent', 'futures', 'ThreadPoolExecutor', 'get', 'hostname', 'submit', '_get_mac_vendor_fast', 'append', 'result', 'Exception', '_save_mac_cache', 'print')
      Var names: ('target_network', 'progress_callback', 'nm', 'scan_args', 'clients', 'hosts', 'executor', 'futures', 'host', 'mac', 'hostname', 'client_data', 'future', 'e')
      Constants: ['Parallel device discovery for maximum speed', '-sn -T5 --min-parallelism 100 --max-parallelism 25...', '⚡ Scanning network at maximum speed...', ('hosts', 'arguments'), '🔍 Found ', ' devices, getting vendor info...', 20, ('max_workers',), 'addresses', 'mac', 'N/A', ('ip', 'mac', 'hostname'), 1, ('timeout',), 'manufacturer', 'Unknown', None, 'Error in ultra-fast scan: ']
  Nested code object 168:
    Code Object Analysis:
      Name: _interactive_scan_workflow
      Filename: tools.py
      First line: 3223
      Arg count: 0
      Local count: 27
      Stack size: 17
      Flags: 3
      Names: ('_load_mac_cache', 'str', 'List', 'bool', 'print', 'split', 'strip', 'items', 'startswith', 'time', 'nmap', 'PortScanner', 'scan', 'all_hosts', 'strftime', 'hostname', 'state', 'upper', 'get', 'all_protocols', 'sorted', 'keys', 'append', 'len', '_mac_cache', 'Exception')
      Var names: ('_create_selection_ui', 'device_options', 'selected_device_str', 'target_ip', 'scan_options', 'selected_scan_str', 'scan_args_map', 'scan_name', 'scan_args', 'key', 'args', 'start_time', 'nm', 'scan_duration', 'host_data', 'report', 'best_os', 'open_ports', 'total_ports', 'proto', 'port', 'service', 'port_info', 'service_detail', 'risk_ports_found', 'ports_per_second', 'e')
      Constants: ['\n    Ultra-fast interactive scanning workflow - sa...', False, 'title', 'prompt', 'options', 'show_progress', 'return', '<code object _create_selection_ui at 0x000001EFF4B...', '🚀 [Thread] Step 1: Ultra-fast device discovery...', 'NETWORK DEVICE SCANNER', '🔍 Discovering devices at maximum speed...', True, ('show_progress',), 'cancel', '❌ Scan cancelled by user.', '│', 0, '   [Thread] Step 1 Complete: User selected ', '   [Thread] Step 2: Scan type selection...', "('⚡ LIGHTNING SCAN      │ Top 100 ports    │ ~5 se...", 'SCAN TYPE SELECTOR', '⚡ Choose scan intensity for ', ':', '-T5 --top-ports 100 --max-parallelism 200 --min-pa...', '-T5 --top-ports 1000 --max-parallelism 150', '-T4 -F --max-parallelism 100', '-T4 -sV --version-intensity 5 --max-parallelism 50', '-T4 -A -sV -O --max-parallelism 30', '-T4 -p 21,22,23,25,53,80,110,135,139,443,445,993,9...', '-sS -T2 -f --scan-delay 1s', "('⚡ LIGHTNING SCAN', '🚀 TURBO SCAN', '🔍 QUICK SCAN...", None, '-T4 -sV', "   [Thread] Step 2 Complete: User selected '", "'", "   [Thread] Step 3: Executing '", "' on ", '...', ('arguments',), '❌ Could not get scan results for ', '\n╔════════════════════════════════════════════════...', '\n⚡ SCAN: ', '\n⏱️  DURATION: ', '.2f', ' seconds\n📅 TIME: ', '%Y-%m-%d %H:%M:%S', '\n\n📋 HOST INFORMATION:\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━...', 'N/A', '\n• Status: ', '\n• MAC: ', 'addresses', 'mac', '\n', 'osmatch', '• OS: ', 'name', ' (', 'accuracy', '% confidence)\n', '\n🔍 PORT ANALYSIS:\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━...', 1, 'state', 'open', 'unknown', 'product', '', 'version', ('port', 'service', 'product', 'version'), '🟢 OPEN PORTS (', ' found):\n', ' ', ')', '   ├─ ', 'port', '<6', ' │ ', 'service', '<15', '🟢 OPEN PORTS: None detected\n', (21, 23, 135, 139, 445, 1433, 3389), '<code object <listcomp> at 0x000001EFF69E5830, fil...', '\n🛡️  SECURITY ASSESSMENT:\n━━━━━━━━━━━━━━━━━━━━━━━━...', 'HIGH', 'MEDIUM', 'LOW', '\n• Open Services: ', '\n• High-Risk Ports: ', '• ⚠️  Critical: Ports ', ' require immediate attention\n', '\n📊 PERFORMANCE METRICS:\n━━━━━━━━━━━━━━━━━━━━━━━━━━...', '.1f', ' ports/second\n• Total Ports: ', '\n• Efficiency: ULTRA-FAST ⚡\n• Cache Hits: ', ' MAC vendors cached\n\n╚════════════════════════════...', '   [Thread] Step 3 Complete: Ultra-fast report gen...', 's', '❌ Critical error in ultra-fast scanner: ', (False,)]
      Nested code object 7:
        Code Object Analysis:
          Name: _create_selection_ui
          Filename: tools.py
          First line: 3230
          Arg count: 4
          Local count: 20
          Stack size: 13
          Flags: 19
          Names: ('str', 'tk', 'Tk', 'title', 'attributes', 'geometry', 'configure', 'update_idletasks', 'winfo_width', 'winfo_height', 'winfo_screenwidth', 'winfo_screenheight', 'format', 'Frame', 'pack', 'Label', 'ttk', 'Progressbar', 'start', 'Scrollbar', 'Listbox', 'set', 'SINGLE', 'config', 'yview', 'bind', 'insert', 'END', 'Button', 'threading', 'Thread', 'after', 'mainloop')
          Var names: ('title', 'prompt', 'options', 'show_progress', 'on_cancel', 'on_double_click', 'width', 'height', 'x', 'y', 'main_frame', 'title_label', 'prompt_label', 'list_frame', 'scrollbar', 'option', 'button_frame', 'select_btn', 'cancel_btn', 'populate_devices_thread')
          Constants: [None, '', '<code object process_ui_queue at 0x000001EFF3F3FD7...', 'message', '<code object update_progress at 0x000001EFF69DD130...', '<code object on_select at 0x000001EFF6991A30, file...', '<code object on_cancel at 0x000001EFF69F84F0, file...', '<code object on_double_click at 0x000001EFF69C2BF0...', '⚡ ', '-topmost', True, '650x550', '#1a1a1a', ('bg',), 2, '{}x{}+{}+{}', 20, ('padx', 'pady', 'bg'), 'both', ('fill', 'expand'), ('Consolas', 16, 'bold'), '#00ff41', ('text', 'font', 'fg', 'bg'), (0, 15), ('pady',), 600, 'left', ('Consolas', 11), '#ffffff', "('text', 'wraplength', 'justify', 'font', 'fg', 'b...", 'indeterminate', ('mode',), 'x', (0, 10), ('fill', 'pady'), '🚀 Initializing ultra-fast scanner...', ('Consolas', 10), '#00ffff', 'vertical', ('orient',), 18, '#0d1117', '#1f6feb', 0, "('yscrollcommand', 'selectmode', 'height', 'font',...", ('command',), 'right', 'y', ('side', 'fill'), ('side', 'fill', 'expand'), '<Double-Button-1>', (20, 0), '⚡ SELECT & CONTINUE', ('Consolas', 11, 'bold'), '#238636', 'flat', 25, 10, 'hand2', "('text', 'command', 'font', 'bg', 'fg', 'relief', ...", 8, ('side', 'expand', 'padx'), '❌ CANCEL', '#da3633', '<code object populate_devices_thread at 0x000001EF...', '<code object update_device_list at 0x000001EFF4B61...', ('target', 'daemon'), 100]
          Nested code object 2:
            Code Object Analysis:
              Name: process_ui_queue
              Filename: tools.py
              First line: 3236
              Arg count: 0
              Local count: 3
              Stack size: 6
              Flags: 19
              Names: ('_ui_queue', 'get_nowait', 'queue', 'Empty', 'after')
              Var names: ('callback', 'args', 'kwargs')
              Constants: ['Process UI updates from the queue in the main thre...', True, 100, None]
          Nested code object 4:
            Code Object Analysis:
              Name: update_progress
              Filename: tools.py
              First line: 3248
              Arg count: 1
              Local count: 1
              Stack size: 5
              Flags: 19
              Names: ('_ui_queue', 'put')
              Var names: ('message',)
              Constants: ['Thread-safe way to update progress label.', '<code object <lambda> at 0x000001EFF69BE100, file ...', None]
              Nested code object 1:
                Code Object Analysis:
                  Name: <lambda>
                  Filename: tools.py
                  First line: 3250
                  Arg count: 1
                  Local count: 1
                  Stack size: 3
                  Flags: 19
                  Names: ('config',)
                  Var names: ('msg',)
                  Constants: [None, ('text',)]
          Nested code object 5:
            Code Object Analysis:
              Name: on_select
              Filename: tools.py
              First line: 3252
              Arg count: 0
              Local count: 1
              Stack size: 4
              Flags: 19
              Names: ('curselection', 'IndexError', 'quit', 'destroy')
              Var names: ('selection',)
              Constants: [None, 0, '']
          Nested code object 6:
            Code Object Analysis:
              Name: on_cancel
              Filename: tools.py
              First line: 3267
              Arg count: 0
              Local count: 0
              Stack size: 2
              Flags: 19
              Names: ('quit', 'destroy')
              Var names: ()
              Constants: [None, 'cancel']
          Nested code object 7:
            Code Object Analysis:
              Name: on_double_click
              Filename: tools.py
              First line: 3274
              Arg count: 1
              Local count: 1
              Stack size: 2
              Flags: 19
              Names: ()
              Var names: ('event',)
              Constants: [None]
          Nested code object 64:
            Code Object Analysis:
              Name: populate_devices_thread
              Filename: tools.py
              First line: 3352
              Arg count: 0
              Local count: 3
              Stack size: 8
              Flags: 19
              Names: ('_discover_network_ultra_fast', '_scan_devices_parallel', '_ui_queue', 'put', 'Exception', 'str')
              Var names: ('target_network', 'devices', 'e')
              Constants: ['This function runs in a separate thread.', '❌ No active network found', '❌ Error: ', None]
          Nested code object 65:
            Code Object Analysis:
              Name: update_device_list
              Filename: tools.py
              First line: 3364
              Arg count: 1
              Local count: 3
              Stack size: 7
              Flags: 19
              Names: ('delete', 'END', 'clear', 'append', 'insert', 'stop', 'destroy', 'config', 'len')
              Var names: ('devices', 'device', 'option')
              Constants: ['This function runs in the main thread.', 0, 'ip', '<16', ' │ ', 'hostname', '<25', 'manufacturer', '✅ Found ', ' devices - Double-click to select instantly!', ('text',), '❌ No devices found on network', None]
      Nested code object 80:
        Code Object Analysis:
          Name: <listcomp>
          Filename: tools.py
          First line: 3527
          Arg count: 1
          Local count: 2
          Stack size: 4
          Flags: 19
          Names: ()
          Var names: ('.0', 'p')
          Constants: ['port']
  Nested code object 169:
    Code Object Analysis:
      Name: advanced_network_scan
      Filename: tools.py
      First line: 3560
      Arg count: 0
      Local count: 2
      Stack size: 4
      Flags: 131
      Names: ('print', 'asyncio', 'get_event_loop', 'run_in_executor', '_interactive_scan_workflow')
      Var names: ('loop', 'final_report')
      Constants: ['\n    Performs an ultra-fast, interactive network s...', '🚀 Agent dispatching ultra-fast interactive network...', None, '✅ Ultra-fast scanner completed successfully!']


EXTRACTED STRINGS:
------------------------------
  1: '🪟 Listing active windows'
  3: 'cancel'
  4: 'up'
  5: '\n   • Color Scheme: '
  6: 'bmh'
  7: '\n    Ultra-fast interactive scanning workflow - same structure as original but 10x faster\n    '
  8: 'indeterminate'
  9: '%\n📈 RAM Usage: '
 10: 'nova_memory/chat_history.db'
 11: 'current_weather'
 12: 'Extract date from text'
 13: '\n                <div class="insight-card">\n                    <div class="insight-icon">✨</div>\n                    <div class="insight-content">'
 14: 'data'
 15: '\nRows × Columns: '
 17: '🟢 OPEN PORTS: None detected\n'
 18: '__main__'
 19: '#00ff41'
 20: '#ffffff'
 21: '.txt'
 22: 'mean'
 23: 'hostname'
 24: '\n   • Location: '
 25: 'Variable 2'
 26: ' successfully created in Notepad\n📄 File saved as: '
 27: '</td></tr>'
 28: '--'
 29: 'Advanced styling configuration'
 30: 'analysis_timestamp'
 31: '\n   • Columns: '
 32: '.'
 34: 'target_network'
 35: '...'
 36: 'notepad'
 37: 'df'
 38: 'restart'
 39: '\n    Plays media content from YouTube.\n    \n    Args:\n        media_name: Name of song/video\n        media_type: Content type (default: "song")\n        \n    Behavior:\n        - Uses YouTube Data API if key available\n        - Falls back to browser search\n        \n    Returns:\n        str: Currently playing confirmation or search link\n\n    '
 40: 'numeric_columns'
 41: ' DPI\n   • Filename: '
 42: '🎵 Playing media: '
 43: 'boolean_columns'
 44: '\n    Clicks on screen text using OCR.\n    \n    Args:\n        target_text: Visible text to click\n        \n    Returns:\n        str: Click confirmation or error\n        \n    Technology:\n        - Uses Tesseract OCR\n        - Fuzzy text matching\n    '
 45: 'both'
 46: "' separator"
 47: "' on "
 48: '<table class="stats-table"><tr><th>Metric</th><th>Maximum</th><th>Minimum</th><th>Average</th><th>Median</th></tr>'
 49: '650x550'
 50: '\n    Loads an Excel file, analyzes its contents using optimized parallel execution, \n    and caches the results including business context, data quality, and insights.\n    '
 51: '\\d{4}-\\d{2}-\\d{2}'
 52: 'to_email'
 53: 'Windows'
 54: '\n    Analyzes camera feed based on prompt.\n    \n    Args:\n        prompt: Analysis request (e.g., "count objects")\n        \n    Returns:\n        str: Analysis results\n        \n    Workflow:\n        1. Temporarily enables camera\n        2. Processes frames\n        3. Returns findings\n    '
 55: 'Advanced input dialog with validation'
 56: 'id_columns'
 57: '\n    Creates formatted documents in Notepad.\n    \n    Args:\n        title: Document heading\n        content: Main text\n        document_type: Format template:\n            - "letter": Formal layout\n            - "application": Structured\n            - "note": Simple text\n            \n    Returns:\n        str: Saved file path confirmation\n    '
 58: '\n    Performs multi-source web search with fallback logic.\n    \n    Args:\n        query: Search terms\n        \n    Workflow:\n        1. Attempts Wikipedia summary\n        2. Tries DuckDuckGo API\n        3. Falls back to DuckDuckGo search\n        \n    Returns:\n        str: First 2 sentences from Wikipedia or top search result\n        \n    Notes:\n        - Results limited to 500 characters\n    '
 59: 'strong'
 60: '\n• MAC: '
 61: 'मौसम त्रुटि: '
 62: "' विंडो बंद कर दी गई।"
 63: ' की ओर '
 64: 'RelatedTopics'
 65: 'business_context'
 66: 'width'
 67: 'quality'
 68: '</strong> actionable business insights identified</li>\n                <li><strong>'
 69: '💾 Graph saved: '
 70: 'Save MAC vendor cache to file'
 71: '🔔 याद दिलाना: '
 72: '❌ ईमेल credentials नहीं मिले। कृपया .env फाइल चेक करें।'
 73: 'Normal (μ='
 74: 'show_progress'
 75: 'https://www.googleapis.com/youtube/v3/search?part=snippet&q='
 76: 'enter'
 77: 'open'
 79: 'strong positive'
 80: '</strong> ↔ \n                            <strong>'
 81: '✅ Ultra-fast scanner completed successfully!'
 82: 'Strong '
 83: '\n\n📋 HOST INFORMATION:\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n• Hostname: '
 84: 'Ultra-fast business insight generation'
 85: 'bold'
 86: 'ignore'
 87: "Unsupported export format. Use either 'json' or 'html'."
 88: 'Data loading failed: '
 89: 'plain'
 90: '\n⚡ SCAN: '
 91: '✍️ Writing document content...'
 92: 'ip'
 93: 'manufacturer'
 94: '❌ त्रुटि: '
 95: 'contact'
 96: 'Data quality requires attention: '
 97: 'format'
 98: 'Std: '
 99: '\n    Performs quick virus scan using Windows Defender.\n    \n    Returns:\n        str: Scan summary with:\n            - Threats found\n            - Scan duration\n            - Last update\n            \n    Notes:\n        - Requires admin privileges\n    '
100: 'x_column'
101: '\n\n🔍 DATA ANALYSIS:\n   • Numeric Columns: '
102: 'left'
103: '\n    ⚡ QUICK ADVANCED GRAPH CREATOR - Fast One-Click Solution\n    \n    Similar to create_advanced_graph() but optimized for speed\n    with minimal user interaction and smart defaults.\n    \n    Args:\n        graph_type (str): Type of graph to create\n        x_column (str): X-axis column name\n        y_column (str): Y-axis column name\n        data_range (str): Range of data to use\n        data_limit (int): Limit number of rows\n        title (str): Graph title\n        save_name (str): Save filename\n        style (str): Graph style\n        color_scheme (str): Color scheme\n        interactive_mode (bool): Interactive mode\n        show_stats (bool): Show statistics\n        export_format (str): Export format\n        dpi (int): DPI for export\n        \n    Returns:\n        str: Quick status report with essential graph details\n    '
104: '\n   • DateTime Columns: '
105: '\n   • Total Rows: '
106: '⚡ SELECT & CONTINUE'
107: '\n• Open Services: '
108: 'center'
109: 'LOW'
110: 'Error loading file: '
111: 'विशिष्ट या सक्रिय विंडो की स्थिति प्रबंधित करें (बड़ा करें, छोटा करें, पुनर्स्थापित करें)'
112: 'https://www.youtube.com/watch?v='
113: '×'
114: 'display_name'
115: '\n🧠 CPU Usage: '
116: 'Ultra-fast column type detection'
117: 'graph_type'
118: "' को "
119: '\n    Provides comprehensive system diagnostics.\n    \n    Returns:\n        str: Formatted report containing:\n            - Battery status\n            - Storage space\n            - Network info\n            - CPU/RAM usage\n            \n    Metrics:\n        - Updates in real-time\n    '
120: 'mac_vendor_cache.json'
121: 'minimize'
122: 'Scan starting'
123: 'Advanced graph creation with all types'
124: '% confidence)\n'
125: '\n🔍 PORT ANALYSIS:\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n'
126: '\n🎯 Document type: '
127: '\n\n📋 Available options:\n'
128: '\n📊 PERFORMANCE METRICS:\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n• Scan Speed: '
129: 'port'
130: 'round'
131: 'scatter'
132: ' devices, getting vendor info...'
133: 'disable'
134: 'weak'
135: 'file://'
136: 'data_range'
137: 'Unknown'
138: ')\n💾 Storage: '
139: 'utf-8'
140: 'analysis_results_'
141: ' requires numeric Y-axis column'
142: '   • DateTime columns: '
143: 'shutdown now'
144: 'black'
145: '</div>'
146: '-T5 --top-ports 100 --max-parallelism 200 --min-parallelism 50'
147: "❌ Column '"
148: '🔔 Reminder: '
149: 'Scan completed'
150: '-T4 -sV --version-intensity 5 --max-parallelism 50'
151: '❌ स्कैन में त्रुटि: '
152: "' नाम की कोई विंडो नहीं मिली"
153: 'याद दिला'
154: 'Reset all analysis data'
156: 'Ultra-fast business context detection'
157: 'darwin'
158: '\n    Retrieves top business insights derived from the dataset analysis.\n    '
159: 'f'
160: '❌ वेब खोज में त्रुटि: '
161: '</div>\n                <div class="metric-label">Data Attributes</div>\n            </div>\n            <div class="metric-card">\n                <div class="metric-value">'
162: 'auto'
163: 'cc_email'
164: '⚠️ DuckDuckGo API failed: '
165: '\n                    <div class="correlation-item">\n                        <div style="flex: 1;">\n                            <strong>'
166: 'Marketing metrics show campaign effectiveness and engagement patterns.'
167: '❌ File dialog error: '
168: '% range from mean, indicating potential opportunities for optimization.'
169: '</p></body></html>'
170: '\n    Generates and saves visual charts based on the analyzed dataset and\n    detected business context.\n    '
171: 'direction'
172: '❌ Graph creation failed: '
173: 'अज्ञात विंडो'
174: 'product'
175: ' किया गया'
176: '❌ अमान्य CC ईमेल: '
177: '🌤️ Getting weather for: '
178: '-T4 -A -sV -O --max-parallelism 30'
179: '-T5 --top-ports 1000 --max-parallelism 150'
180: 'Agg'
181: '📨 WhatsApp भेजने की प्रक्रिया शुरू: '
182: '❌ No file selected - Process cancelled'
183: 'numeric'
184: ' MAC vendors cached\n\n╚══════════════════════════════════════════════════════════════════════════════╝\n'
185: 'identifier'
186: '🔍 Data Analysis:'
187: 'SCAN TYPE SELECTOR'
188: 'lat'
189: 'c'
190: 'all'
191: '</strong>\n                        </div>\n                        <div>\n                            <span class="correlation-strength '
192: 'state'
193: ' GB\n📶 Network: '
194: '🖥️ Desktop control: '
195: '">'
196: 'date|time|month|year'
197: '⚡ Scanning network at maximum speed...'
198: '• OS: '
199: 'मौसम की जानकारी प्राप्त करने में असमर्थ: '
200: '\n    🚀 ADVANCED GRAPH CREATOR - Complete Solution in One Method\n    \n    Features:\n    - Smart file dialog with multiple format support\n    - 8 different graph types with professional styling\n    - Interactive column selection\n    - Advanced data preprocessing\n    - Statistical analysis overlay\n    - Multiple export formats\n    - Cross-platform compatibility\n    \n    Args:\n        file_path (str): Path to data file (None = opens file dialog)\n        graph_type (str): "line", "bar", "scatter", "histogram", "pie", "box", "heatmap", "area"\n        x_column (str): X-axis column (None = auto-detect/ask user)\n        y_column (str): Y-axis column (None = auto-detect/ask user)\n        data_range (str): "full", "head", "tail", "custom"\n        data_limit (int): Number of rows for head/tail/custom\n        title (str): Graph title (None = auto-generate)\n        save_name (str): Save filename (None = auto-generate)\n        style (str): "default", "seaborn", "ggplot", "bmh", "classic", "dark"\n        figsize (tuple): Figure size (width, height)\n        color_scheme (str): "auto", "viridis", "plasma", "coolwarm", "tab10", "pastel"\n        interactive_mode (bool): Enable GUI dialogs for user input\n        show_stats (bool): Show statistical information on graph\n        export_format (str): "png", "jpg", "pdf", "svg"\n        dpi (int): Resolution for export (300 = high quality)\n        \n    Returns:\n        str: Detailed status message with graph information\n    '
201: '-suspend'
202: 'whatsapp'
203: 'city'
204: 'query'
205: 'app_name'
206: '❌ सिस्टम जानकारी प्राप्त करने में त्रुटि: '
207: '<h3 class="section-title" style="margin-top: 40px;"><span>🔍</span> Business Intelligence</h3>'
208: '</div>\n                </div>'
209: 'Yours sincerely,'
210: "✅ विंडो '"
211: '. '
212: '%Y-%m-%d'
213: ' करने में विफल: '
214: '</strong></td><td>'
215: 'HR data analysis shows employee performance and organizational metrics.'
216: '-sS -T2 -f --scan-delay 1s'
217: 'https://nominatim.openstreetmap.org/search?q='
218: '🔧 Opening Notepad...'
219: "' खोल दिया गया है।"
220: '\n                </div>\n                <div class="meta-item">\n                    <span>📊</span> '
221: 'No file selected for analysis.'
222: '   ├─ '
223: 'bottom'
224: '❌ संदेश भेजने में त्रुटि: '
225: '% ('
226: 'smtp.gmail.com'
227: '❌ File is empty or contains no valid data'
228: 'No dataset available for visualization. Please analyze data first.'
229: 'head'
230: '✅ Columns selected - X: '
231: 'Subject'
232: '\nCategorical Columns: '
233: 'Key Metrics Overview'
234: 'videoId'
235: '⚠️ DuckDuckGo search tool failed: '
236: '   [Thread] Step 2: Scan type selection...'
237: 'id|_id$'
238: 'bar'
239: '\n    Manages the currently active application window.\n    \n    Args:\n        action: Window operation:\n            - "close": Terminates window\n            - "minimize": Minimizes to taskbar\n            - "maximize": Expands window\n            \n    Returns:\n        str: Window title with action confirmation\n        \n    Notes:\n        - Uses pygetwindow for cross-platform support\n\n    '
240: "❌ '"
241: 's'
242: 'Maximized'
243: 'data_limit'
244: 'chat_messages'
245: '<6'
246: ')'
247: 'Sales analysis reveals performance patterns across different metrics.'
248: 'Error'
249: 'Connected (IP: '
250: '%d-%m-%Y'
251: '</strong> key performance metrics with detailed statistical analysis</li>\n                <li><strong>'
252: '\nData Quality: '
253: '❌ मीडिया चलाने में समस्या आई: '
254: 'version'
255: 'Error generating HTML report: '
256: '</div>\n                <div class="metric-label">Potential Outliers</div>\n            </div>\n            <div class="metric-card">\n                <div class="metric-value">'
257: 'document_type'
258: 'box'
259: ' (type: '
260: 'column2'
261: 'content'
262: 'मौसम सेवा अस्थायी रूप से अनुपलब्ध है। कृपया बाद में प्रयास करें।'
263: 'enable'
264: '\nInsights Generated: '
265: 'media_name'
266: '.2f'
267: ' - '
268: 'a'
269: 'Advanced Graph Creator'
270: 'png'
271: 'interactive_mode'
272: 'This function runs in a separate thread.'
273: 'Date: '
274: ', '
275: 'Darwin'
276: 'SELECT role, content FROM '
277: 'Ultra-fast visualization creation'
278: "\n    Exports the analysis results in the specified format.\n    Supported formats: 'json', 'html'\n    "
279: '----------------------------------------'
280: '<Double-Button-1>'
281: 'min_value'
282: ' for '
283: 'Advanced data analysis and column classification'
284: 'fast'
285: 'analysis_results'
286: 'lon'
287: 'Ultra-fast MAC vendor lookup with caching'
288: 'service'
289: '🚀 Advanced Graph Creator Starting...'
290: 'msg'
291: 'sudo shutdown -r now'
292: 'Weak'
293: 'Generated Visualizations:\n'
294: 'No visualizations were generated for this dataset.'
295: '📊 Graph Creator Input'
296: ', σ='
297: '-T4 -F --max-parallelism 100'
298: '✅ DuckDuckGo API result found'
299: 'Count: '
300: 'results'
301: 'ईमेल एड्रेस को वैलिडेट करें'
302: 'correlations'
303: '⚠️ Wikipedia failed: '
304: 'datetime'
305: '✅ सफलतापूर्वक '
306: 'histogram'
307: '- '
308: '🔍 Select Your Data File'
309: '<EMAIL>'
310: 'x'
311: 'Analysis results exported to '
312: '❌ कोई विंडो नहीं मिली'
313: '📁 Opening advanced file dialog...'
314: '&current_weather=true'
315: '</td><td>'
316: '<25'
317: '🔍 संबंधित:\n'
318: 'window_title'
319: 'classic'
320: '❌ Error writing to Notepad: '
321: '\n• Status: '
322: 'reboot'
323: 'max_value'
324: '❌ विंडो डिटेक्शन विफल: '
325: 'Advanced file dialog with multiple format support'
326: 'show_stats'
327: '❌ कोई सक्रिय विंडो नहीं मिली।'
328: '🔒 स्क्रीन लॉक की गई है।'
329: 'आज'
330: '</strong> dataset reveals valuable insights from <strong>'
331: 'potential_date'
332: 'datetime_columns'
333: ' का वर्तमान तापमान है '
334: '✅ DuckDuckGo related topics found'
335: '\nKey Metrics Identified: '
336: 'Variable 1'
337: 'Moderate'
338: '\n   • Y-axis: '
339: '-T4 -sV'
340: "   [Thread] Step 2 Complete: User selected '"
341: '</span>\n                            <div style="margin-top: 8px; font-weight: 600; font-size: 1.1rem;">r = '
342: 'File not found: '
343: '{}x{}+{}+{}'
344: '</div>\n                <div class="metric-label">Data Inconsistencies</div>\n            </div>\n        </div>\n    </div>\n    \n    <div class="section">\n        <h2 class="section-title"><span>📋</span> Executive Summary</h2>\n        <div class="executive-summary">\n            <p style="font-size: 1.2rem; margin-bottom: 25px; line-height: 1.8;">This comprehensive analysis of the <strong class="glow">'
345: 'line'
346: '❌ डेस्कटॉप कंट्रोल में त्रुटि: '
347: '\n    Enable or disable camera feed analysis. When enabled, the assistant will process video frames.\n    \n    Args:\n        enable: True to enable camera analysis, False to disable\n        assistant_instance: The assistant instance to control\n        \n    Returns:\n        str: Confirmation message\n    '
348: '&longitude='
349: '❌ विंडो '
350: 'TF_CPP_MIN_LOG_LEVEL'
351: 'Analysis completed.\nDataset: '
352: 'Subject: '
353: 'Select column for '
354: 'dark_background'
355: 'remember'
356: ' columns'
357: 'सिस्टम शटडाउन किया जा रहा है।'
358: 'loginctl'
359: '/'
360: 'Error in ultra-fast scan: '
361: 'tail'
362: ' columns\nDomain: '
363: '"\n🧠 क्या कुछ और भेजना है sir? जवाब दें — Nova उस संदेश को भेज देगा।'
364: '+'
365: 'coolwarm'
366: '। सप्ताह का दिन है '
367: '🔍 Searching web for: '
368: '❌ Error while checking reminders: '
369: 'h'
370: 'Enabled'
371: 'moderate'
372: ': '
373: '🚫 Error: '
374: '\n    Performs an ultra-fast, interactive network security audit with advanced features.\n    Same structure as original but with massive speed improvements and modern UI.\n    '
375: 'Failed to '
376: '\n\n💾 EXPORT DETAILS:\n   • Format: '
377: '\n📝 Content written with proper formatting\n🔄 New file created for clean writing experience'
378: '🔍 Checking reminders for '
379: ' shows high variability with a '
380: 'shrink'
381: '%A'
382: 'mixed_columns'
383: '│'
384: 'python'
385: 'win'
386: '<!DOCTYPE html>\n<html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>Advanced Data Analysis Report</title>\n<style>\n:root {\n    --primary: #6366f1;\n    --primary-dark: #4f46e5;\n    --secondary: #8b5cf6;\n    --accent: #ec4899;\n    --success: #22c55e;\n    --warning: #f59e0b;\n    --danger: #ef4444;\n    --info: #06b6d4;\n    --dark: #0f172a;\n    --darker: #020617;\n    --gray: #94a3b8;\n    --light: #f8fafc;\n    --border: #1e293b;\n    --card-bg: rgba(30, 41, 59, 0.6);\n}\n* {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n}\nbody {\n    font-family: \'Inter\', \'Segoe UI\', system-ui, sans-serif;\n    background: linear-gradient(135deg, var(--darker), var(--dark));\n    color: var(--light);\n    line-height: 1.6;\n    padding: 20px;\n    min-height: 100vh;\n}\n.report-container {\n    max-width: 1200px;\n    margin: 20px auto;\n    background: rgba(15, 23, 42, 0.8);\n    border-radius: 24px;\n    overflow: hidden;\n    box-shadow: 0 25px 50px -12px rgba(0,0,0,0.25);\n    backdrop-filter: blur(12px);\n    border: 1px solid rgba(99, 102, 241, 0.2);\n}\n.report-header {\n    background: linear-gradient(135deg, var(--primary), var(--secondary));\n    color: white;\n    padding: 50px 40px;\n    position: relative;\n    text-align: center;\n    overflow: hidden;\n}\n.report-header::before {\n    content: "";\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 4px;\n    background: linear-gradient(90deg, var(--success), var(--info), var(--warning), var(--accent));\n}\n.header-content {\n    max-width: 800px;\n    margin: 0 auto;\n    position: relative;\n    z-index: 2;\n}\n.report-title {\n    font-size: 3rem;\n    font-weight: 800;\n    margin-bottom: 15px;\n    letter-spacing: -0.5px;\n    text-shadow: 0 2px 10px rgba(0,0,0,0.2);\n}\n.report-subtitle {\n    font-size: 1.2rem;\n    opacity: 0.9;\n    margin-bottom: 25px;\n    font-weight: 400;\n}\n.report-meta {\n    display: flex;\n    justify-content: center;\n    gap: 30px;\n    margin-top: 25px;\n    flex-wrap: wrap;\n}\n.meta-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    background: rgba(255, 255, 255, 0.15);\n    padding: 12px 24px;\n    border-radius: 50px;\n    backdrop-filter: blur(4px);\n    border: 1px solid rgba(255,255,255,0.1);\n}\n.section {\n    padding: 45px;\n    border-bottom: 1px solid var(--border);\n}\n.section:last-child {\n    border-bottom: none;\n}\n.section-title {\n    font-size: 2rem;\n    font-weight: 700;\n    color: var(--primary);\n    margin-bottom: 35px;\n    display: flex;\n    align-items: center;\n    gap: 15px;\n}\n.section-title::after {\n    content: "";\n    flex: 1;\n    height: 2px;\n    background: linear-gradient(90deg, var(--primary), transparent);\n    margin-left: 20px;\n    opacity: 0.3;\n}\n.metric-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n    gap: 30px;\n    margin: 35px 0;\n}\n.metric-card {\n    background: var(--card-bg);\n    border-radius: 16px;\n    padding: 30px;\n    text-align: center;\n    border: 1px solid rgba(99, 102, 241, 0.2);\n    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n    position: relative;\n    overflow: hidden;\n}\n.metric-card:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 15px 30px rgba(0,0,0,0.25);\n    border-color: rgba(99, 102, 241, 0.4);\n}\n.metric-card::before {\n    content: "";\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 4px;\n    background: linear-gradient(90deg, var(--primary), var(--accent));\n}\n.metric-value {\n    font-size: 2.8rem;\n    font-weight: 800;\n    margin: 20px 0;\n    background: linear-gradient(135deg, var(--primary), var(--accent));\n    -webkit-background-clip: text;\n    background-clip: text;\n    color: transparent;\n    text-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n.metric-label {\n    color: var(--gray);\n    font-size: 1.1rem;\n    font-weight: 500;\n}\n.insight-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n    gap: 30px;\n    margin-top: 30px;\n}\n.insight-card {\n    background: var(--card-bg);\n    border-radius: 16px;\n    padding: 30px;\n    border-left: 4px solid var(--primary);\n    box-shadow: 0 8px 20px rgba(0,0,0,0.15);\n    transition: all 0.4s ease;\n    position: relative;\n    overflow: hidden;\n}\n.insight-card:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 12px 30px rgba(0,0,0,0.25);\n}\n.insight-card::before {\n    content: "";\n    position: absolute;\n    top: 0;\n    right: 0;\n    width: 60px;\n    height: 60px;\n    background: linear-gradient(45deg, var(--primary), transparent 70%);\n    border-radius: 0 16px 0 100%;\n}\n.insight-content {\n    font-size: 1.15rem;\n    line-height: 1.7;\n    position: relative;\n    z-index: 2;\n}\n.insight-icon {\n    font-size: 2rem;\n    margin-bottom: 20px;\n    color: var(--primary);\n    position: relative;\n    z-index: 2;\n}\n.correlation-item {\n    display: flex;\n    align-items: center;\n    padding: 25px;\n    background: var(--card-bg);\n    border-radius: 16px;\n    margin: 20px 0;\n    border-left: 4px solid var(--info);\n    transition: all 0.3s ease;\n}\n.correlation-item:hover {\n    transform: translateX(5px);\n    border-left-width: 6px;\n}\n.stats-table {\n    width: 100%;\n    border-collapse: separate;\n    border-spacing: 0;\n    margin: 35px 0;\n    overflow: hidden;\n    border-radius: 16px;\n    background: var(--card-bg);\n    box-shadow: 0 8px 25px rgba(0,0,0,0.15);\n}\n.stats-table th {\n    background: linear-gradient(135deg, var(--primary), var(--primary-dark));\n    color: white;\n    padding: 20px 25px;\n    text-align: left;\n    font-weight: 600;\n    font-size: 1.1rem;\n}\n.stats-table td {\n    padding: 18px 25px;\n    border-bottom: 1px solid var(--border);\n    color: var(--light);\n}\n.stats-table tr:last-child td {\n    border-bottom: none;\n}\n.stats-table tr:nth-child(even) {\n    background-color: rgba(30, 41, 59, 0.4);\n}\n.stats-table tr:hover td {\n    background-color: rgba(30, 41, 59, 0.8);\n}\n.executive-summary {\n    background: linear-gradient(135deg, rgba(30, 41, 59, 0.7), rgba(15, 23, 42, 0.8));\n    border-radius: 20px;\n    padding: 40px;\n    margin-top: 30px;\n    border: 1px solid rgba(99, 102, 241, 0.2);\n    position: relative;\n    overflow: hidden;\n}\n.executive-summary::before {\n    content: "";\n    position: absolute;\n    top: -50%;\n    right: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);\n}\n.summary-points {\n    margin: 25px 0 0 30px;\n}\n.summary-points li {\n    margin-bottom: 20px;\n    position: relative;\n    padding-left: 35px;\n    font-size: 1.1rem;\n    line-height: 1.8;\n}\n.summary-points li::before {\n    content: "•";\n    color: var(--primary);\n    font-size: 2rem;\n    position: absolute;\n    left: 0;\n    top: -5px;\n}\n.report-footer {\n    text-align: center;\n    padding: 40px;\n    background: rgba(2, 6, 23, 0.7);\n    color: rgba(255,255,255,0.7);\n    position: relative;\n}\n.report-footer::before {\n    content: "";\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 2px;\n    background: linear-gradient(90deg, transparent, var(--primary), transparent);\n}\n.footer-note {\n    margin-top: 15px;\n    font-size: 0.95rem;\n    opacity: 0.7;\n}\n.correlation-strength {\n    display: inline-block;\n    padding: 6px 15px;\n    border-radius: 50px;\n    font-size: 0.9rem;\n    font-weight: 600;\n    margin-left: 10px;\n}\n.strong {\n    background: rgba(34, 197, 94, 0.15);\n    color: var(--success);\n    border: 1px solid rgba(34, 197, 94, 0.3);\n}\n.moderate {\n    background: rgba(245, 158, 11, 0.15);\n    color: var(--warning);\n    border: 1px solid rgba(245, 158, 11, 0.3);\n}\n.weak {\n    background: rgba(239, 68, 68, 0.15);\n    color: var(--danger);\n    border: 1px solid rgba(239, 68, 68, 0.3);\n}\n.glow {\n    text-shadow: 0 0 15px rgba(99, 102, 241, 0.5);\n}\n@keyframes fadeIn {\n    from { opacity: 0; transform: translateY(20px); }\n    to { opacity: 1; transform: translateY(0); }\n}\n.section, .report-header, .report-footer {\n    animation: fadeIn 0.6s ease forwards;\n}\n@media (max-width: 768px) {\n    .section {\n        padding: 35px 25px;\n    }\n    .metric-grid {\n        grid-template-columns: 1fr;\n    }\n    .report-header {\n        padding: 40px 25px;\n    }\n    .report-title {\n        font-size: 2.4rem;\n    }\n}\n</style>\n<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">\n</head>\n<body>\n<div class="report-container">\n    <div class="report-header">\n        <div class="header-content">\n            <h1 class="report-title glow">Advanced Data Insights Report</h1>\n            <p class="report-subtitle">Dark Theme Edition • Comprehensive Analytics</p>\n            \n            <div class="report-meta">\n                <div class="meta-item">\n                    <span>📅</span> '
387: 'name'
388: '</strong> significant data relationships discovered</li>\n                <li>Overall data quality score of <strong>'
389: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
390: '❌ No devices found on network'
391: '%I:%M %p'
392: '\n🔋 Battery: '
393: '🔧 Power action: '
394: '🔎 परिणाम:\n'
395: 'categorical'
396: 'This function runs in the main thread.'
397: '%B %d, %Y at %I:%M %p'
398: '📋 खुली हुई विंडोज:\n'
399: ' suggests potential business relationship.'
400: '/System/Library/CoreServices/Menu Extras/User.menu/Contents/Resources/CGSession'
401: '#da3633'
402: 'windspeed'
403: 'Process UI updates from the queue in the main thread.'
404: '%\nGenerated Insights: '
405: 'Top Business Insights\n--------------------------------------------------\n'
406: '❌ '
407: '❌ ऐप खोलने में त्रुटि: '
408: '🧠 सर, आज आपको याद है न — '
409: 'remind'
410: 'export_format'
411: 'RdYlBu_r'
412: 'ctrl'
413: '</div>\n                        </div>\n                    </div>'
414: 'categorical_columns'
415: ' seconds\n📅 TIME: '
416: 'Inventory analysis indicates stock levels and product performance.'
417: 'm'
418: 'options'
419: '\n    Asks user to select an Excel file, performs full analysis, creates visualizations and generates report.\n    Executes the complete end-to-end analysis including report generation.\n    Suitable for comprehensive data assessment and export.\n    '
420: '❌ Critical error in ultra-fast scanner: '
421: '❌ स्क्रॉल करने में विफल: '
422: 'The selected file could not be loaded or is empty.'
423: '</div>\n                <div class="metric-label">Duplicate Records</div>\n            </div>\n            <div class="metric-card">\n                <div class="metric-value">'
424: 'Select Data File'
425: 'flat'
426: 'figsize'
427: 'metadata'
428: '\n\n    Fetches current weather conditions for a specified city in Hindi/English.\n    \n    Args:\n        city (str): The city name to get weather for (e.g., "Delhi")\n        \n    Returns:\n        str: Formatted weather string with temperature and wind speed\n        \n    Behavior:\n        1. First tries Open-Meteo geocoding API\n        2. Falls back to OpenStreetMap if needed\n        3. Returns temperature (°C) and wind speed (km/h)\n        \n    Example:\n        "Delhi का वर्तमान तापमान है 32°C और पवन की गति है 12 km/h।"\n    \n    '
429: 'Median: '
430: 'Parallel device discovery for maximum speed'
431: '%</div>\n                <div class="metric-label">Quality Score</div>\n            </div>\n            <div class="metric-card">\n                <div class="metric-value">'
432: '⚠️ स्कैन पूरा हुआ, लेकिन कोई जानकारी नहीं मिली:\n\n'
433: '&type=video&key='
434: 'temperature'
435: '🧠 System Info for: '
436: '✅ Document created successfully!'
437: " encoding, '"
438: "' not found in data"
439: '✅ Data loaded successfully: '
440: '❌ क्षमा करें, अभी कोई उपयोगी जानकारी नहीं मिली।'
441: '_id'
442: ' │ '
443: '❌ Input dialog error: '
444: 'visualizations'
445: '\n    Types content into active window.\n    \n    Args:\n        message: Text to type\n        \n    Returns:\n        str: Typing confirmation\n        \n    Behavior:\n        - Natural typing speed (0.1s intervals)\n        - Preserves original cursor position\n\n    '
446: 'user'
447: 'Dataset Summary\n'
448: 'r--'
449: '~'
450: '✅ Wikipedia result found'
451: '\n🎉 GRAPH CREATION SUCCESSFUL!\n\n📊 GRAPH DETAILS:\n   • Type: '
452: 'show'
453: 'N/A'
454: '%d/%m/%Y'
455: '🔍 Discovering devices at maximum speed...'
456: ', Y: '
457: '\n   • Style: '
458: 'style'
459: "'"
460: 'text1'
461: "✅ '"
462: '❌ Quick graph creation failed: '
463: '</div>\n    \n    <div class="section">\n        <h2 class="section-title"><span>✅</span> Data Quality Assessment</h2>\n        <div class="metric-grid">\n            <div class="metric-card">\n                <div class="metric-value">'
464: '^Unnamed'
465: 'close'
466: 'text2'
467: 'Ultra-fast data quality analysis'
468: '%Y-%m-%d %H:%M:%S'
469: 'MEDIUM'
470: '\n    Sends emails via authenticated Gmail SMTP.\n    \n    Args:\n        to_email: Primary recipient\n        subject: Email subject\n        message: Body content\n        cc_email: CC recipient (optional)\n        \n    Validation:\n        - Strict email format validation\n        - Requires GMAIL_USER/GMAIL_PASSWORD in .env\n        \n    Returns:\n        str: Delivery confirmation or error\n    '
471: '✅ ईमेल सफलतापूर्वक भेजा गया: '
472: ' ('
473: '\\'
474: 'Report generated and opened successfully at: '
475: 'snippet'
476: 'id'
477: ':'
478: ' vs '
479: '\n   • Categorical Columns: '
480: ' devices - Double-click to select instantly!'
481: 'मीडिया त्रुटि: '
482: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$'
483: 'strong negative'
484: ' × '
485: 'Assistant instance not set'
486: 'Smart data loading with multiple encoding support'
487: 'median'
488: '\n   • Size: '
489: 'maximize'
490: "' "
491: 'mac_address'
492: 'Pie chart requires categorical column'
493: '   • Numeric columns: '
494: 'AbstractText'
495: 'default'
496: 'osmatch'
497: '\n\n📈 AXIS CONFIGURATION:\n   • X-axis: '
498: 'From'
499: 'tight'
500: 'heatmap'
501: 'amount'
502: '🔍 Found '
503: '-topmost'
504: 'none'
505: '❌ अमान्य प्राप्तकर्ता ईमेल: '
506: 'inconsistencies'
507: 'key'
508: 'top'
509: '.1f'
510: ' rows'
511: 'zisn kydb qysb yifr'
512: ' यूनिट स्क्रॉल किया।'
513: '\n    Launches applications via Start Menu search.\n    \n    Args:\n        app_name: Application name (e.g., "chrome")\n        \n    Returns:\n        str: Launch confirmation or error\n        \n    Notes:\n        - Windows-specific implementation\n    '
514: '🔋 On Battery'
515: '✅ DuckDuckGo search tool result found'
516: 'seaborn-v0_8-darkgrid'
517: 'professional_data_report_dark.html'
518: ','
519: '%</strong> with recommendations for improvement</li>\n                <li>Advanced statistical analysis of key metrics and performance indicators</li>\n            </ul>\n        </div>\n    </div>\n    \n    <div class="report-footer">\n        <div style="font-size: 1.2rem; font-weight: 600;">Advanced Analytics Report | Confidential</div>\n        <div class="footer-note">Generated by Data Insights Engine v3.0 • Dark Theme</div>\n    </div>\n</div>\n</body>\n</html>'
520: '\nBusiness Domain: '
521: 'y'
522: ' graph with advanced styling...'
523: '\n    Lists all visible application windows.\n    \n    Returns:\n        str: Formatted list with:\n            - Window titles\n            - Current state (minimized/maximized/active)\n            \n    Example:\n        "• Chrome (Maximized)\n• Notepad (Active)"\n    '
524: 'n'
525: 'The '
526: ' records × '
527: '</table>'
528: 'No data loaded. Please perform analysis first.'
529: 'coerce'
530: '🟢 OPEN PORTS ('
531: '✅ Weather result: '
532: 'key_metrics'
533: 'object'
534: 'xdg-open "'
535: '❌ Not Connected'
536: 'Strong'
537: '<html><body><h1>Report Generation Error</h1><p>'
538: '❌ File not found: '
539: '<16'
540: ' ORDER BY created_at ASC'
541: '<h3 class="section-title" style="margin-top: 40px;"><span>🔗</span> Key Relationships</h3>'
542: 'quality_score'
543: '\n    Generates and opens a detailed HTML report summarizing the analysis results,\n    including business insights, data quality, and detected context.\n    '
544: 'vertical'
545: 'outliers'
546: ' columns\nDetected Domain: '
547: '💾 Saving document...'
548: 'domain'
549: '[Your Name]'
550: 'shutdown'
551: 'pie'
552: '⚠️ Error parsing content: '
553: 'AIzaSyBW5YkF21ntit_zIMGE5TpTKlBpBJl3mfM'
554: '🚀 ऐप खोलने का प्रयास: '
555: '.csv'
556: 'xlrd'
557: 'ईमेल त्रुटि: '
558: "YouTube पर '"
559: 'save_name'
560: 'return'
561: 'items'
562: '❌ कुंजी दबाने में त्रुटि: '
563: '⚡ Charging'
564: 'Error during analysis: '
565: '   [Thread] Step 1 Complete: User selected '
566: 'media_type'
567: ' attributes\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class="section">\n        <h2 class="section-title"><span>📈</span> Dataset Overview</h2>\n        <div class="metric-grid">\n            <div class="metric-card">\n                <div class="metric-value">'
568: 'Visualization error: '
569: '\n   • Resolution: '
570: 'date|2020|2021|2022|2023|2024|2025'
571: "   [Thread] Step 3: Executing '"
572: '✅ Analysis data reset successfully.'
573: 'सिस्टम रीस्टार्ट किया जा रहा है।'
574: '🎵 अब बज रहा है: '
575: 'Add statistical information to graph'
576: 'Visual analysis failed: '
577: '"'
578: 'C:\\Program Files\\Tesseract-OCR\\tesseract.exe'
579: '\nLast Run: '
580: 'song'
581: 'wheat'
582: 'Correlation Matrix'
583: '📊 Using first '
584: '🪟 Managing window state: '
585: 'https://geocoding-api.open-meteo.com/v1/search?name='
586: 'Mean: '
587: 'https://api.duckduckgo.com/'
588: 'prompt'
589: 'integer'
590: '#1f6feb'
591: '-sn -T5 --min-parallelism 100 --max-parallelism 256'
592: 'विंडो प्रबंधन विफल: '
593: '\n   • Used Rows: '
594: 'open "'
595: 'color_scheme'
596: '❌ ईमेल भेजने में त्रुटि: '
597: 'विंडो सूची त्रुटि: '
598: "Get today's reminders from the database"
599: ' km/h।'
600: '</div>\n                <div class="metric-label">Key Metrics</div>\n            </div>\n        </div>\n    </div>\n    \n    <div class="section">\n        <h2 class="section-title"><span>📊</span> Key Performance Insights</h2>'
601: 'Ultra-fast data loading with optimizations'
602: 'No business insights available. Please run analysis first.'
603: 'json'
604: 'Linux'
605: '⚡ Starting Quick Advanced Graph Creation...'
606: 'message'
607: '❌ डेस्कटॉप दिखाने में विफल: '
608: '⚡ Choose scan intensity for '
609: '\n⏱️  DURATION: '
610: ' ports/second\n• Total Ports: '
611: 'कल'
612: 'dpi'
613: ' and '
614: '❌ CANCEL'
615: "' विंडो छोटी की गई।"
616: '\n    Summarizes the dataset including structure, quality metrics, and key features.\n    '
617: ' GB of '
618: '\n                </div>\n                <div class="meta-item">\n                    <span>🏢</span> Domain: '
619: '%\nMissing Values: '
620: '❌ कोई सक्रिय विंडो नहीं मिली'
621: 'duplicate_rows'
622: '• '
623: "' पर क्लिक किया गया!"
624: '• ⚠️  Critical: Ports '
625: 'title'
626: 'business_insights'
627: 'No results available to export. Please run the analysis first.'
628: 'time_columns'
629: '\n    Creates audible/visual reminders.\n    \n    Args:\n        msg: Reminder content\n        \n    Returns:\n        str: Formatted reminder with bell icon\n        \n    Example:\n        "🔔 याद दिलाना: Meeting at 3 PM"\n    '
630: 'hand2'
631: 'text'
632: 'Select X-axis column:'
633: '✅ Found '
634: 'correlation'
635: 'subject'
636: '✅ File selected: '
637: '🎨 Creating '
638: ' करने में समस्या आई: '
639: ' requires numeric columns'
640: 'sudo shutdown -h now'
641: '<tr><td><strong>'
642: 'string'
643: '🖥️ डेस्कटॉप दिखाया जा रहा है।'
644: '</strong> attributes. Key findings include:</p>\n            \n            <ul class="summary-points">\n                <li><strong>'
645: '.json'
646: 'action'
647: 'height'
648: ' found):\n'
649: 'Select Y-axis column:'
650: 'top_performers'
651: '%\nNumeric Columns: '
652: '🚀 [Thread] Step 1: Ultra-fast device discovery...'
653: 'boxes'
654: 'right'
655: '🎯 Column selection process...'
656: ' -> '
657: 'https://www.youtube.com/results?search_query='
658: '-T4 -p 21,22,23,25,53,80,110,135,139,443,445,993,995,1433,3389,5432,8080,8443'
659: 'scroll'
660: 'खोज त्रुटि: '
661: 'आज की तारीख है '
662: 'mac'
663: 'error_report.html'
664: '\n   • Statistics: '
665: 'compute.use_bottleneck'
666: ' GB free of '
667: '#00ffff'
668: ' rows × '
669: 'delete'
670: 'Ultra-fast insight extraction'
671: '📧 Sending email to: '
672: '\nDimensions: '
673: '🔔 '
674: '🚀 Initializing ultra-fast scanner...'
675: "' दबा दिया गया है।"
676: 'addresses'
677: '\n🛡️  SECURITY ASSESSMENT:\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n• Risk Level: '
678: 'Error in file dialog: '
679: 'longitude'
680: '\n    Sends WhatsApp messages via desktop automation.\n    \n    Args:\n        contact: Name/number from contacts\n        message: Content to send\n        \n    Workflow:\n        1. Opens WhatsApp\n        2. Searches contact\n        3. Sends message\n        \n    Returns:\n        str: Delivery confirmation and follow-up prompt\n    '
681: 'target_text'
682: 'Financial analysis reveals cost structures and budget allocations.'
683: 'r'
684: 'Data summary not available. Please run analysis first.'
685: 'Heatmap requires numeric columns'
686: '\n• Efficiency: ULTRA-FAST ⚡\n• Cache Hits: '
687: 'r-'
688: 'column1'
689: '📄 Creating new file...'
690: 'insights'
691: 'custom'
692: 'पावर एक्शन विफल: '
693: 'Analysis Status\n'
694: 'Disabled'
695: '📊 Using random sample of '
696: 'compute.use_numexpr'
697: 'openpyxl'
698: 'full'
699: '\n    Controls system power state across Windows/Linux/MacOS.\n    \n    Args:\n        action: Power action to perform:\n            - "shutdown": Powers off system\n            - "restart": Reboots system\n            - "lock": Locks workstation\n            \n    Returns:\n        str: Action confirmation in Hindi/English\n        \n    Security:\n        - Requires admin privileges for shutdown/restart\n    '
700: '<15'
701: 'Cc'
702: 'missing_percentage'
703: '\nData Quality Score: '
704: 'Downloads'
705: '~/Downloads'
706: '\n• High-Risk Ports: '
707: 'HIGH'
708: '   • Categorical columns: '
709: '2'
710: '   [Thread] Step 3 Complete: Ultra-fast report generated in '
711: 'd'
712: '% missing values detected.'
713: 'area'
714: 'skyblue'
715: 'lock-session'
716: 'ggplot'
717: '\n\n📁 DATA INFORMATION:\n   • File: '
718: ' camera analysis: '
719: 'w'
720: '.3f'
721: '✅ टाइप कर दिया गया: "'
722: 'svg'
723: 'file_path'
724: ',.2f'
725: 'shutdown /s /t 1'
726: 'empty'
727: '\n    Controls desktop UI elements.\n    \n    Args:\n        action: "show" desktop or "scroll"\n        direction: Scroll direction (required if action=scroll)\n        amount: Scroll units (default: 3)\n        \n    Returns:\n        str: Action confirmation\n        \n    Notes:\n        - Restores mouse position after operation\n    '
728: '#238636'
729: 'unknown domain'
730: '📚 विकिपीडिया:\n'
731: "' खोल रहा हूँ..."
732: 'down'
733: 'Load MAC vendor cache from file for instant lookups'
734: 'NETWORK DEVICE SCANNER'
735: '\n╔══════════════════════════════════════════════════════════════════════════════╗\n║                    ⚡ ULTRA-FAST NETWORK SECURITY AUDIT ⚡                   ║\n╚══════════════════════════════════════════════════════════════════════════════╝\n\n🎯 TARGET: '
736: 'o'
737: "' विंडो बड़ी की गई।"
738: 'https://api.open-meteo.com/v1/forecast?latitude='
739: '📝 Starting Notepad writing process: '
740: 'latitude'
741: 'key_metrics_overview.png'
742: 'Thread-safe way to update progress label.'
743: '❌ No active network found'
744: 'क्षमा करें, मैं स्थान नहीं ढूंढ पाया: '
745: 'Minimized'
746: 'Dear Sir/Madam,'
747: '\nLast Analyzed: '
748: '⚡ '
749: 'lock'
750: '_'
751: 'html'
752: '। अभी का समय है '
753: 'No file'
754: '</div>\n                <div class="metric-label">Total Records</div>\n            </div>\n            <div class="metric-card">\n                <div class="metric-value">'
755: 'email'
756: '°C और पवन की गति है '
757: 'No data available. Please run load_and_analyze_excel() first.'
758: '\n    Provides current datetime information in Hindi.\n    \n    Returns:\n        str: Formatted string containing:\n            - Date (DD-MM-YYYY)\n            - Time (12-hour format)\n            - Day of week\n            \n    Example:\n        "आज की तारीख है 19-07-2023। अभी का समय है 03:45 PM। सप्ताह का दिन है Wednesday।"\n    '
759: 'Active'
760: "' नहीं मिला"
761: 'pdf'
762: '127.'
763: '⚠️ Sir, message खाली है।'
764: '📊 Loading data with smart detection...'
765: ') between '
766: 'accuracy'
767: 'white'
768: 'Unsupported file format: '
769: 'Lightning-fast network discovery'
770: 'shutdown /r /t 1'
771: 'To'
772: '🛡️ सिस्टम स्कैन पूरा हुआ:\n\n'
773: '🦆 DuckDuckGo:\n'
774: ' GB)'
775: '❌ Could not get scan results for '
776: '%</div>\n                <div class="metric-label">Missing Values</div>\n            </div>\n            <div class="metric-card">\n                <div class="metric-value">'
777: '1'
778: '✅ Data loaded: '
779: 'Text'
780: 'format_type'
781: 'y_column'
782: '<div class="insight-grid">'
783: ' correlation ('
784: 'restore'
785: '.xlsx'
786: '</strong> records across <strong>'
787: '❌ Scan cancelled by user.'
788: '📊 Using last '
789: 'Ultra-fast HTML report generation with enhanced dark theme design'
790: 'strength'
791: '❌ Error: '
792: 'Thank you for your time and consideration.'
793: '\n    Returns the current status of the analysis, including data shape, domain,\n    quality score, number of insights, and timestamp.\n    '
794: '%1.1f%%'
795: 'active window'
796: '#0d1117'
797: ' require immediate attention\n'
798: '।'
799: 'Open file dialog to select Excel/CSV file'
800: '\nQuality Score: '
801: '%Y%m%d_%H%M%S'
802: '\n    Simulates keyboard key presses.\n    \n    Args:\n        key: Single key ("enter") or combo ("ctrl+alt+del")\n        \n    Returns:\n        str: Press confirmation\n        \n    Notes:\n        - Supports most standard keyboard keys\n    '
803: 'unknown'
804: ' Distribution'
805: '#1a1a1a'
806: '\n\n✏️ Enter your choice:'
807: '🚀 Agent dispatching ultra-fast interactive network scanner...'
808: '&format=json'
809: '\' को संदेश भेजा गया: "'
810: 'Report generation failed: '
811: '%\nKey Metrics: '
812: '🪟 Window action: '
813: 'correlation_heatmap.png'
814: ' Analysis: '
815: 'letter'


EXTRACTED NAMES/IDENTIFIERS:
------------------------------
  1: kwargs
  2: send_email
  3: data
  4: Listbox
  5: server
  6: nmap
  7: enumerate
  8: service_detail
  9: total_cells
 10: hostname
 11: mean
 12: scan_options
 13: linspace
 14: list
 15: sample
 16: read_excel
 17: np
 18: target_network
 19: concurrent
 20: gather
 21: on_double_click
 22: tight_layout
 23: risk_ports_found
 24: df
 25: port_info
 26: tkinter
 27: Optional
 28: encoding
 29: StandardScaler
 30: type_user_message_auto
 31: all_protocols
 32: net_if_stats
 33: db_operation
 34: domain_keywords
 35: color
 36: device_options
 37: mp
 38: load_and_analyze_excel
 39: free_gb
 40: float
 41: group
 42: to_email
 43: get_event_loop
 44: all_hosts
 45: norm
 46: network_status
 47: target_ip
 48: is_bool_dtype
 49: selected_device_str
 50: priority_interfaces
 51: start
 52: get_x
 53: cvtColor
 54: quality
 55: business_context
 56: iface_name
 57: reminders
 58: width
 59: center_y
 60: callback
 61: open
 62: show_progress
 63: __name__
 64: set_facecolor
 65: assistant_instance
 66: cat
 67: host_data
 68: paragraph
 69: recipients
 70: attach
 71: all_interfaces
 72: io
 73: contact
 74: create_quick_advanced_graph
 75: format
 76: start_time
 77: x_column
 78: askinteger
 79: image
 80: to_thread
 81: battery_percent
 82: median_val
 83: data_to_plot
 84: abspath
 85: screen_height
 86: error
 87: filterwarnings
 88: _create_visualizations_fast
 89: power_plugged
 90: session
 91: _generate_html_report_fast
 92: split
 93: Exception
 94: graph_type
 95: minimize
 96: netmask
 97: get_time_info
 98: get_nowait
 99: write_in_notepad
100: patch
101: addr
102: threading
103: port
104: gethostname
105: round
106: scatter
107: dotenv
108: role
109: k
110: data_range
111: bool
112: root
113: set_alpha
114: total_ports
115: ValueError
116: mpatches
117: array
118: encodings
119: fill_between
120: ipaddress
121: values
122: curselection
123: upper
124: option
125: concurrent.futures
126: attributes
127: scan_system_for_viruses
128: mask
129: shape
130: host
131: combined
132: sample_str
133: texts
134: cv2
135: press
136: KMeans
137: ratio
138: is_maximized
139: f
140: loc
141: figure
142: cc_email
143: get_system_info
144: FAILSAFE
145: today
146: create_task
147: get_data_summary
148: isMaximized
149: direction
150: target_window
151: configure
152: system
153: stats_text
154: sct
155: _analyze_data_quality_fast
156: Frame
157: str
158: gray
159: all
160: _create_input_dialog
161: abs
162: state
163: strftime
164: set_xlabel
165: exists
166: safe_title
167: ip_address
168: get_analysis_report
169: execute
170: dropna
171: props
172: zip
173: city
174: query
175: app_name
176: get_weather
177: load
178: index
179: Button
180: analysis
181: corr_values
182: startswith
183: Scrollbar
184: sklearn.preprocessing
185: video
186: psutil
187: COLOR_RGB2BGR
188: screenshot
189: FileNotFoundError
190: nunique
191: report_result
192: bars
193: askopenfilename
194: set_fontweight
195: head
196: separators
197: position
198: used
199: sleep
200: bar
201: mu
202: scrollbar
203: TABLE_NAME
204: Tuple
205: AF_INET
206: open_app
207: transAxes
208: _detect_column_types_fast
209: data_limit
210: update_idletasks
211: list_active_windows
212: net_if_addrs
213: now
214: set_title
215: quality_results
216: Thread
217: get
218: numeric_cols
219: warnings
220: triu
221: search_web
222: document_type
223: full_save_path
224: decode
225: image_to_data
226: clear
227: content
228: ones_like
229: enable
230: tab10
231: platform
232: mean_val
233: DuckDuckGoSearchRun
234: Label
235: media_name
236: on_cancel
237: aiohttp
238: interactive_mode
239: textstr
240: ctypes
241: scan_args
242: populate_devices_thread
243: startfile
244: pytesseract
245: corr_path
246: family
247: contains
248: colors
249: hotkey
250: dialog_type
251: current
252: webbrowser
253: pathlib
254: keywords
255: total
256: service
257: sendmail
258: logging
259: moveTo
260: withdraw
261: event
262: iloc
263: msg
264: range_pct
265: futures
266: lower
267: results
268: style_configs
269: value_counts
270: Empty
271: progress_callback
272: _load_mac_cache
273: validate_email
274: strptime
275: correlations
276: Pastel1
277: re
278: Any
279: set_xticks
280: center_x
281: sensors_battery
282: clients
283: datetime
284: Path
285: subplots
286: Set2
287: df_plot
288: location_name
289: hist
290: windows
291: cm
292: x
293: savefig
294: missing_count
295: focus_force
296: GMAIL_PASSWORD
297: col_data
298: active_win
299: window_title
300: quit
301: ClientTimeout
302: update
303: virtual_memory
304: best_score
305: winfo_height
306: get_today_reminder_message_from_db
307: show_stats
308: sum
309: path
310: mac_vendor_lookup
311: i
312: save_params
313: play_media
314: type_info
315: strength_text
316: Dict
317: hosts
318: sample_size
319: z
320: weather_data
321: autotext
322: manage_window
323: PIPE
324: ttk
325: line
326: splitext
327: numeric_df
328: len
329: where
330: _find_key_insights_fast
331: switch_backend
332: quality_task
333: executor
334: ThreadPoolExecutor
335: scroll_amount
336: tail
337: set_option
338: std
339: plot
340: categories
341: unique
342: ram_percent
343: coolwarm
344: endswith
345: _add_statistics
346: ram_used_gb
347: get_height
348: insights_task
349: charging
350: candidates
351: insight
352: domain_insights
353: cancel_btn
354: Tk
355: set
356: select_dtypes
357: set_color
358: win
359: time
360: sklearn.cluster
361: best_os
362: ClientSession
363: _get_mac_vendor_fast
364: corr_value
365: address
366: config
367: html_content
368: device
369: step
370: mainloop
371: selection
372: set_ylabel
373: keys
374: _save_mac_cache
375: context
376: tuple
377: _scan_devices_parallel
378: gethostbyname
379: report
380: main_frame
381: set_visible
382: tab20
383: strength_class
384: nm
385: original_size
386: _create_selection_ui
387: vendor
388: button_frame
389: .0
390: system_name
391: screenshot_np
392: _detect_business_context_fast
393: export_format
394: bins
395: YOUTUBE_API_KEY
396: free
397: wedges
398: simpledialog
399: paragraphs
400: poly1d
401: options
402: PAUSE
403: unique_ratio
404: location
405: figsize
406: ax
407: weather_url
408: bind
409: screen_width
410: viridis
411: item_lower
412: final_report
413: langchain_community.tools
414: _cache_file
415: SMTP
416: reset_index
417: pyautogui
418: _create_file_dialog
419: visualizations
420: min_val
421: axes
422: key_metrics_count
423: winfo_screenheight
424: file_ext
425: gw
426: os
427: write
428: show
429: search_tool
430: devices
431: min
432: autotexts
433: analyze_visual_scene
434: style
435: cmd
436: export_results
437: tesseract_cmd
438: text1
439: conn
440: close
441: text2
442: nlargest
443: is_object_dtype
444: stats
445: _discover_network_ultra_fast
446: result
447: params
448: selected_scan_str
449: top_metrics
450: columns_lower
451: stderr
452: askstring
453: median
454: status
455: COLOR_BGR2GRAY
456: multiprocessing
457: maximize
458: mac_address
459: pack
460: patches
461: url
462: suffix
463: business_task
464: user32
465: asyncio
466: sigma
467: heatmap
468: amount
469: Dark2
470: api
471: Set1
472: lookup
473: downloads_path
474: manage_window_state
475: key
476: inconsistencies
477: stop
478: open_ports
479: extract_date_from_text
480: analyze_current_scene
481: getLogger
482: queue
483: max
484: sep
485: y
486: date_match
487: expanduser
488: content_json
489: List
490: n
491: email.mime.text
492: seaborn
493: error_msg
494: Series
495: key_metrics
496: options_str
497: SequenceMatcher
498: monitor
499: function_tool
500: GMAIL_USER
501: create_visualizations_chart
502: numeric_count
503: simple_html
504: corr_matrix
505: quality_score
506: END
507: outliers
508: future
509: domain
510: Progressbar
511: pie
512: load_dotenv
513: _load_data_file
514: Queue
515: pygetwindow
516: as_string
517: filedialog
518: enable_camera_analysis
519: Literal
520: save_name
521: loads
522: original_pos
523: SINGLE
524: duplicated
525: basename
526: ram_total_gb
527: reset_analysis
528: items
529: suptitle
530: choice
531: content_items
532: _create_advanced_styling
533: is_minimized
534: number
535: environ
536: media_type
537: metric_keywords
538: _analyze_data
539: dtype
540: basicConfig
541: proto
542: all_stats
543: PCA
544: cpu_percent
545: opt
546: legend
547: clean_paragraph
548: ProcessPoolExecutor
549: typing
550: isoformat
551: _generate_business_insights_fast
552: fetchall
553: prompt
554: sklearn.decomposition
555: ANALYSIS_CACHE
556: sns
557: percent
558: Output
559: bp
560: select_btn
561: keyword
562: color_scheme
563: colors_pie
564: cursor
565: join
566: search
567: json
568: list_frame
569: message
570: logger
571: winfo_width
572: similarity
573: metric
574: get_width
575: insert
576: press_key
577: any
578: read_csv
579: tolist
580: dpi
581: dump
582: size
583: isna
584: _create_graph_advanced
585: login
586: livekit.agents
587: isinstance
588: replace
589: unnamed_cols
590: duplicate_rows
591: _ui_queue
592: mss
593: title
594: business_insights
595: filename
596: send_whatsapp_message
597: loop
598: _interactive_scan_workflow
599: prompt_label
600: text
601: astype
602: full_prompt
603: e
604: chart
605: p
606: set_axisbelow
607: response
608: LockWorkStation
609: subject
610: winfo_screenwidth
611: range
612: to_numeric
613: summary
614: plt
615: sys
616: is_datetime64_any_dtype
617: GLOBAL_DF
618: win32gui
619: scan_duration
620: alt_cmd
621: sqlite3
622: action
623: height
624: after
625: pattern
626: PortScanner
627: typewrite
628: proc
629: top_performers
630: best_match
631: matplotlib.patches
632: difflib
633: PIL
634: stdout
635: disk_usage
636: yview
637: int
638: DataFrame
639: submit
640: IPv4Network
641: scroll
642: color_maps
643: messagebox
644: count
645: mac
646: numpy
647: scipy
648: score
649: MacLookup
650: battery
651: print
652: all_windows
653: types
654: resize
655: delete
656: say_reminder
657: getActiveWindow
658: missing_pct
659: strip
660: is_numeric_dtype
661: set_xticklabels
662: base64
663: target_text
664: isup
665: r
666: scan_name
667: args
668: email.mime.multipart
669: corr
670: monitors
671: send_email_sync
672: insights
673: DB_PATH
674: suitable_columns
675: destroy
676: title_label
677: max_val
678: grid
679: ram
680: requests
681: Image
682: advanced_network_scan
683: get_analysis_status
684: timedelta
685: geo_data
686: plasma
687: clf
688: socket
689: client_data
690: missing_percentage
691: matplotlib
692: scan
693: connect
694: date
695: geometry
696: net
697: sorted
698: click_on_text
699: row
700: starttls
701: IndexError
702: system_power_action
703: functools
704: pd
705: file_path
706: run
707: output
708: subprocess
709: fig
710: empty
711: metrics_path
712: create_subprocess_exec
713: correlation_matrix
714: item
715: wikipedia
716: matplotlib.pyplot
717: addrs
718: MIMEMultipart
719: j
720: col
721: smtplib
722: ports_per_second
723: MIMEText
724: boxplot
725: getAllWindows
726: isnull
727: wraps
728: _mac_cache
729: email
730: _load_data_smart
731: tk
732: append
733: pdf
734: isMinimized
735: dict
736: fit
737: timestamp
738: polyfit
739: columns
740: partial
741: use
742: max_score
743: put
744: INFO
745: report_path
746: scan_args_map
747: format_type
748: y_column
749: node
750: time_mask
751: click
752: restore
753: total_gb
754: total_count
755: windll
756: id_mask
757: _open_file_dialog
758: rows
759: current_date
760: DICT
761: run_in_executor
762: INTER_CUBIC
763: enable_visual_analysis
764: pyplot
765: shutil
766: x_vals
767: match
768: create_advanced_graph
769: full_analysis_with_report
770: pandas
771: desktop_control
772: communicate
773: get_top_insights
