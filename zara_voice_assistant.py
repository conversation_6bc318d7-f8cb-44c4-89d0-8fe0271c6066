#!/usr/bin/env python3
"""
Zara Voice Assistant
A multilingual AI voice assistant with advanced capabilities
Based on Nova architecture with enhanced features

Creator: Ratnam Sanjay
Version: 2.0.0
License: MIT
"""

import asyncio
import logging
import json
import time
import sqlite3
import uuid
from datetime import datetime
from typing import Any, List, Optional, Dict
from collections import deque
import os
from pathlib import Path

# Import the tools and prompts from Nova
try:
    import tools
    from prompts import AGENT_INSTRUCTION, SESSION_INSTRUCTION, AGENT_INSTRUCTION_FOR_TOOLS
    print("✅ Successfully imported tools and prompts from Nova")
except ImportError as e:
    print(f"⚠️ Warning: Could not import Nova modules: {e}")
    tools = None

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zara_assistant.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Database configuration
DB_PATH = "zara_chat_history.db"
CHAT_LOG_PATH = "zara_conversations.txt"

class ZaraDatabase:
    """Database manager for Zara conversations"""

    @staticmethod
    def init_db():
        """Initialize the database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                id TEXT PRIMARY KEY,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                timestamp REAL NOT NULL
            )
        """)
        conn.commit()
        conn.close()

    @staticmethod
    def store_message(role: str, content: str, message_id: str = None, timestamp: float = None):
        """Store a message in the database"""
        if timestamp is None:
            timestamp = time.time()
        if message_id is None:
            message_id = str(uuid.uuid4())

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT OR REPLACE INTO messages (id, role, content, timestamp)
            VALUES (?, ?, ?, ?)
        """, (message_id, role, content, timestamp))
        conn.commit()
        conn.close()

    @staticmethod
    def get_chat_history(limit: int = 100) -> List[Dict[str, Any]]:
        """Get chat history from database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, role, content, timestamp
            FROM messages
            ORDER BY timestamp ASC
            LIMIT ?
        """, (limit,))

        messages = []
        for row in cursor.fetchall():
            messages.append({
                'id': row[0],
                'role': row[1],
                'content': row[2],
                'timestamp': row[3]
            })
        conn.close()
        return messages

    @staticmethod
    def get_last_n_messages(n: int = 4):
        """Get last n messages from database"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT role, content, timestamp FROM messages ORDER BY id DESC LIMIT ?", (n,))
        messages = cursor.fetchall()
        conn.close()
        return list(reversed(messages))

class ZaraVoiceAssistant:
    """
    Zara Voice Assistant - Advanced AI Assistant

    Features:
    - Multilingual support (English/Hindi)
    - Voice interaction
    - System automation
    - Visual analysis
    - Task management
    - 30+ Advanced Tools from Nova
    - Conversation logging (JSON + Database)
    """

    def __init__(self):
        """Initialize Zara Voice Assistant"""
        self.name = "Zara"
        self.creator = "Ratnam Sanjay"
        self.version = "2.0.0"
        self.language = "hi"  # Default to Hindi like Nova

        # Initialize database
        ZaraDatabase.init_db()

        # Core components
        self._initialize_components()
        self._load_configuration()
        self._initialize_tools()

        # Chat logging
        self._chat_log_path = CHAT_LOG_PATH

        logger.info(f"🤖 {self.name} Voice Assistant v{self.version} initialized")
        logger.info(f"👨‍💻 Created by: {self.creator}")
        logger.info(f"🛠️ Tools available: {len(self.available_tools) if hasattr(self, 'available_tools') else 'Unknown'}")
        logger.info(f"💾 Database initialized: {DB_PATH}")
        logger.info(f"📝 Chat log: {self._chat_log_path}")
    
    def _initialize_components(self):
        """Initialize core components"""
        # Conversation management
        self.conversation_history = deque(maxlen=100)
        self.session_start_time = datetime.now()
        
        # Visual analysis
        self.visual_analysis_enabled = False
        self.frame_buffer = deque(maxlen=20)
        
        # Task management
        self.active_tasks = []
        self.completed_tasks = []
        
        # System state
        self.is_listening = False
        self.is_processing = False
        
    def _load_configuration(self):
        """Load configuration from file or environment"""
        self.config = {
            'language_preference': 'hi',  # Default to Hindi
            'voice_enabled': True,
            'visual_analysis': True,  # Enable visual analysis like Nova
            'auto_save_conversations': True,
            'max_conversation_length': 100,
            'response_timeout': 30.0,
            'tools_enabled': True,
            'user_name': 'Sir'  # Default user name
        }

        # Try to load from config file
        try:
            if os.path.exists('zara_config.json'):
                with open('zara_config.json', 'r') as f:
                    user_config = json.load(f)
                    self.config.update(user_config)
                logger.info("✅ Configuration loaded from file")
        except Exception as e:
            logger.warning(f"⚠️ Could not load config file: {e}")

    def _initialize_tools(self):
        """Initialize tools from Nova"""
        self.available_tools = {}

        if tools:
            # Get all function tools from the tools module
            tool_functions = [
                'get_weather', 'system_power_action', 'manage_window', 'desktop_control',
                'get_time_info', 'search_web', 'get_system_info', 'send_email',
                'send_whatsapp_message', 'play_media', 'write_in_notepad', 'say_reminder',
                'type_user_message_auto', 'click_on_text', 'press_key', 'scan_system_for_viruses',
                'load_and_analyze_excel', 'create_visualizations', 'enable_camera_analysis',
                'analyze_visual_scene', 'open_app'
            ]

            for func_name in tool_functions:
                if hasattr(tools, func_name):
                    self.available_tools[func_name] = getattr(tools, func_name)

            logger.info(f"🛠️ Initialized {len(self.available_tools)} tools")
        else:
            logger.warning("⚠️ Tools module not available")
    
    async def start_session(self):
        """Start a new conversation session"""
        self.session_start_time = datetime.now()
        self.conversation_history.clear()
        
        welcome_message = self._get_welcome_message()
        logger.info(f"🎯 Session started: {welcome_message}")
        
        return welcome_message
    
    def _get_welcome_message(self):
        """Get localized welcome message using Nova's session instruction"""
        user_name = self.config.get('user_name', 'Sir')

        # Use Nova's greeting style
        messages = {
            'hi': f"प्रणाली सक्रिय हो चुकी है। ज़ारा आपकी सेवा में प्रस्तुत है, {user_name}। आज का दिन कैसा रहा आपका?",
            'en': f"System activated. Zara at your service, {user_name}. How can I assist you today?"
        }
        return messages.get(self.language, messages['hi'])
    
    async def process_message(self, user_input: str) -> str:
        """Process user message and generate response"""
        if not user_input or not user_input.strip():
            return self._get_error_message("empty_input")
        
        self.is_processing = True
        start_time = time.time()
        
        try:
            # Log conversation
            self._log_conversation("user", user_input)
            
            # Detect language
            detected_lang = self._detect_language(user_input)
            if detected_lang:
                self.language = detected_lang
            
            # Process the message
            response = await self._generate_response(user_input)
            
            # Log response
            self._log_conversation("assistant", response)
            
            processing_time = time.time() - start_time
            logger.info(f"⚡ Processed in {processing_time:.2f}s")
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
            return self._get_error_message("processing_error")
        
        finally:
            self.is_processing = False
    
    def _detect_language(self, text: str) -> Optional[str]:
        """Simple language detection"""
        # Basic Hindi detection (Devanagari script)
        hindi_chars = sum(1 for char in text if '\u0900' <= char <= '\u097F')
        if hindi_chars > len(text) * 0.3:
            return 'hi'
        return 'en'
    
    async def _generate_response(self, user_input: str) -> str:
        """Generate response based on user input with tool integration"""
        user_lower = user_input.lower().strip()

        # Check for tool commands first
        if self.config.get('tools_enabled', True) and self.available_tools:
            tool_response = await self._try_execute_tool(user_input)
            if tool_response:
                return tool_response

        # Greeting responses
        if any(word in user_lower for word in ['hello', 'hi', 'hey', 'नमस्कार', 'हैलो']):
            return self._get_greeting_response()

        # Time queries
        if any(word in user_lower for word in ['time', 'समय', 'clock', 'टाइम']):
            if 'get_time_info' in self.available_tools:
                try:
                    result = await self.available_tools['get_time_info']()
                    return f"🕐 {result}"
                except Exception as e:
                    logger.error(f"Time tool error: {e}")
            return self._get_current_time()

        # Weather queries
        if any(word in user_lower for word in ['weather', 'मौसम', 'temperature']):
            return await self._handle_weather_query(user_input)

        # System queries
        if any(word in user_lower for word in ['system', 'सिस्टम', 'computer', 'pc']):
            return await self._handle_system_query(user_input)

        # Status queries
        if any(word in user_lower for word in ['status', 'how are you', 'कैसी हो']):
            return self._get_status_response()

        # Help requests
        if any(word in user_lower for word in ['help', 'सहायता', 'मदद']):
            return self._get_help_response()

        # Default response
        return self._get_default_response(user_input)
    
    def _get_greeting_response(self) -> str:
        """Get greeting response"""
        responses = {
            'en': [
                f"Hello! I'm {self.name}, ready to assist you!",
                f"Hi there! {self.name} at your service!",
                f"Greetings! How can {self.name} help you today?"
            ],
            'hi': [
                f"नमस्कार! मैं {self.name} हूँ, आपकी सेवा के लिए तैयार!",
                f"हैलो! {self.name} आपकी सेवा में!",
                f"प्रणाम! आज {self.name} आपकी कैसे मदद कर सकती है?"
            ]
        }
        import random
        return random.choice(responses.get(self.language, responses['en']))
    
    def _get_current_time(self) -> str:
        """Get current time response"""
        now = datetime.now()
        time_str = now.strftime("%I:%M %p")
        date_str = now.strftime("%B %d, %Y")
        
        if self.language == 'hi':
            return f"अभी का समय है {time_str} और आज की तारीख है {date_str}"
        else:
            return f"The current time is {time_str} and today's date is {date_str}"
    
    def _get_status_response(self) -> str:
        """Get status response"""
        uptime = datetime.now() - self.session_start_time
        uptime_str = str(uptime).split('.')[0]  # Remove microseconds
        
        if self.language == 'hi':
            return f"मैं बिल्कुल ठीक हूँ! मैं {uptime_str} से सक्रिय हूँ और आपकी सेवा के लिए तैयार हूँ।"
        else:
            return f"I'm doing great! I've been active for {uptime_str} and ready to help you."
    
    def _get_help_response(self) -> str:
        """Get help response"""
        if self.language == 'hi':
            return """मैं निम्नलिखित में आपकी सहायता कर सकती हूँ:
• समय और तारीख बताना
• सामान्य प्रश्नों के उत्तर
• बातचीत और सहायता
• सिस्टम की जानकारी

कुछ और जानना चाहते हैं? बस पूछिए!"""
        else:
            return """I can help you with:
• Time and date information
• General questions and answers
• Conversation and assistance
• System information

Want to know more? Just ask!"""
    
    def _get_default_response(self, user_input: str) -> str:
        """Get default response for unrecognized input"""
        if self.language == 'hi':
            return f"मैं समझ गई कि आपने '{user_input}' कहा है। मैं अभी भी सीख रही हूँ। क्या आप इसे दूसरे तरीके से पूछ सकते हैं?"
        else:
            return f"I understand you said '{user_input}'. I'm still learning. Could you rephrase that or ask something else?"
    
    def _get_error_message(self, error_type: str) -> str:
        """Get localized error message"""
        errors = {
            'en': {
                'empty_input': "Please provide some input for me to process.",
                'processing_error': "I encountered an error while processing your request. Please try again."
            },
            'hi': {
                'empty_input': "कृपया मुझे प्रोसेस करने के लिए कुछ इनपुट दें।",
                'processing_error': "आपके अनुरोध को प्रोसेस करते समय मुझे एक त्रुटि का सामना करना पड़ा। कृपया पुनः प्रयास करें।"
            }
        }
        return errors.get(self.language, errors['en']).get(error_type, "Unknown error")
    
    def _log_conversation(self, sender: str, message: str):
        """Log conversation to file with proper encoding and error handling."""
        try:
            # Log to database
            ZaraDatabase.store_message(sender, message)

            # Log to text file (like Nova)
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            with open(self._chat_log_path, 'a', encoding='utf-8') as f:
                f.write(f'[{timestamp}] {sender}: {message}\n')

            # Add to memory
            entry = {
                'timestamp': datetime.now().isoformat(),
                'sender': sender,
                'message': message,
                'language': self.language
            }
            self.conversation_history.append(entry)

            # Auto-save JSON if enabled
            if self.config.get('auto_save_conversations', True):
                self._save_conversation_log()

        except IOError as e:
            print(f"⚠️ Failed to log conversation: {str(e)}")
    
    def _save_conversation_log(self):
        """Save conversation to file"""
        try:
            log_file = f"zara_conversations_{datetime.now().strftime('%Y%m%d')}.json"
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.conversation_history), f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ Could not save conversation log: {e}")
    
    async def _try_execute_tool(self, user_input: str) -> Optional[str]:
        """Try to execute appropriate tool based on user input"""
        user_lower = user_input.lower().strip()

        # Weather commands
        if any(word in user_lower for word in ['weather', 'मौसम']) and 'get_weather' in self.available_tools:
            # Extract city name (simple approach)
            words = user_input.split()
            city = None
            for word in words:
                if word.lower() not in ['weather', 'मौसम', 'का', 'की', 'get', 'check']:
                    city = word
                    break

            if city:
                try:
                    result = await self.available_tools['get_weather'](city)
                    return f"🌤️ {result}"
                except Exception as e:
                    return f"मौसम सेवा में त्रुटि: {str(e)}"

        # System power commands
        if any(word in user_lower for word in ['shutdown', 'restart', 'lock', 'बंद', 'रीस्टार्ट']):
            action = 'shutdown' if any(w in user_lower for w in ['shutdown', 'बंद']) else \
                    'restart' if any(w in user_lower for w in ['restart', 'रीस्टार्ट']) else 'lock'

            if 'system_power_action' in self.available_tools:
                return f"⚠️ सिस्टम {action} करने के लिए तैयार। क्या आप पुष्टि करते हैं? (हाँ/नहीं)"

        return None

    async def _handle_weather_query(self, user_input: str) -> str:
        """Handle weather-related queries"""
        if 'get_weather' not in self.available_tools:
            return "मौसम सेवा उपलब्ध नहीं है।"

        # Extract city name
        words = user_input.split()
        city = "Delhi"  # Default city
        for word in words:
            if word.lower() not in ['weather', 'मौसम', 'का', 'की', 'get', 'check', 'बताओ']:
                city = word
                break

        try:
            result = await self.available_tools['get_weather'](city)
            return f"🌤️ {city} का मौसम: {result}"
        except Exception as e:
            return f"मौसम जानकारी प्राप्त करने में त्रुटि: {str(e)}"

    async def _handle_system_query(self, user_input: str) -> str:
        """Handle system-related queries"""
        if 'get_system_info' not in self.available_tools:
            return "सिस्टम जानकारी सेवा उपलब्ध नहीं है।"

        try:
            result = await self.available_tools['get_system_info']()
            return f"💻 सिस्टम जानकारी:\n{result}"
        except Exception as e:
            return f"सिस्टम जानकारी प्राप्त करने में त्रुटि: {str(e)}"

    def generate_startup_message(self) -> str:
        """
        Dynamically constructs startup instructions by combining the base SESSION_INSTRUCTION
        with a summary of the last conversation, then generates a context-aware greeting.
        """
        try:
            # Get last few messages from database
            last_messages = ZaraDatabase.get_last_n_messages(n=4)

            if last_messages:
                print("✅ Found recent messages. Adding conversational context to startup instructions.")

                # Build conversation summary
                summary_lines = []
                for msg in last_messages[1:]:  # Skip first message
                    content = msg[1]  # content is at index 1
                    summary_lines.append(f"- {msg[0]}: {content}")

                last_convo_summary = '\n'.join(summary_lines)

                final_instructions = SESSION_INSTRUCTION + f"""

🔰 विशेष संदर्भ:
यह हमारी पिछली बातचीत का अंतिम अंश है:
{last_convo_summary}

--------------------------"""
            else:
                print("✅ No message history found. Using standard startup instructions.")
                final_instructions = SESSION_INSTRUCTION

            # Generate contextual greeting (simplified)
            return "नमस्कार सर, मैं आपकी सेवा में प्रस्तुत हूँ।"

        except Exception as e:
            print(f"⚠️ Failed to generate startup message: {str(e)}")
            return "नमस्कार सर, मैं आपकी सेवा में प्रस्तुत हूँ।"

    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information"""
        return {
            'assistant_name': self.name,
            'creator': self.creator,
            'version': self.version,
            'session_start': self.session_start_time.isoformat(),
            'current_language': self.language,
            'conversation_count': len(self.conversation_history),
            'is_processing': self.is_processing,
            'visual_analysis_enabled': self.visual_analysis_enabled,
            'tools_available': len(self.available_tools) if hasattr(self, 'available_tools') else 0,
            'tools_enabled': self.config.get('tools_enabled', False),
            'database_path': DB_PATH,
            'chat_log_path': self._chat_log_path
        }

    def get_conversation_history_json(self) -> str:
        """Export conversation history as JSON"""
        try:
            history = ZaraDatabase.get_chat_history()
            return json.dumps(history, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to export conversation history: {e}")
            return "[]"

    def clear_conversation_history(self):
        """Clear all conversation history"""
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM messages")
            conn.commit()
            conn.close()

            # Clear memory
            self.conversation_history.clear()

            # Clear log file
            with open(self._chat_log_path, 'w', encoding='utf-8') as f:
                f.write(f"# Zara Conversation Log - Cleared on {datetime.now().isoformat()}\n")

            logger.info("✅ Conversation history cleared")
            return True
        except Exception as e:
            logger.error(f"Failed to clear conversation history: {e}")
            return False

# Example usage and testing
async def main():
    """Main function for testing Zara Voice Assistant"""
    print("🤖 Initializing Zara Voice Assistant...")
    
    # Create assistant instance
    zara = ZaraVoiceAssistant()
    
    # Start session
    welcome = await zara.start_session()
    print(f"\n{welcome}\n")
    
    # Interactive loop
    print("Commands: 'quit' to exit, 'info' for session info, 'history' for conversation history, 'clear' to clear history")
    print("-" * 80)

    while True:
        try:
            user_input = input("\n👤 You: ").strip()

            if user_input.lower() in ['quit', 'exit', 'bye']:
                print(f"\n🤖 {zara.name}: अलविदा! आपका दिन शुभ हो!")
                break

            if user_input.lower() == 'info':
                info = zara.get_session_info()
                print(f"\n📊 Session Info:")
                for key, value in info.items():
                    print(f"   {key}: {value}")
                continue

            if user_input.lower() == 'history':
                history_json = zara.get_conversation_history_json()
                print(f"\n📜 Conversation History (JSON):")
                print(history_json)
                continue

            if user_input.lower() == 'clear':
                if zara.clear_conversation_history():
                    print("\n✅ Conversation history cleared!")
                else:
                    print("\n❌ Failed to clear conversation history")
                continue

            if not user_input:
                continue

            # Process message
            response = await zara.process_message(user_input)
            print(f"\n🤖 {zara.name}: {response}")

        except KeyboardInterrupt:
            print(f"\n\n🤖 {zara.name}: अलविदा!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
