#!/usr/bin/env python3
"""
<PERSON>ara Voice Assistant
A multilingual AI voice assistant with advanced capabilities

Creator: Ratnam Sanjay
Version: 1.0.0
License: MIT
"""

import asyncio
import logging
import json
import time
from datetime import datetime
from typing import Any, List, Optional, Dict
from collections import deque
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zara_assistant.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ZaraVoiceAssistant:
    """
    <PERSON><PERSON> Voice Assistant - Advanced AI Assistant
    
    Features:
    - Multilingual support (English/Hindi)
    - Voice interaction
    - System automation
    - Visual analysis
    - Task management
    """
    
    def __init__(self):
        """Initialize Zara Voice Assistant"""
        self.name = "Zara"
        self.creator = "Ratnam Sanjay"
        self.version = "1.0.0"
        self.language = "en"  # Default language
        
        # Core components
        self._initialize_components()
        self._load_configuration()
        
        logger.info(f"🤖 {self.name} Voice Assistant v{self.version} initialized")
        logger.info(f"👨‍💻 Created by: {self.creator}")
    
    def _initialize_components(self):
        """Initialize core components"""
        # Conversation management
        self.conversation_history = deque(maxlen=100)
        self.session_start_time = datetime.now()
        
        # Visual analysis
        self.visual_analysis_enabled = False
        self.frame_buffer = deque(maxlen=20)
        
        # Task management
        self.active_tasks = []
        self.completed_tasks = []
        
        # System state
        self.is_listening = False
        self.is_processing = False
        
    def _load_configuration(self):
        """Load configuration from file or environment"""
        self.config = {
            'language_preference': 'en',
            'voice_enabled': True,
            'visual_analysis': False,
            'auto_save_conversations': True,
            'max_conversation_length': 100,
            'response_timeout': 30.0
        }
        
        # Try to load from config file
        try:
            if os.path.exists('zara_config.json'):
                with open('zara_config.json', 'r') as f:
                    user_config = json.load(f)
                    self.config.update(user_config)
                logger.info("✅ Configuration loaded from file")
        except Exception as e:
            logger.warning(f"⚠️ Could not load config file: {e}")
    
    async def start_session(self):
        """Start a new conversation session"""
        self.session_start_time = datetime.now()
        self.conversation_history.clear()
        
        welcome_message = self._get_welcome_message()
        logger.info(f"🎯 Session started: {welcome_message}")
        
        return welcome_message
    
    def _get_welcome_message(self):
        """Get localized welcome message"""
        messages = {
            'en': f"Hello! I'm {self.name}, your AI assistant created by {self.creator}. How can I help you today?",
            'hi': f"नमस्कार! मैं {self.name} हूँ, आपकी AI सहायक जो {self.creator} द्वारा बनाई गई है। आज मैं आपकी कैसे सहायता कर सकती हूँ?"
        }
        return messages.get(self.language, messages['en'])
    
    async def process_message(self, user_input: str) -> str:
        """Process user message and generate response"""
        if not user_input or not user_input.strip():
            return self._get_error_message("empty_input")
        
        self.is_processing = True
        start_time = time.time()
        
        try:
            # Log conversation
            self._log_conversation("user", user_input)
            
            # Detect language
            detected_lang = self._detect_language(user_input)
            if detected_lang:
                self.language = detected_lang
            
            # Process the message
            response = await self._generate_response(user_input)
            
            # Log response
            self._log_conversation("assistant", response)
            
            processing_time = time.time() - start_time
            logger.info(f"⚡ Processed in {processing_time:.2f}s")
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
            return self._get_error_message("processing_error")
        
        finally:
            self.is_processing = False
    
    def _detect_language(self, text: str) -> Optional[str]:
        """Simple language detection"""
        # Basic Hindi detection (Devanagari script)
        hindi_chars = sum(1 for char in text if '\u0900' <= char <= '\u097F')
        if hindi_chars > len(text) * 0.3:
            return 'hi'
        return 'en'
    
    async def _generate_response(self, user_input: str) -> str:
        """Generate response based on user input"""
        # Simple command processing
        user_lower = user_input.lower().strip()
        
        # Greeting responses
        if any(word in user_lower for word in ['hello', 'hi', 'hey', 'नमस्कार', 'हैलो']):
            return self._get_greeting_response()
        
        # Time queries
        if any(word in user_lower for word in ['time', 'समय', 'clock']):
            return self._get_current_time()
        
        # Status queries
        if any(word in user_lower for word in ['status', 'how are you', 'कैसी हो']):
            return self._get_status_response()
        
        # Help requests
        if any(word in user_lower for word in ['help', 'सहायता', 'मदद']):
            return self._get_help_response()
        
        # Default response
        return self._get_default_response(user_input)
    
    def _get_greeting_response(self) -> str:
        """Get greeting response"""
        responses = {
            'en': [
                f"Hello! I'm {self.name}, ready to assist you!",
                f"Hi there! {self.name} at your service!",
                f"Greetings! How can {self.name} help you today?"
            ],
            'hi': [
                f"नमस्कार! मैं {self.name} हूँ, आपकी सेवा के लिए तैयार!",
                f"हैलो! {self.name} आपकी सेवा में!",
                f"प्रणाम! आज {self.name} आपकी कैसे मदद कर सकती है?"
            ]
        }
        import random
        return random.choice(responses.get(self.language, responses['en']))
    
    def _get_current_time(self) -> str:
        """Get current time response"""
        now = datetime.now()
        time_str = now.strftime("%I:%M %p")
        date_str = now.strftime("%B %d, %Y")
        
        if self.language == 'hi':
            return f"अभी का समय है {time_str} और आज की तारीख है {date_str}"
        else:
            return f"The current time is {time_str} and today's date is {date_str}"
    
    def _get_status_response(self) -> str:
        """Get status response"""
        uptime = datetime.now() - self.session_start_time
        uptime_str = str(uptime).split('.')[0]  # Remove microseconds
        
        if self.language == 'hi':
            return f"मैं बिल्कुल ठीक हूँ! मैं {uptime_str} से सक्रिय हूँ और आपकी सेवा के लिए तैयार हूँ।"
        else:
            return f"I'm doing great! I've been active for {uptime_str} and ready to help you."
    
    def _get_help_response(self) -> str:
        """Get help response"""
        if self.language == 'hi':
            return """मैं निम्नलिखित में आपकी सहायता कर सकती हूँ:
• समय और तारीख बताना
• सामान्य प्रश्नों के उत्तर
• बातचीत और सहायता
• सिस्टम की जानकारी

कुछ और जानना चाहते हैं? बस पूछिए!"""
        else:
            return """I can help you with:
• Time and date information
• General questions and answers
• Conversation and assistance
• System information

Want to know more? Just ask!"""
    
    def _get_default_response(self, user_input: str) -> str:
        """Get default response for unrecognized input"""
        if self.language == 'hi':
            return f"मैं समझ गई कि आपने '{user_input}' कहा है। मैं अभी भी सीख रही हूँ। क्या आप इसे दूसरे तरीके से पूछ सकते हैं?"
        else:
            return f"I understand you said '{user_input}'. I'm still learning. Could you rephrase that or ask something else?"
    
    def _get_error_message(self, error_type: str) -> str:
        """Get localized error message"""
        errors = {
            'en': {
                'empty_input': "Please provide some input for me to process.",
                'processing_error': "I encountered an error while processing your request. Please try again."
            },
            'hi': {
                'empty_input': "कृपया मुझे प्रोसेस करने के लिए कुछ इनपुट दें।",
                'processing_error': "आपके अनुरोध को प्रोसेस करते समय मुझे एक त्रुटि का सामना करना पड़ा। कृपया पुनः प्रयास करें।"
            }
        }
        return errors.get(self.language, errors['en']).get(error_type, "Unknown error")
    
    def _log_conversation(self, sender: str, message: str):
        """Log conversation entry"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'sender': sender,
            'message': message,
            'language': self.language
        }
        self.conversation_history.append(entry)
        
        # Auto-save if enabled
        if self.config.get('auto_save_conversations', True):
            self._save_conversation_log()
    
    def _save_conversation_log(self):
        """Save conversation to file"""
        try:
            log_file = f"zara_conversations_{datetime.now().strftime('%Y%m%d')}.json"
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.conversation_history), f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ Could not save conversation log: {e}")
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information"""
        return {
            'assistant_name': self.name,
            'creator': self.creator,
            'version': self.version,
            'session_start': self.session_start_time.isoformat(),
            'current_language': self.language,
            'conversation_count': len(self.conversation_history),
            'is_processing': self.is_processing,
            'visual_analysis_enabled': self.visual_analysis_enabled
        }

# Example usage and testing
async def main():
    """Main function for testing Zara Voice Assistant"""
    print("🤖 Initializing Zara Voice Assistant...")
    
    # Create assistant instance
    zara = ZaraVoiceAssistant()
    
    # Start session
    welcome = await zara.start_session()
    print(f"\n{welcome}\n")
    
    # Interactive loop
    print("Type 'quit' to exit, 'info' for session info")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print(f"\n🤖 {zara.name}: Goodbye! Have a great day!")
                break
            
            if user_input.lower() == 'info':
                info = zara.get_session_info()
                print(f"\n📊 Session Info:")
                for key, value in info.items():
                    print(f"   {key}: {value}")
                continue
            
            if not user_input:
                continue
            
            # Process message
            response = await zara.process_message(user_input)
            print(f"\n🤖 {zara.name}: {response}")
            
        except KeyboardInterrupt:
            print(f"\n\n🤖 {zara.name}: Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
