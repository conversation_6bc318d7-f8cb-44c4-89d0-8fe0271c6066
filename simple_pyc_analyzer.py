#!/usr/bin/env python3.12
"""
Simple .pyc file analyzer for Python 3.11 bytecode
Extracts basic information and attempts simple decompilation
"""

import os
import sys
import marshal
import dis
import types
from pathlib import Path

def analyze_pyc_header(pyc_file):
    """Analyze .pyc file header"""
    with open(pyc_file, 'rb') as f:
        magic = f.read(4)
        timestamp = f.read(4)
        size_or_hash = f.read(4)
        
        # Python 3.7+ has additional fields
        try:
            extra = f.read(4)
            return magic, timestamp, size_or_hash, extra
        except:
            return magic, timestamp, size_or_hash, None

def extract_code_object(pyc_file):
    """Extract code object from .pyc file"""
    try:
        with open(pyc_file, 'rb') as f:
            # Skip header - Python 3.11 has 16 bytes header
            f.read(16)
            code_obj = marshal.load(f)
            return code_obj
    except Exception as e:
        print(f"Error extracting code object: {e}")
        return None

def analyze_code_object(code_obj, indent=0):
    """Recursively analyze code object"""
    prefix = "  " * indent
    
    print(f"{prefix}Code Object Analysis:")
    print(f"{prefix}  Name: {code_obj.co_name}")
    print(f"{prefix}  Filename: {code_obj.co_filename}")
    print(f"{prefix}  First line: {code_obj.co_firstlineno}")
    print(f"{prefix}  Arg count: {code_obj.co_argcount}")
    print(f"{prefix}  Local count: {code_obj.co_nlocals}")
    print(f"{prefix}  Stack size: {code_obj.co_stacksize}")
    print(f"{prefix}  Flags: {code_obj.co_flags}")
    
    print(f"{prefix}  Names: {code_obj.co_names}")
    print(f"{prefix}  Var names: {code_obj.co_varnames}")
    print(f"{prefix}  Constants: {[str(c)[:50] + '...' if len(str(c)) > 50 else c for c in code_obj.co_consts]}")
    
    # Look for nested code objects
    for i, const in enumerate(code_obj.co_consts):
        if isinstance(const, types.CodeType):
            print(f"{prefix}  Nested code object {i}:")
            analyze_code_object(const, indent + 2)

def extract_strings_and_names(code_obj):
    """Extract all strings and names from code object"""
    strings = []
    names = []
    
    def collect_from_code(co):
        names.extend(co.co_names)
        names.extend(co.co_varnames)
        
        for const in co.co_consts:
            if isinstance(const, str):
                strings.append(const)
            elif isinstance(const, types.CodeType):
                collect_from_code(const)
    
    collect_from_code(code_obj)
    return strings, names

def simple_disassemble(pyc_file, output_file=None):
    """Simple disassembly using dis module"""
    if output_file is None:
        output_file = pyc_file.replace('.pyc', '_simple_disasm.txt')
    
    code_obj = extract_code_object(pyc_file)
    if not code_obj:
        return False
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Simple disassembly of {pyc_file}\n")
        f.write("=" * 60 + "\n\n")
        
        # Redirect dis output
        import sys
        old_stdout = sys.stdout
        sys.stdout = f
        
        try:
            dis.dis(code_obj)
        finally:
            sys.stdout = old_stdout
    
    print(f"✅ Simple disassembly saved to: {output_file}")
    return True

def extract_readable_info(pyc_file, output_file=None):
    """Extract human-readable information"""
    if output_file is None:
        output_file = pyc_file.replace('.pyc', '_readable_info.txt')
    
    code_obj = extract_code_object(pyc_file)
    if not code_obj:
        return False
    
    strings, names = extract_strings_and_names(code_obj)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Readable Information from {pyc_file}\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("HEADER ANALYSIS:\n")
        header = analyze_pyc_header(pyc_file)
        f.write(f"Magic bytes: {header[0].hex()}\n")
        f.write(f"File size: {os.path.getsize(pyc_file)} bytes\n\n")
        
        f.write("CODE STRUCTURE:\n")
        old_stdout = sys.stdout
        sys.stdout = f
        try:
            analyze_code_object(code_obj)
        finally:
            sys.stdout = old_stdout
        
        f.write("\n\nEXTRACTED STRINGS:\n")
        f.write("-" * 30 + "\n")
        for i, string in enumerate(set(strings)):
            if len(string.strip()) > 0:
                f.write(f"{i+1:3d}: {repr(string)}\n")
        
        f.write("\n\nEXTRACTED NAMES/IDENTIFIERS:\n")
        f.write("-" * 30 + "\n")
        for i, name in enumerate(set(names)):
            if len(name.strip()) > 0:
                f.write(f"{i+1:3d}: {name}\n")
    
    print(f"✅ Readable info saved to: {output_file}")
    return True

def main():
    if len(sys.argv) < 2:
        print("Usage: python simple_pyc_analyzer.py <pyc_file>")
        return
    
    pyc_file = sys.argv[1]
    
    if not os.path.exists(pyc_file):
        print(f"❌ File not found: {pyc_file}")
        return
    
    print(f"🔍 Analyzing: {pyc_file}")
    
    # Extract readable information
    if extract_readable_info(pyc_file):
        print("✅ Readable information extracted")
    
    # Simple disassembly
    if simple_disassemble(pyc_file):
        print("✅ Simple disassembly completed")
    
    print("\n💡 Files created:")
    base_name = pyc_file.replace('.pyc', '')
    print(f"   - {base_name}_readable_info.txt")
    print(f"   - {base_name}_simple_disasm.txt")

if __name__ == "__main__":
    main()
