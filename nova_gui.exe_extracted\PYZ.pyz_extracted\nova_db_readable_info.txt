Readable Information from nova_gui.exe_extracted\PYZ.pyz_extracted\nova_db.pyc
============================================================

HEADER ANALYSIS:
Magic bytes: a70d0d0a
File size: 3658 bytes

CODE STRUCTURE:
Code Object Analysis:
  Name: <module>
  Filename: nova_db.py
  First line: 1
  Arg count: 0
  Local count: 0
  Stack size: 11
  Flags: 0
  Names: ('sqlite3', 'json', 'typing', 'List', 'Dict', 'Any', 'DB_PATH', 'init_db', 'str', 'float', 'store_message', 'int', 'get_chat_history', 'get_last_n_messages')
  Var names: ()
  Constants: [0, None, ('List', 'Dict', 'Any'), 'chat_history.db', '<code object init_db at 0x0000021F4E95FC50, file "...', 'role', 'content', 'message_id', 'timestamp', 'return', '<code object store_message at 0x0000021F4CAB4950, ...', 100, 'limit', '<code object get_chat_history at 0x0000021F4CB4B26...', 4, '<code object get_last_n_messages at 0x0000021F4E9A...', (None, None), (100,), (4,)]
  Nested code object 4:
    Code Object Analysis:
      Name: init_db
      Filename: nova_db.py
      First line: 7
      Arg count: 0
      Local count: 2
      Stack size: 3
      Flags: 3
      Names: ('sqlite3', 'connect', 'DB_PATH', 'cursor', 'execute', 'commit', 'close')
      Var names: ('conn', 'cursor')
      Constants: ['Initialize the database', '\n        CREATE TABLE IF NOT EXISTS messages (\n   ...', None]
  Nested code object 10:
    Code Object Analysis:
      Name: store_message
      Filename: nova_db.py
      First line: 22
      Arg count: 4
      Local count: 8
      Stack size: 7
      Flags: 3
      Names: ('time', 'uuid', 'str', 'uuid4', 'sqlite3', 'connect', 'DB_PATH', 'cursor', 'execute', 'commit', 'close')
      Var names: ('role', 'content', 'message_id', 'timestamp', 'time', 'uuid', 'conn', 'cursor')
      Constants: ['Store a message in the database', 0, None, '\n        INSERT OR REPLACE INTO messages (id, role...']
  Nested code object 13:
    Code Object Analysis:
      Name: get_chat_history
      Filename: nova_db.py
      First line: 42
      Arg count: 1
      Local count: 5
      Stack size: 8
      Flags: 3
      Names: ('sqlite3', 'connect', 'DB_PATH', 'cursor', 'execute', 'fetchall', 'append', 'close')
      Var names: ('limit', 'conn', 'cursor', 'messages', 'row')
      Constants: ['Get chat history from database', '\n        SELECT id, role, content, timestamp\n     ...', 0, 1, 2, 3, ('id', 'role', 'content', 'timestamp')]
  Nested code object 15:
    Code Object Analysis:
      Name: get_last_n_messages
      Filename: nova_db.py
      First line: 66
      Arg count: 1
      Local count: 4
      Stack size: 5
      Flags: 3
      Names: ('sqlite3', 'connect', 'DB_PATH', 'cursor', 'execute', 'fetchall', 'close', 'list', 'reversed')
      Var names: ('n', 'conn', 'cursor', 'messages')
      Constants: [None, 'SELECT role, content, timestamp FROM messages ORDE...']


EXTRACTED STRINGS:
------------------------------
  1: 'Store a message in the database'
  2: 'role'
  3: 'Initialize the database'
  4: 'content'
  5: 'limit'
  6: 'Get chat history from database'
  7: '\n        SELECT id, role, content, timestamp\n        FROM messages\n        ORDER BY timestamp ASC\n        LIMIT ?\n    '
  8: '\n        INSERT OR REPLACE INTO messages (id, role, content, timestamp)\n        VALUES (?, ?, ?, ?)\n    '
  9: 'return'
 10: 'message_id'
 11: '\n        CREATE TABLE IF NOT EXISTS messages (\n            id TEXT PRIMARY KEY,\n            role TEXT NOT NULL,\n            content TEXT NOT NULL,\n            timestamp REAL NOT NULL\n        )\n    '
 12: 'chat_history.db'
 13: 'timestamp'
 14: 'SELECT role, content, timestamp FROM messages ORDER BY id DESC LIMIT ?'


EXTRACTED NAMES/IDENTIFIERS:
------------------------------
  1: float
  2: get_chat_history
  3: store_message
  4: time
  5: message_id
  6: commit
  7: str
  8: init_db
  9: conn
 10: content
 11: messages
 12: Any
 13: timestamp
 14: int
 15: n
 16: close
 17: DB_PATH
 18: list
 19: sqlite3
 20: limit
 21: connect
 22: cursor
 23: get_last_n_messages
 24: typing
 25: append
 26: execute
 27: json
 28: role
 29: uuid
 30: reversed
 31: List
 32: fetchall
 33: Dict
 34: row
 35: uuid4
